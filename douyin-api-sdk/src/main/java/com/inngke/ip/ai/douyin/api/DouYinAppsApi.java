package com.inngke.ip.ai.douyin.api;

import com.github.lianjiatech.retrofit.spring.boot.annotation.Intercept;
import com.github.lianjiatech.retrofit.spring.boot.annotation.RetrofitClient;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.LogLevel;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.LogStrategy;
import com.inngke.ip.ai.douyin.dto.reqeust.Code2SessionRequest;
import com.inngke.ip.ai.douyin.dto.response.DouYinBaseResponse;
import com.inngke.ip.ai.douyin.dto.response.SessionDto;
import com.inngke.ip.ai.douyin.interceptor.RedirectInterceptor;
import retrofit2.http.Body;
import retrofit2.http.Headers;
import retrofit2.http.POST;

@RetrofitClient(
        baseUrl = "https://developer.toutiao.com/api/apps/",
        logLevel = LogLevel.INFO,
        logStrategy = LogStrategy.BODY,
        followRedirects = false)
@Intercept(handler = RedirectInterceptor.class)
public interface DouYinAppsApi {

    @POST("v2/jscode2session")
    @Headers({"Content-Type: application/json"})
    DouYinBaseResponse<SessionDto> code2Session(@Body Code2SessionRequest request);
}