package com.inngke.ip.ai.douyin.event;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 事件注册 factory
 */
@Service
public class DouYinEventManager {

    private static final Logger logger = LoggerFactory.getLogger(DouYinEventManager.class);

    @Autowired
    private List<DouYinEventHandler> douYinEventHandlerList;

    public String handler(DouYinEventDto douYinEventDto) {
        DouYinEventEnum douYinEventEnum = DouYinEventEnum.parse(douYinEventDto.getEvent());
        if (Objects.isNull(douYinEventEnum)) {
            logger.info("未实现事件{}", douYinEventDto.getEvent());
            return "success";
        }

        return getEventHandler(douYinEventEnum).handler(douYinEventDto);
    }

    public DouYinEventHandler getEventHandler(DouYinEventEnum douYinEventEnum) {
        for (DouYinEventHandler douYinEventHandler : douYinEventHandlerList) {
            if (douYinEventEnum.equals(douYinEventHandler.getEvent())) {
                return douYinEventHandler;
            }
        }

        throw new RuntimeException("获取事件处理器失败");
    }


}
