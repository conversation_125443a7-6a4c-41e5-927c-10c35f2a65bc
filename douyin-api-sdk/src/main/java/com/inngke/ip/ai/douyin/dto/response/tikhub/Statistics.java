package com.inngke.ip.ai.douyin.dto.response.tikhub;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 抖音视频统计数据
 */
@Data
public class Statistics {
    /**
     * 点赞数
     */
    @JsonProperty("admire_count")
    private Integer admireCount;

    /**
     * 视频ID
     */
    @JsonProperty("aweme_id")
    private String awemeId;

    /**
     * 收藏数
     */
    @JsonProperty("collect_count")
    private Integer collectCount;

    /**
     * 评论数
     */
    @JsonProperty("comment_count")
    private Integer commentCount;

    /**
     * 视频摘要
     */
    @JsonProperty("digest")
    private String digest;

    /**
     * 点赞数（与admire_count相同）
     */
    @JsonProperty("digg_count")
    private Integer diggCount;

    /**
     * 下载数
     */
    @JsonProperty("download_count")
    private Integer downloadCount;

    /**
     * 曝光数
     */
    @JsonProperty("exposure_count")
    private Integer exposureCount;

    /**
     * 转发数
     */
    @JsonProperty("forward_count")
    private Integer forwardCount;

    /**
     * 直播观看数
     */
    @JsonProperty("live_watch_count")
    private Integer liveWatchCount;

    /**
     * 丢失评论数
     */
    @JsonProperty("lose_comment_count")
    private Integer loseCommentCount;

    /**
     * 丢失数
     */
    @JsonProperty("lose_count")
    private Integer loseCount;

    /**
     * 播放数
     */
    @JsonProperty("play_count")
    private Integer playCount;

    /**
     * 分享数
     */
    @JsonProperty("share_count")
    private Integer shareCount;

    /**
     * WhatsApp分享数
     */
    @JsonProperty("whatsapp_share_count")
    private Integer whatsappShareCount;
} 