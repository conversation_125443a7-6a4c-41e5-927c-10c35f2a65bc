package com.inngke.ip.ai.douyin.dto.reqeust;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class DouYinGetShareIdRequest implements Serializable {
    /**
     * 如果需要知道视频分享成功的结果，need_callback设置为true
     */
    @JsonProperty("need_callback")
    private Boolean needCallback;

    /**
     * 多来源样式id（暂未开放）
     */
    @JsonProperty("source_style_id")
    private String sourceStyleId;

    /**
     * 追踪分享默认hashtag
     */
    @JsonProperty("default_hashtag")
    private String defaultHashtag;

    /**
     * 分享来源url附加参数（暂未开放）
     */
    @JsonProperty("link_param")
    private String linkParam;
}
