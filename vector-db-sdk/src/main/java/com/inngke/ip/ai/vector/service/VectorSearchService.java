package com.inngke.ip.ai.vector.service;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.inngke.ip.ai.vector.core.Filter;
import com.inngke.ip.ai.vector.core.FilterBuilder;
import com.inngke.ip.ai.vector.core.WeightConfig;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.utils.JsonUtil;
import com.inngke.ip.ai.vector.dto.MaterialCateDto;
import com.inngke.ip.ai.vector.dto.MaterialFragmentDto;
import com.inngke.ip.ai.vector.dto.MaterialInfoDto;
import com.inngke.ip.ai.vector.dto.SearchRequest;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class VectorSearchService {

    private static final Logger logger = LoggerFactory.getLogger(VectorSearchService.class);

    @Autowired
    private VectorSearchAssistantService vectorSearchAssistantService;
    @Autowired
    private MilvusClient milvusClient;
    @Autowired
    private EmbedService embedService;

    public List<MaterialInfoDto> search(SearchRequest request) {
        //参数校验
        validateParams(request);

        //填充子分类
        if (Boolean.TRUE.equals(request.getCateRecursion())) {
            if (!CollectionUtils.isEmpty(request.getAnyCategoryIds())) {
                request.setAnyCategoryIds(vectorSearchAssistantService.perfectCategoryIds(request.getAnyCategoryIds()));
            }
            if (!CollectionUtils.isEmpty(request.getAllCategoryIds())) {
                request.setAllCategoryIds(vectorSearchAssistantService.perfectCategoryIds(request.getAllCategoryIds()));
            }
        }

        List<List<MaterialFragmentDto>> videoMaterialFragmentGroup = Lists.newArrayList();

        if (StringUtils.isNotBlank(request.getKeyword())) {
            videoMaterialFragmentGroup.add(searchFragmentByKeyword(request.getKeyword(), request));
        } else if (StringUtils.isNotBlank(request.getImageUrl())) {
            videoMaterialFragmentGroup.add(searchFragmentByKeyword(embedService.evalImage(request.getImageUrl()), request));
        } else if (!CollectionUtils.isEmpty(request.getKeywords())) {
            Set<Long> existMaterialIds = Sets.newHashSet();
            for (String keyword : request.getKeywords()) {

                List<MaterialFragmentDto> materialFragmentDtos = searchFragmentByKeyword(keyword, request, existMaterialIds);

                existMaterialIds.addAll(materialFragmentDtos.stream().map(MaterialFragmentDto::getVideoId).collect(Collectors.toSet()));

                videoMaterialFragmentGroup.add(materialFragmentDtos);
            }
        }

        if (CollectionUtils.isEmpty(videoMaterialFragmentGroup.stream().flatMap(Collection::stream).filter(Objects::nonNull).collect(Collectors.toList()))) {
            logger.info("milvus向量检索结果为空: request={}", JsonUtil.toJsonString(request));
            return Lists.newArrayList();
        }

        List<Long> materialIds = videoMaterialFragmentGroup.stream().flatMap(Collection::stream)
                .map(MaterialFragmentDto::getVideoId).collect(Collectors.toList());

        //降权配置
        WeightConfig weightConfig = vectorSearchAssistantService.getWeightConfig(request.getOrganizeId());

        //素材详情
        Map<Long, MaterialInfoDto> materialMap = vectorSearchAssistantService.getMaterialInfoByIds(materialIds);

        //素材使用次数
        Map<Long, Map<Integer, Integer>> materialFragmentUseCountMap = vectorSearchAssistantService.getMaterialFragmentUseCountMap(materialIds, weightConfig.getUseCycle());

        // 排序分页返回
        return videoMaterialFragmentGroup.stream().map(videoMaterialFragmentList -> implementReduceWeight(
                request.getDuration(), videoMaterialFragmentList, materialMap, materialFragmentUseCountMap, weightConfig
        )).flatMap(Collection::stream).sorted(
                Comparator.comparing(MaterialInfoDto::getScore, Comparator.reverseOrder())
        ).skip(
                (long) (request.getPageNo() - 1) * request.getPageSize()
        ).limit(request.getPageSize()).collect(Collectors.toList());
    }

    private void validateParams(SearchRequest request) {
        if (StringUtils.isBlank(request.getKeyword()) && StringUtils.isBlank(request.getImageUrl()) && CollectionUtils.isEmpty(request.getKeywords())) {
            throw new InngkeServiceException("关键词(keyword),图片(imageUrl)不能为空");
        }
        if (Objects.isNull(request.getDuration()) || request.getDuration() <= 0) {
            throw new InngkeServiceException("视频时长(duration)必填,并且大于0");
        }
    }


    private List<MaterialFragmentDto> searchFragmentByKeyword(String keyword, SearchRequest request) {
        return searchFragmentByKeyword(embedService.evalText(keyword), request,Sets.newHashSet());
    }

    private List<MaterialFragmentDto> searchFragmentByKeyword(String keyword, SearchRequest request,Set<Long> existMaterialIds) {
        return searchFragmentByKeyword(embedService.evalText(keyword), request, existMaterialIds);
    }

    private List<MaterialFragmentDto> searchFragmentByKeyword(List<List<Double>> vector, SearchRequest request) {
        return searchFragmentByKeyword(vector, request, Sets.newHashSet());
    }

    private List<MaterialFragmentDto> searchFragmentByKeyword(List<List<Double>> vector, SearchRequest request, Set<Long> existMaterialIds) {

        //初步向量搜索
        List<MaterialFragmentDto> materialFragmentDtoList = milvusClient.doSearch(vector, FilterBuilder.toFilter(request));
        if (CollectionUtils.isEmpty(materialFragmentDtoList)){
            return Lists.newArrayList();
        }

        //取素材id
        Set<Long> materialIds = materialFragmentDtoList.stream().map(MaterialFragmentDto::getVideoId).collect(Collectors.toSet());
        materialIds.removeAll(existMaterialIds);

        if (CollectionUtils.isEmpty(materialIds)){
            return Lists.newArrayList();
        }

        //复搜素材id的所有片段
        return milvusClient.doSearch(vector, new Filter().in("video_id", materialIds).build());
    }

    private List<MaterialInfoDto> implementReduceWeight(
            Integer duration, List<MaterialFragmentDto> materialFragmentDtoList,
            Map<Long, MaterialInfoDto> materialMap, Map<Long, Map<Integer, Integer>> materialFragmentUseCountMap,
            WeightConfig weightConfig) {

        //第三步：降权
        reduceWeight(
                materialFragmentDtoList, materialMap, materialFragmentUseCountMap, weightConfig
        );

        //第四步：获取得分最高的片段
        List<MaterialInfoDto> maxVideoFragmentList = getMaxSourceVideoFragmentList(materialFragmentDtoList, duration);

        //填充素材基本信息
        List<MaterialInfoDto> videoMaterialItemList = fillVideoInfo(maxVideoFragmentList, materialMap);

        //设置分类信息
        fillCategoryInfo(videoMaterialItemList);

        //根据materialId+optimal去重，保留score最高的
        Map<String, MaterialInfoDto> dedupeMap = new HashMap<>();
        videoMaterialItemList.forEach(item -> {
            String key = item.getMaterialId() + "_" + item.getOptimal();
            MaterialInfoDto existing = dedupeMap.get(key);
            if (existing == null || item.getScore() > existing.getScore()) {
                dedupeMap.put(key, item);
            }
        });

        //按照score倒序排序
        return dedupeMap.values().stream().sorted(
                Comparator.comparing(MaterialInfoDto::getScore, Comparator.reverseOrder())
        ).collect(Collectors.toList());
    }

    private void reduceWeight(
            List<MaterialFragmentDto> materialFragmentDtoList,
            Map<Long, MaterialInfoDto> materialMap,
            Map<Long, Map<Integer, Integer>> materialFragmentUseCountMap,
            WeightConfig weightConfig) {

        for (MaterialFragmentDto materialFragmentDto : materialFragmentDtoList) {
            if (Objects.isNull(materialMap.get(materialFragmentDto.getVideoId()))) {
                continue;
            }

            double useReduceSource = getReduceScoreByUseCount(materialFragmentDto, materialFragmentUseCountMap, weightConfig.getUseCoefficient());

            double shakeReduceSource = getReduceScoreByShake(materialFragmentDto, materialMap.get(materialFragmentDto.getVideoId()), weightConfig.getShakeCoefficient());

            materialFragmentDto.setDistance(materialFragmentDto.getDistance() - useReduceSource - shakeReduceSource);
        }
    }

    private double getReduceScoreByUseCount(
            MaterialFragmentDto materialFragmentDto,
            Map<Long, Map<Integer, Integer>> materialFragmentUseCountMap,
            Double useCoefficient) {

        //每秒的使用次数Map
        Map<Integer, Integer> materialUseSecondCountMap = materialFragmentUseCountMap.getOrDefault(
                materialFragmentDto.getVideoId(), Maps.newConcurrentMap()
        );

        Integer secondUseCount = materialUseSecondCountMap.getOrDefault(materialFragmentDto.getSecond(), 0);

        return secondUseCount * useCoefficient;
    }

    private double getReduceScoreByShake(MaterialFragmentDto materialFragmentDto, MaterialInfoDto videoMaterial, Double shakeCoefficient) {
        String shakeSecondsStr = videoMaterial.getShakeSeconds();
        if (StringUtils.isBlank(shakeSecondsStr)) {
            return 0.0;
        }
        List<Integer> shakeSeconds = Splitter.on(InngkeAppConst.COMMA_STR).splitToList(shakeSecondsStr).stream().filter(StringUtils::isNotBlank)
                .map(Integer::valueOf).collect(Collectors.toList());

        if (shakeSeconds.contains(materialFragmentDto.getSecond())) {
            return shakeCoefficient;
        }

        return 0.0;
    }

    private List<MaterialInfoDto> getMaxSourceVideoFragmentList(
            List<MaterialFragmentDto> videoMaterialFragment, Integer duration) {

        Map<Long, List<MaterialFragmentDto>> videoMaterialFragmentGroup = videoMaterialFragment.stream()
                .collect(Collectors.groupingBy(MaterialFragmentDto::getVideoId));

        return videoMaterialFragmentGroup.values().stream().map(
                videoMaterialFragmentList -> getMaxVideoFragment(videoMaterialFragmentList, duration)
        ).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private MaterialInfoDto getMaxVideoFragment(List<MaterialFragmentDto> videoMaterialFragmentList, Integer duration) {
        //片段数量大于5，去掉首尾片段
        videoMaterialFragmentList.sort(Comparator.comparing(MaterialFragmentDto::getSecond));
        if (videoMaterialFragmentList.size() > 5) {
            videoMaterialFragmentList.remove(0);
            videoMaterialFragmentList.remove(videoMaterialFragmentList.size() - 1);
        }

        if (videoMaterialFragmentList.size() < duration) {
            return null;
        }
        MaterialInfoDto videoMaterial = new MaterialInfoDto(videoMaterialFragmentList.get(0).getVideoId(), -999.0);

        for (int second = 0; second <= videoMaterialFragmentList.size() - duration; second++) {
            Double source = 0.0;
            for (int plusSecond = 0; plusSecond < duration; plusSecond++) {
                source += videoMaterialFragmentList.get(second + plusSecond).getDistance();
            }
            if (source > videoMaterial.getScore()) {
                videoMaterial.setScore(source);
                videoMaterial.setOptimal(videoMaterialFragmentList.get(second).getSecond());
            }
        }
        videoMaterial.setScore(videoMaterial.getScore() / duration);

        try {
            videoMaterial.setEffectiveIntervalSecond(Lists.newArrayList(videoMaterial.getOptimal(), videoMaterial.getOptimal() + duration));
            videoMaterial.setClipStart(videoMaterial.getOptimal() * 1000);
            videoMaterial.setClipDuration(duration * 1000);
        } catch (Exception e) {
            logger.error("执行发生错误:", e);
        }

        return videoMaterial;
    }

    private List<MaterialInfoDto> fillVideoInfo(List<MaterialInfoDto> maxVideoFragmentList, Map<Long, MaterialInfoDto> materialMap) {
        return maxVideoFragmentList.stream().map(videoMaterialItem -> {
            MaterialInfoDto videoMaterial = materialMap.get(videoMaterialItem.getMaterialId());
            if (Objects.isNull(videoMaterial)) {
                return null;
            }

            videoMaterialItem.setUrl(videoMaterial.getUrl());
            videoMaterialItem.setLowQualityUrl(videoMaterial.getLowQualityUrl());
            videoMaterialItem.setDuration(videoMaterial.getDuration());
            videoMaterialItem.setHeight(videoMaterial.getHeight());
            videoMaterialItem.setWidth(videoMaterial.getWidth());
            videoMaterialItem.setCreateTime(videoMaterial.getCreateTime());
            videoMaterialItem.setRotate(videoMaterial.getRotate());

            //仅填充分类id不再查库填充具体分类信息
            if (StringUtils.isNotBlank(videoMaterial.getCategoryIds())) {
                List<Long> categoryIds = JsonUtil.jsonToList(videoMaterial.getCategoryIds(), Long.class);
                if (!CollectionUtils.isEmpty(categoryIds)) {
                    videoMaterialItem.setCategoryList(categoryIds.stream().map(categoryId -> {
                        MaterialCateDto materialCategoryDto = new MaterialCateDto();
                        materialCategoryDto.setId(categoryId);
                        return materialCategoryDto;
                    }).collect(Collectors.toList()));
                }
            }
            return videoMaterialItem;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private void fillCategoryInfo(List<MaterialInfoDto> videoMaterialItemList) {
        Set<String> categoryIds = videoMaterialItemList.stream().map(MaterialInfoDto::getCategoryList).filter(Objects::nonNull).flatMap(Collection::stream)
                .filter(Objects::nonNull).map(MaterialCateDto::getId).filter(Objects::nonNull).map(String::valueOf).collect(Collectors.toSet());

        Map<Long, MaterialCateDto> categoryMap = vectorSearchAssistantService.getCategoryMap(categoryIds);

        videoMaterialItemList.forEach(videoMaterialItem -> {
            if (org.springframework.util.CollectionUtils.isEmpty(videoMaterialItem.getCategoryList())) {
                return;
            }

            videoMaterialItem.setTags(Lists.newArrayList());

            videoMaterialItem.setCategoryList(videoMaterialItem.getCategoryList().stream().map(categoryDto -> {
                if (Objects.isNull(categoryDto)) {
                    return null;
                }
                MaterialCateDto materialCategory = categoryMap.get(categoryDto.getId());
                if (Objects.isNull(materialCategory)) {
                    return null;
                }
                categoryDto.setName(materialCategory.getName());
                categoryDto.setPid(materialCategory.getPid());
                categoryDto.setSort(materialCategory.getSort());
                videoMaterialItem.getTags().add(categoryDto.getName());
                return categoryDto;
            }).filter(Objects::nonNull).collect(Collectors.toList()));
        });
    }
}
