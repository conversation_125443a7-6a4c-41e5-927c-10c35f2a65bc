package com.inngke.ip.ai.vector.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.inngke.ip.ai.vector.api.MilvusApi;
import com.inngke.ip.ai.vector.api.dto.*;
import com.inngke.ip.ai.vector.config.MilvusConfig2;
import com.inngke.ip.ai.vector.dto.MaterialFragmentDto;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class MilvusClient {

    @Autowired
    private MilvusConfig2 milvusConfig2;

    private static final Map<String, String> DEFAULT_HEADERS = Maps.newHashMap();

    @Autowired
    private MilvusApi milvusApi;

    @PostConstruct
    public void init() {
        DEFAULT_HEADERS.put("Content-Type", "application/json");
        DEFAULT_HEADERS.put("Authorization", "Bearer " + milvusConfig2.getUser() + InngkeAppConst.CLN_STR + milvusConfig2.getPassword());
        DEFAULT_HEADERS.put("accept", "application/json");
    }

    public MilvusSearchRequest buildRequest() {
        MilvusSearchRequest request = new MilvusSearchRequest();

        request.setDbName(milvusConfig2.getDatabase());
        request.setCollectionName(milvusConfig2.getCollection());
        request.setOutputFields(Lists.newArrayList("video_id", "second", "deprecate"));
        request.setLimit(1000);

        MilvusSearchRequest.SearchParams searchParams = new MilvusSearchRequest.SearchParams();
        searchParams.setMetricType(MilvusSearchRequest.SearchParams.METRIC_TYPE_COSINE);

        MilvusSearchRequest.SearchParams.Params params = new MilvusSearchRequest.SearchParams.Params();
        params.setNprobe(1024);
        searchParams.setParams(params);

        request.setSearchParams(searchParams);

        return request;
    }


    public List<MaterialFragmentDto> search(MilvusSearchRequest request) {
        BaseResponse<List<MaterialFragmentDto>> response = milvusApi.search(DEFAULT_HEADERS, request);

        return Optional.ofNullable(response).map(BaseResponse::getData).orElse(Lists.newArrayList());
    }

    public List<MaterialFragmentDto> hybridSearch(MilvusHybridSearchRequest request) {
        BaseResponse<List<MaterialFragmentDto>> response = milvusApi.hybridSearch(DEFAULT_HEADERS, request);

        return Optional.ofNullable(response).map(BaseResponse::getData).orElse(Lists.newArrayList());
    }

    public List<MaterialFragmentDto> query(MilvusSearchRequest request) {
        BaseResponse<List<MaterialFragmentDto>> response = milvusApi.query(DEFAULT_HEADERS, request);

        List<MaterialFragmentDto> materialFragmentDtos = Optional.ofNullable(response).map(BaseResponse::getData).orElse(Lists.newArrayList());
        materialFragmentDtos.forEach(item->{
            item.setCategoryIds(Optional.ofNullable((Map)item.getCategoryIds())
                    .map(cate->(Map)cate.get("Data"))
                    .map(data -> (Map)data.get("LongData"))
                    .map(data -> data.get("data")).orElse(Lists.newArrayList()));

            item.setDirIds(Optional.ofNullable((Map)item.getDirIds())
                    .map(cate->(Map)cate.get("Data"))
                    .map(data -> (Map)data.get("LongData"))
                    .map(data -> data.get("data")).orElse(Lists.newArrayList()));
            item.setTags(Lists.newArrayList());

            System.out.println();
        });
        return materialFragmentDtos;
    }

    public InsertResDto insert(List<MaterialFragmentDto> materialList) {
        InsertRequest insertRequest = new InsertRequest();
        insertRequest.setDbName(milvusConfig2.getDatabase());
        insertRequest.setCollectionName(milvusConfig2.getCollection());
        insertRequest.setData(materialList.stream().map(this::toVideoMaterialFragment).collect(Collectors.toList()));
        BaseResponse<InsertResDto> response = milvusApi.insert(DEFAULT_HEADERS, insertRequest);

        return Optional.ofNullable(response).map(BaseResponse::getData).orElse(null);
    }

    public InsertResDto update(List<MaterialFragmentDto> materialList){
        UpsertRequest insertRequest = new UpsertRequest();
        insertRequest.setDbName(milvusConfig2.getDatabase());
        insertRequest.setCollectionName(milvusConfig2.getCollection());
        insertRequest.setData(materialList);
        BaseResponse<InsertResDto> response = milvusApi.upsert(DEFAULT_HEADERS, insertRequest);

        return Optional.ofNullable(response).map(BaseResponse::getData).orElse(null);
    }

    public List<MaterialFragmentDto> getByVideoId(Long videoId){
        MilvusSearchRequest milvusSearchRequest = buildRequest();
        milvusSearchRequest.setOutputFields(Lists.newArrayList("*"));
        milvusSearchRequest.setFilter("video_id in [" + videoId + "]");
        milvusSearchRequest.setLimit(1000);

        return query(milvusSearchRequest);
    }

    private VideoMaterialFragment toVideoMaterialFragment(MaterialFragmentDto materialFragmentDto) {
        VideoMaterialFragment videoMaterialFragment = new VideoMaterialFragment();
        videoMaterialFragment.setId(materialFragmentDto.getId());
        videoMaterialFragment.setVector(materialFragmentDto.getVector());
        videoMaterialFragment.setVideoId(materialFragmentDto.getVideoId());
        videoMaterialFragment.setSecond(materialFragmentDto.getSecond());
        videoMaterialFragment.setVertical(materialFragmentDto.getVertical());
        videoMaterialFragment.setDeprecate(materialFragmentDto.getDeprecate());
        videoMaterialFragment.setCategoryIds(materialFragmentDto.getCategoryIds());
        videoMaterialFragment.setDirIds(materialFragmentDto.getDirIds());
        videoMaterialFragment.setTags(materialFragmentDto.getTags());
        videoMaterialFragment.setOrganizeId(materialFragmentDto.getOrganizeId());
        videoMaterialFragment.setCreateTime(materialFragmentDto.getCreateTime());
        return videoMaterialFragment;
    }

    /**
     * 构建混合搜索请求
     *
     * @return MilvusHybridSearchRequest
     */
    public MilvusHybridSearchRequest buildHybridSearchRequest(int k) {
        MilvusHybridSearchRequest request = new MilvusHybridSearchRequest();
        request.setDbName(milvusConfig2.getDatabase());
        request.setCollectionName(milvusConfig2.getCollection());
        request.setSearch(Lists.newArrayList());

        // 设置默认重排配置
        MilvusHybridSearchRequest.Rerank rerank = new MilvusHybridSearchRequest.Rerank();
        rerank.setStrategy("rrf"); // Reciprocal Rank Fusion
        Map<String, Object> rerankParams = new HashMap<>();
        rerankParams.put("k", k);
        rerank.setParams(rerankParams);
        request.setRerank(rerank);

        request.setLimit(1000);
        request.setIgnoreGrowing(false);
        request.setOutputFields(Lists.newArrayList("video_id", "second", "deprecate"));

        return request;
    }

    /**
     * 构建混合搜索参数
     *
     * @return MilvusHybridSearchRequest.SearchParam
     */
    public MilvusHybridSearchRequest.SearchParam buildHybridSearchParams() {
        MilvusHybridSearchRequest.SearchParam searchParam = new MilvusHybridSearchRequest.SearchParam();

        // 设置相似度度量类型为余弦相似度
        searchParam.setMetricType("COSINE");
        searchParam.setData(Lists.newArrayList());
        searchParam.setAnnsField("vector");


        // 设置搜索参数
        Map<String, Object> params = new HashMap<>();
        params.put("nprobe", 1024);
        searchParam.setParams(params);

        return searchParam;
    }

    public List<MaterialFragmentDto> doSearch(List<List<Double>> vector, String filter) {
        MilvusSearchRequest milvusSearchRequest = this.buildRequest();
        milvusSearchRequest.setData(vector);
        milvusSearchRequest.setLimit(1000);
        milvusSearchRequest.setFilter(filter);

        //milvus向量搜索
        return this.search(milvusSearchRequest);
    }

}
