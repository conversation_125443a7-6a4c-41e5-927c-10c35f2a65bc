package com.inngke.ip.ai.vector.api;

import com.github.lianjiatech.retrofit.spring.boot.annotation.RetrofitClient;
import com.inngke.ip.ai.vector.api.dto.*;
import com.inngke.ip.ai.vector.dto.MaterialFragmentDto;
import com.inngke.common.dto.response.BaseResponse;
import retrofit2.http.Body;
import retrofit2.http.HeaderMap;
import retrofit2.http.POST;

import java.util.List;
import java.util.Map;

@RetrofitClient(baseUrl = "${inngke.milvus.url:http://*************:19530}",
        callTimeoutMs = 120000,
        connectTimeoutMs = 120000,
        writeTimeoutMs = 120000,
        readTimeoutMs = 120000,
        retryOnConnectionFailure = false
)
public interface MilvusApi {

    @POST("/v2/vectordb/entities/search")
    BaseResponse<List<MaterialFragmentDto>> search(@HeaderMap Map<String, String> headers, @Body MilvusSearchRequest milvusSearchParams);

    @POST("/v2/vectordb/entities/query")
    BaseResponse<List<MaterialFragmentDto>> query(@HeaderMap Map<String, String> headers, @Body MilvusSearchRequest milvusSearchParams);

    @POST("/v2/vectordb/entities/insert")
    BaseResponse<InsertResDto> insert(@HeaderMap Map<String, String> headers, @Body InsertRequest request);

    @POST("/v2/vectordb/entities/upsert")
    BaseResponse<InsertResDto> upsert(@HeaderMap Map<String, String> headers, @Body UpsertRequest request);

    /**
     * 混合搜索接口
     * 支持多个向量字段的联合搜索,可以同时搜索稀疏向量和密集向量
     *
     * @param headers 请求头
     * @param request 混合搜索请求
     * @return 搜索结果
     */
    @POST("/v2/vectordb/entities/advanced_search")
    BaseResponse<List<MaterialFragmentDto>> hybridSearch(@HeaderMap Map<String, String> headers, @Body MilvusHybridSearchRequest request);
}
