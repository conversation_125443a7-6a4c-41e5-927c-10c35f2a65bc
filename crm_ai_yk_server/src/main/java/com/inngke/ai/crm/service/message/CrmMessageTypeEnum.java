package com.inngke.ai.crm.service.message;

/**
 * <AUTHOR>
 * @since 2023-09-04 14:00
 **/
public enum CrmMessageTypeEnum {

    AI_GENERATE_SUCCESS("AIGC生成成功通知", "ai_generate_success"),
    AI_USER_DOCUMENT_SUCCESS("AI用户文档索引完成", "ai_user_document_success"),

    AI_USER_IMAGE_SUCCESS("AI用户效果图创作完成", "ai_user_image_success"),

    AI_USER_VIDEO_SUCCESS("AI视频创作完成", "ai_user_video_success"),

    ENTERPRISE_INVITE_MSG("企业邀约短信", "enterprise_invite_msg"),

    XIAO_HONG_SHU_PUBLISH("小红书发布通知", "xiao_hong_shu_publish"),

    ORGANIZE_RECHARGE_MSG("企业积分充值短信通知", "organize_recharge_msg"),
    DOU_YIN_OAUTH_SMS("抖音授权通知","authorization_reminder"),

    CREATION_TASK_MSG("创作任务短信","task_reminder"),

    PUBLISH_TASK_MSG("发布任务短信","publish_task_reminder")
    ;

    private final String name;

    private final String templateCode;


    CrmMessageTypeEnum(String name, String templateCode) {
        this.name = name;
        this.templateCode = templateCode;
    }

    public String getName() {
        return name;
    }

    public String getTemplateCode() {
        return templateCode;
    }


}
