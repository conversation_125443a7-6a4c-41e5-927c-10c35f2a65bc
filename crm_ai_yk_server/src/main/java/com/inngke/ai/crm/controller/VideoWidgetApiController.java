package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.dto.request.video.CreateAiWidgetTemplateRequest;
import com.inngke.ai.crm.dto.request.video.WidgetSaveRequest;
import com.inngke.ai.crm.dto.request.video.WidgetTemplateQuery;
import com.inngke.ai.crm.dto.response.video.WidgetDetail;
import com.inngke.ai.crm.dto.response.video.WidgetEditConfigDto;
import com.inngke.ai.crm.dto.response.video.WidgetTemplateListItem;
import com.inngke.ai.crm.service.VideoWidgetService;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @chapter 视频
 * @section 贴片
 */
@RestController
@RequestMapping("/api/ai/video-widget")
public class VideoWidgetApiController {
    @Autowired
    private VideoWidgetService videoWidgetService;

    /**
     * 获取贴片编辑选项
     */
    @GetMapping("/widget-edit-config")
    public BaseResponse<WidgetEditConfigDto> getWidgetEditConfig(
            @RequestAttribute JwtPayload jwtPayload
    ) {
        return BaseResponse.success(videoWidgetService.getWidgetEditConfig(jwtPayload));
    }

    /**
     * 获取贴片模板列表
     */
    @GetMapping("/widget-template")
    public BaseResponse<List<WidgetTemplateListItem>> listWidgetTemplates(
            @RequestAttribute JwtPayload jwtPayload,
            WidgetTemplateQuery request
    ) {

        return BaseResponse.success(videoWidgetService.listWidgetTemplates(jwtPayload, request));
    }

    /**
     * 保存贴片模板
     */
    @PostMapping
    public BaseResponse<WidgetTemplateListItem> save(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody WidgetSaveRequest request
    ) {
        return BaseResponse.success(videoWidgetService.save(jwtPayload, request));
    }

    /**
     * 获取贴片配置
     */
    @GetMapping("/widget-template/{id:\\d+}")
    public BaseResponse<WidgetDetail> getDetail(
            @RequestAttribute JwtPayload jwtPayload,
            @PathVariable long id
    ) {
        return BaseResponse.success(videoWidgetService.getDetail(jwtPayload, id));
    }

    /**
     * 删除贴片配置
     */
    @DeleteMapping("/widget-template/{id:\\d+}")
    public BaseResponse<Boolean> delete(
            @RequestAttribute JwtPayload jwtPayload,
            @PathVariable long id
    ) {
        videoWidgetService.delete(jwtPayload, id);
        return BaseResponse.success(true);
    }

    /**
     * 智能模板
     * 获取用户上一次创作时选的模板（缺省时随机取一个模板），请求DIFY生成新模板
     */
    @PostMapping("/ai-widget-template")
    public BaseResponse<WidgetDetail> createAiWidgetTemplate(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody CreateAiWidgetTemplateRequest request
    ) {
        return BaseResponse.success(videoWidgetService.createAiWidgetTemplate(jwtPayload, request));
    }
}
