package com.inngke.ai.crm.service.qunfeng.init;

import com.inngke.ai.crm.db.crm.entity.Coin;
import com.inngke.ai.crm.db.crm.entity.CoinLog;
import com.inngke.ai.crm.db.crm.entity.User;
import com.inngke.ai.crm.db.crm.entity.UserVip;
import com.inngke.ai.crm.dto.enums.CoinLogEventTypeEnum;
import com.inngke.ai.crm.dto.enums.VipTypeEnum;
import com.inngke.common.core.utils.SnowflakeHelper;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
public class CoinInit extends Init {

    @Override
    public Class<? extends Init> next() {
        return null;
    }

    @Override
    public void init(InitContext ctx) {
        Coin coin = initCoin(ctx);
        ctx.setCoin(coin);

        CoinLog coinLog = initCoinLog(ctx);
        ctx.setCoinLog(coinLog);
    }

    private CoinLog initCoinLog(InitContext ctx) {
        User user = ctx.getUser();
        Coin coin = ctx.getCoin();
        CoinLog coinLog = new CoinLog();
        coinLog.setId(SnowflakeHelper.getId());
        coinLog.setUserId(user.getId());
        coinLog.setCoinId(coin.getId());
        coinLog.setCoin(coin.getCoin());

        if (VipTypeEnum.S_VIP.getType().equals(ctx.getUserVip().getVipType())) {
            coinLog.setEventType(CoinLogEventTypeEnum.S_VIP.getCode());
        }else {
            coinLog.setEventType(CoinLogEventTypeEnum.VIP.getCode());
        }
        coinLog.setEventLogId(coin.getId());
        coinLog.setCreateTime(LocalDateTime.now());

        return coinLog;
    }

    private Coin initCoin(InitContext ctx) {
        User user = ctx.getUser();
        UserVip userVip = ctx.getUserVip();
        Coin coin = new Coin();
        coin.setId(SnowflakeHelper.getId());
        coin.setUserId(user.getId());
        coin.setCoin(userVip.getCoin());
        coin.setDispatchId(0L);
        coin.setDispatchType(3);
        coin.setDispatchSrcId(null);
        coin.setTotalCoin(ctx.getInitCoinProduct().getCoin());
        coin.setExpireTime(getExtTime(userVip.getPeriodType()));
        coin.setCreateTime(LocalDateTime.now());

        return coin;
    }
}
