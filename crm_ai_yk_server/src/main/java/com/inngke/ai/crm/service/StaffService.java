package com.inngke.ai.crm.service;

import com.inngke.ai.crm.controller.StaffInfoDto;
import com.inngke.ai.crm.db.crm.entity.Staff;
import com.inngke.ai.crm.dto.request.org.DeleteStaffRequest;
import com.inngke.ai.crm.dto.request.org.EditStaffRequest;
import com.inngke.ai.crm.dto.request.org.GetOrgStaffPagingRequest;
import com.inngke.ai.crm.dto.request.org.GetStaffInfoRequest;
import com.inngke.ai.crm.dto.request.org.staff.CreateStaffRequest;
import com.inngke.ai.crm.dto.response.org.StaffItemDto;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;

public interface StaffService {

    /**
     * 创建员工
     */
    BaseResponse<Boolean> createStaff(JwtPayload jwtPayload, CreateStaffRequest request);

    /**
     * 编辑员工
     */
    BaseResponse<Boolean> editStaff(JwtPayload jwtPayload, EditStaffRequest request);


    /**
     * 获取员工详情
     */
    BaseResponse<StaffInfoDto> getStaffInfo(JwtPayload jwtPayload, GetStaffInfoRequest request);

    /**
     * 退出企业
     */
    BaseResponse<Boolean> quit(JwtPayload jwtPayload,DeleteStaffRequest request);

    /**
     * 退出企业
     */
    BaseResponse<Boolean> quit(JwtPayload jwtPayload);

    /**
     * 获取员工列表
     */
    BaseResponse<BasePaginationResponse<StaffItemDto>> getStaffPaging(JwtPayload jwtPayload, GetOrgStaffPagingRequest request);

    /**
     * 删除员工
     */
    BaseResponse<Boolean> delete(JwtPayload jwtPayload, DeleteStaffRequest request);

    /**
     * 通过用户ID获取员工信息
     * @param userId 用户ID
     */
    Staff getStaffByUserId(Long userId);

    Long getUserStaffOrganizeId(Long userId);

    Staff getStaffByUserIdNotExistThrow(JwtPayload jwtPayload);

    Staff getStaffById(Long staffId);
}
