package com.inngke.ai.crm.controller;


import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.ai.crm.db.crm.entity.VideoOceanengineDiagnosis;
import com.inngke.ai.crm.db.crm.manager.VideoOceanengineDiagnosisManager;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.utils.JsonUtil;
import com.inngke.ip.ai.volc.dto.request.ChallengeSpiRequest;
import com.inngke.ip.ai.volc.dto.request.MaterialPreCheckResponse;
import com.inngke.ip.ai.volc.dto.response.VolcBaseSpiResponse;
import com.inngke.ip.ai.volc.dto.response.VolcChallengeSpiResponse;
import com.inngke.ip.ai.volc.utils.AuthTokenUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * @chapter 巨量引擎
 * @section 巨量引擎回调
 */
@RestController
@RequestMapping("/api/ai/volc-spi")
public class VolcSpiController {

    public static final String SECRET_KEY = "c685a9f8943c46b2bca99f10e8ab3a59";
    private static final String MATERIAL_PRECHECK_AD = "ad.audit.material_precheck_ad";
    public static final long ADVERTISER_IDS = 1829722283293193L;

    @Autowired
    private VideoOceanengineDiagnosisManager videoOceanengineDiagnosisManager;

    @GetMapping("/callback")
    public Object apiGetCallback(
            HttpServletRequest request,
            ChallengeSpiRequest challengeSpiRequest
    ) {
        if ("challengeSpiRequest".equals(challengeSpiRequest.getEvent())) {
            return getVolcChallengeSpiResponse(request, challengeSpiRequest);
        }

        return getVolcChallengeSpiResponse(request, challengeSpiRequest);
    }

    @PostMapping("/callback")
    public Object apiPostCallback(
            HttpServletRequest request,
            @RequestBody ChallengeSpiRequest challengeSpiRequest
    ) {
        if ("challengeSpiRequest".equals(challengeSpiRequest.getEvent())) {
            return getVolcChallengeSpiResponse(request, challengeSpiRequest);
        }

        return getVolcChallengeSpiResponse(request, challengeSpiRequest);
    }

    private VolcChallengeSpiResponse getVolcChallengeSpiResponse(HttpServletRequest request, ChallengeSpiRequest challengeSpiRequest) {
        byte[] data;
        if (request.getMethod().equalsIgnoreCase("POST")) {
            try {
                AuthTokenUtil.InputStreamCacher cacher = new AuthTokenUtil.InputStreamCacher(request.getInputStream());
                data = AuthTokenUtil.readAsBytes(cacher);
            } catch (Exception e) {
                throw new InngkeServiceException(e);
            }
        } else {
            data = request.getQueryString().getBytes();
        }

        boolean isValidToken = AuthTokenUtil.isValidToken(SECRET_KEY, data, request.getHeader("X-Open-Signature"));
        if (!isValidToken) {
            return new VolcChallengeSpiResponse().setResp(new VolcBaseSpiResponse(400, "签名错误"));
        }

        // 读取 body
        if (request.getMethod().equalsIgnoreCase("POST")) {
            try {
                String body = new String(data, "UTF-8");
                if (StringUtils.hasLength(body) && body.contains(MATERIAL_PRECHECK_AD)) {
                    materialPreCheckAdProcess(body);
                }


            } catch (Exception e) {
                throw new InngkeServiceException(e);
            }
        }

        return new VolcChallengeSpiResponse().setResp(new VolcBaseSpiResponse(200, "ok")).setChallenge(challengeSpiRequest.getChallenge());
    }

    private void materialPreCheckAdProcess(String body) {
        MaterialPreCheckResponse response = JsonUtil.jsonToObject(body, MaterialPreCheckResponse.class);
        if (response == null) {
            return;
        }
        String dataStr = response.getData();
        if (!StringUtils.hasLength(dataStr)) {
            return;
        }
        MaterialPreCheckResponse.Data dataObj = JsonUtil.jsonToObject(dataStr, MaterialPreCheckResponse.Data.class);
        if (dataObj == null) {
            return;
        }
        if (!dataObj.getUserId().equals(ADVERTISER_IDS)) {
            return;
        }
        String contentStr = dataObj.getContent();
        if (!StringUtils.hasLength(contentStr)) {
            return;
        }
        MaterialPreCheckResponse.Content contentObj = JsonUtil.jsonToObject(contentStr, MaterialPreCheckResponse.Content.class);
        if (contentObj == null) {
            return;
        }
        UpdateWrapper<VideoOceanengineDiagnosis> updateWrapper = Wrappers.<VideoOceanengineDiagnosis>update()
                .eq(VideoOceanengineDiagnosis.OE_OBJECT_ID, contentObj.getObjectId());
        if (contentObj.getStatus().equals("APPROVE")) {
            updateWrapper.set(VideoOceanengineDiagnosis.AUDIO_STATUS, 2);
        } else if (contentObj.getStatus().equals("REJECT")) {
            updateWrapper.set(VideoOceanengineDiagnosis.AUDIO_STATUS, -2);
        } else {
            //AUDITING 审核中
            return;
        }
        updateWrapper.set(StringUtils.hasLength(contentObj.getReasonText()), VideoOceanengineDiagnosis.AUDIO_REJECT_REASON, contentObj.getReasonText());
        videoOceanengineDiagnosisManager.update(updateWrapper);
    }
}