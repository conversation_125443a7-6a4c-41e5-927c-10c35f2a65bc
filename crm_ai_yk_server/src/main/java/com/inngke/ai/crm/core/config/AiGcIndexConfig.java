package com.inngke.ai.crm.core.config;

import com.inngke.ai.crm.dto.request.UserIndexDto;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-09-05 20:42
 **/
@Configuration
@ConfigurationProperties(prefix = "inngke.index")
public class AiGcIndexConfig {

    private List<UserIndexDto> list;


    public List<UserIndexDto> getList() {
        return list;
    }

    public void setList(List<UserIndexDto> list) {
        this.list = list;
    }
}
