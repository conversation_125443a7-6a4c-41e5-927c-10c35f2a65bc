package com.inngke.ai.crm.service.schedule;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Sets;
import com.inngke.ai.crm.core.CrmServiceConsts;
import com.inngke.ai.crm.db.crm.entity.AiGenerateTaskRelease;
import com.inngke.ai.crm.db.crm.entity.DouYinAccount;
import com.inngke.ai.crm.db.crm.manager.AiGenerateTaskReleaseManager;
import com.inngke.ai.crm.db.crm.manager.AppConfigManager;
import com.inngke.ai.crm.db.crm.manager.DouYinAccountManager;
import com.inngke.ai.crm.dto.enums.AppConfigCodeEnum;
import com.inngke.ai.crm.service.TpAccountService;
import com.inngke.ai.crm.service.es.AiGenerateTaskEsService;
import com.inngke.ai.crm.service.impl.DouYinDataService;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.dto.Lock;
import com.inngke.common.service.LockService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@Component
public class DouYinSchedule {

    public static final String IGNORE_PROFILE_ACTIVE = "dev";
    private static final Executor pool = Executors.newFixedThreadPool(4);

    @Value("${spring.profiles.active:dev}")
    private String profileActive;

    @Resource
    private DouYinDataService douYinDataService;
    @Resource
    private LockService lockService;
    @Resource
    private AiGenerateTaskReleaseManager aiGenerateTaskReleaseManager;
    @Resource
    private AiGenerateTaskEsService aiGenerateTaskEsService;
    @Resource
    private DouYinAccountManager douYinAccountManager;
    @Resource
    private TpAccountService tpAccountService;
    @Resource
    private AppConfigManager appConfigManager;

    /**
     * 更新抖音数据，规则：
     * 1. 发布7天内，一小时更新一次数据
     * 2. 发布7天后，每天更新一次数据
     * 3. 发布30天后，不再更新数据
     * 4. 每天 00:00 ~ 08:00 停止更新数据
     *
     * 待优化： @陆键 功能优化完，修改下这里的注释
     * 1. 发布后又下架的视频，需要做个逻辑检测，并停止更新数据
     * 2. 有授权的账号，不走浏览器拉取数据，更新频次相同
     */
    @Scheduled(cron = "0 12 2 * * *")
    public void refreshVideoData() {
        if (IGNORE_PROFILE_ACTIVE.equals(profileActive)) {
            return;
        }
        Lock lock = lockService.getLock(CrmServiceConsts.CACHE_LOCK_KEY_PRE + "refresh:video:data", 23*60*60);
        if (Objects.isNull(lock)) {
            return;
        }
        int hour = LocalTime.now().getHour();
        if (hour < 8) {
            return;
        }

        String token = appConfigManager.getValueByCode(AppConfigCodeEnum.TIK_HUB_APP_KEY.getCode());

        List<AiGenerateTaskRelease> refreshList = aiGenerateTaskReleaseManager.getRefreshList(2);

        refreshList.stream().map(releaseData -> AsyncUtils.supplyTraceAsync(
                () -> douYinDataService.refreshVideoData(releaseData.getId(), releaseData.getExternalId(), token),pool)
        ).forEach(AsyncUtils::getFutureData);
    }

    /**
     * 刷新抖音账号数据，规则
     * 接口已失效
     * @deprecated
     */
//    @Scheduled(cron = "0 0 * * * *")
    public void refreshDouYinVideoData() {
        if (IGNORE_PROFILE_ACTIVE.equals(profileActive)) {
            return;
        }
        Lock lock = lockService.getLock(CrmServiceConsts.CACHE_LOCK_KEY_PRE + "refresh:douyin:video:data", 60);
        if (Objects.isNull(lock)) {
            return;
        }

        List<DouYinAccount> douYinAccountList = douYinAccountManager.list(Wrappers.<DouYinAccount>query()
                .ne(DouYinAccount.ACCESS_TOKEN, InngkeAppConst.EMPTY_STR)
                .gt(DouYinAccount.ACCESS_TOKEN_EXPIRE_AT, LocalDateTime.now())
        );
        douYinAccountList.forEach(douYinDataService::refreshDouYinAccountVideoData);
    }

    @Scheduled(cron = "22 1 0 * * *")
    public void refreshDouYinAccount() {
        Lock lock = lockService.getLock(CrmServiceConsts.CACHE_LOCK_KEY_PRE + "refresh:douyin:account", 360);
        if (Objects.isNull(lock)) {
            return;
        }

        List<DouYinAccount> list = douYinAccountManager.list(Wrappers.<DouYinAccount>query()
                .lt(DouYinAccount.ACCESS_TOKEN_EXPIRE_AT, LocalDate.now().plusDays(1).atTime(LocalTime.MAX))
                .or()
                .lt(DouYinAccount.REFRESH_TOKEN_EXPIRE_AT, LocalDate.now().plusDays(1).atTime(LocalTime.MAX))
        );

        Set<String> refreshed = Sets.newHashSet();
        for (DouYinAccount douYinAccount : list) {
            if (refreshed.contains(douYinAccount.getOpenid())) {
                continue;
            }

            tpAccountService.refreshDouYinAccount(douYinAccount);
            refreshed.add(douYinAccount.getOpenid());
        }
    }
}
