package com.inngke.ai.crm.service;

import com.inngke.ai.crm.dto.request.digital.person.CreateDigitalPersonRequest;
import com.inngke.ai.crm.dto.request.digital.person.GetDigitalPersonListRequest;
import com.inngke.ai.crm.dto.response.digital.person.DigitalPersonTagDto;
import com.inngke.ai.crm.dto.response.digital.person.DigitalPersonTemplateDto;
import com.inngke.ai.dto.response.DigitalPersonVideoDto;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;

import java.util.List;

public interface DigitalPersonService {

    BaseResponse<Long> create(CreateDigitalPersonRequest request);

    BaseResponse<List<DigitalPersonTemplateDto>> getDigitalPersonList(JwtPayload jwtPayload, GetDigitalPersonListRequest type);

    BaseResponse<DigitalPersonVideoDto> getDigitalPerson(Long id);

    BaseResponse<List<DigitalPersonTagDto>> getDigitalPersonTagList();

    BaseResponse<DigitalPersonTemplateDto> getDigitalPersonTemplate(Long id);

    List<Integer> getFaceCenterPosition(String url);
}
