package com.inngke.ai.crm.dto.request.video;

import com.inngke.common.dto.request.BasePageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class GetVideoMaterialListRequest extends BasePageRequest {

    private Long videoId;

    /**
     * -2=识别内容失败 -1=已删除 0=初始化 1=识别内容成功
     *
     * @demo 1
     */
    private Integer status;

    /**
     * 素材库分组ID
     *
     * @demo 87656789234
     */
    private List<Long> categoryIds;

    /**
     * 视频理解内容
     */
    private String content;

    /**
     * 向量检索
     */
    private String vectorQuery;
}
