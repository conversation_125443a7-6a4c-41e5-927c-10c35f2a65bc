package com.inngke.ai.crm.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.beust.jcommander.internal.Sets;
import com.google.common.base.Splitter;
import com.inngke.ai.crm.db.crm.entity.JianyingResource;
import com.inngke.ai.crm.db.crm.manager.AppConfigManager;
import com.inngke.ai.crm.db.crm.manager.JianyingResourceManager;
import com.inngke.ai.crm.dto.enums.AppConfigCodeEnum;
import com.inngke.ai.crm.dto.response.video.JianyingResourceSimpleDto;
import com.inngke.ai.crm.service.JianyingResourceService;
import com.inngke.common.core.InngkeAppConst;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class JianyingResourceServiceImpl implements JianyingResourceService {
    @Autowired
    private JianyingResourceManager jianyingResourceManager;

    @Autowired
    private AppConfigManager appConfigManager;

    /**
     * 获取文本样式
     * 1. 先从 jianying_resource中查询出material_type=text的记录
     * 2. 再从 app_config中查询出 video.jy.resource.{type}.excludes记录
     * 3. 排除掉不需要的返回
     *
     * @param type 类型：subtitle=字幕 bigTitle=大字报 cover=封面
     */
    @Override
    public List<JianyingResourceSimpleDto> getTextResources(String type) {
        AppConfigCodeEnum typeEnum;
        switch (type) {
            case "subtitle":
                typeEnum = AppConfigCodeEnum.VIDEO_JY_RESOURCE_SUBTITLE_EXCLUDES;
                break;
            case "bigTitle":
                typeEnum = AppConfigCodeEnum.VIDEO_JY_RESOURCE_BIG_TITLE_EXCLUDES;
                break;
            case "cover":
                typeEnum = AppConfigCodeEnum.VIDEO_JY_RESOURCE_COVER_EXCLUDES;
                break;
            default:
                return List.of();
        }
        String excludeIdStr = appConfigManager.getValueByCode(typeEnum.getCode());
        Set<Integer> excludeIds = Sets.newHashSet();
        if (StringUtils.hasLength(excludeIdStr)) {
            Splitter.on(InngkeAppConst.COMMA_STR)
                    .omitEmptyStrings()
                    .trimResults()
                    .split(excludeIdStr)
                    .forEach(idStr -> {
                        excludeIds.add(Integer.parseInt(idStr));
                    });
        }
        return jianyingResourceManager.list(
                Wrappers.<JianyingResource>query().eq(JianyingResource.MATERIAL_TYPE, "text")
                        .eq(JianyingResource.STATUS, 1)
                        .notIn(!CollectionUtils.isEmpty(excludeIds), JianyingResource.ID, excludeIds)
                        .select(
                                JianyingResource.ID,
                                JianyingResource.RESOURCE_ID,
                                JianyingResource.NAME,
                                JianyingResource.DEMO_URL,
                                JianyingResource.MATERIAL_TYPE
                        )
                        .orderByDesc(JianyingResource.ORDER_SCORE)
        ).stream().map(resource -> new JianyingResourceSimpleDto()
                .setId(resource.getId())
                .setResourceId(resource.getResourceId())
                .setName(resource.getName())
                .setDemoUrl(resource.getDemoUrl())
                .setMaterialType(resource.getMaterialType())).collect(Collectors.toList());
    }
}
