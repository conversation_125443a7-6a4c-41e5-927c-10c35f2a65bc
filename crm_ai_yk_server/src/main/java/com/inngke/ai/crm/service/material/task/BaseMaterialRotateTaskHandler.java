package com.inngke.ai.crm.service.material.task;

import com.inngke.ai.crm.api.video.VideoMaterialApi;
import com.inngke.ai.crm.api.video.dto.FfmpegVideoInfoDto;
import com.inngke.ai.crm.api.video.dto.RotateMaterialRequest;
import com.inngke.ai.crm.core.CrmServiceConsts;
import com.inngke.ai.crm.dto.VideoMaterialTask;
import com.inngke.ai.crm.service.VideoJobService;
import com.inngke.ai.dto.request.VideoJobCreateRequest;
import com.inngke.ai.dto.response.VideoJobIdData;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.utils.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.concurrent.Executor;

public abstract class BaseMaterialRotateTaskHandler implements MaterialTaskHandler<Integer, FfmpegVideoInfoDto> {
    private static final Logger logger = LoggerFactory.getLogger(BaseMaterialRotateTaskHandler.class);
    public static final String VIDEO_MATERIAL_ROTATE_KEYS = CrmServiceConsts.CACHE_KEY_PRE + "materialTask:rotate";

    @Autowired
    private VideoJobService videoJobService;

    @Autowired
    @Qualifier("otherThreadPool")
    private Executor otherThreadPool;

    @Autowired
    private VideoMaterialApi videoMaterialApi;

    @Autowired
    private RedisTemplate redisTemplate;

    @Override
    public void afterSubmitTask(VideoMaterialTask<Integer> task) {
        //临时存放旋转角度
        redisTemplate.opsForHash().put(VIDEO_MATERIAL_ROTATE_KEYS, task.getId(), task.getParams());
    }

    @Override
    public FfmpegVideoInfoDto process(VideoMaterialTask<Integer> task) {
        RotateMaterialRequest request = new RotateMaterialRequest();
        request.setUrl(task.getUrl());
        request.setRotate(task.getParams().toString());
        logger.info("执行旋转任务：{}", JsonUtil.toJsonString(request));
        BaseResponse<FfmpegVideoInfoDto> response = videoMaterialApi.rotate2(request);
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            return null;
        }
        return response.getData();
    }

    @Override
    public boolean isTaskSuccess(VideoMaterialTask<Integer> task, FfmpegVideoInfoDto data) {
        return data != null;
    }

    @Override
    public void callback(VideoMaterialTask<Integer> task, FfmpegVideoInfoDto data) {
        //删除临时存放旋转角度
        redisTemplate.opsForHash().delete(VIDEO_MATERIAL_ROTATE_KEYS, task.getId());

        if (!isTaskSuccess(task, data)) {
            return;
        }
        doSuccess(task, data);

        AsyncUtils.runAsync(() -> {
            //创建videoJob: 删除本地素材缓存
            videoJobService.create(
                    new VideoJobCreateRequest()
                            .setJobType(VideoJobCreateRequest.JOB_TYPE_MATERIAL_CACHE_CLEAR)
                            .setJobData(new VideoJobIdData().setId(task.getId()))
            );

            doSuccessAsync(task, data);
        }, otherThreadPool);
    }

    protected void doSuccess(VideoMaterialTask<Integer> task, FfmpegVideoInfoDto data) {

    }

    protected void doSuccessAsync(VideoMaterialTask<Integer> task, FfmpegVideoInfoDto data) {

    }
}
