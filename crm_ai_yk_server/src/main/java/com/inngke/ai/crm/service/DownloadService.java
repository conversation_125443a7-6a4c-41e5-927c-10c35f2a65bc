package com.inngke.ai.crm.service;

import java.io.File;

public interface DownloadService {
    /**
     * 下载文件
     *
     * @param taskId   任务ID
     * @param url      文件URL
     * @param filePath 文件路径
     */
    void addDownload(long taskId, String url, String filePath);

    /**
     * 等待下载完成
     *
     * @param taskId 任务ID
     * @return 是否已经全部下载成功，如果有失败的，返回false
     */
    boolean waitDownload(long taskId);

    void directDownload(String url, File distFile);

    /**
     * 清理下载文件
     *
     * @param taskId 任务ID
     */
    void clear(long taskId);
}
