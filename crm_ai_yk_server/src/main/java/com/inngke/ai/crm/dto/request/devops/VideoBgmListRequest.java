package com.inngke.ai.crm.dto.request.devops;

import com.inngke.common.dto.request.BasePageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class VideoBgmListRequest extends BasePageRequest {
    /**
     * 搜索关键词
     *
     * @demo 志邦家居
     */
    private String keyword;

    /**
     * BGM分类
     */
    private Integer type;

    /**
     * 背景音乐ids
     */
    private List<Long> ids;

    private Long organizeId;
}
