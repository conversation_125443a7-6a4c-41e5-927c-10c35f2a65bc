package com.inngke.ai.crm.service;

import com.inngke.ai.crm.dto.IdPageDto;
import com.inngke.ai.crm.dto.request.IdPageRequest;
import com.inngke.ai.crm.dto.request.publish.*;
import com.inngke.ai.crm.dto.response.publish.*;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BasePaginationResponse;

public interface PublishTaskService {

    BasePaginationResponse<PublishTaskDto> list(JwtPayload jwtPayload, GetPublishTaskListRequest request);

    PublishTaskDto create(JwtPayload jwtPayload, CreatePublishTaskRequest request);

    boolean update(JwtPayload jwtPayload, UpdatePublishTaskRequest request);

    boolean upAndDownShelves(JwtPayload jwtPayload, Long id);

    boolean addVideo(AddPublishTaskVideoRequest request);

    PublishVideoDto receiveVideo(JwtPayload jwtPayload, Long id);

    PublishVideoDto getReceivedVideo(JwtPayload jwtPayload, Long taskId);

    BasePaginationResponse<PendingVideoDto> getPendingVideo(JwtPayload jwtPayload, GetPendingVideoListRequest request);

    BasePaginationResponse<SelectedVideoDto> getSelectedVideo(JwtPayload jwtPayload, GetSelectedVideoListRequest request);

    boolean unSelectVideo(JwtPayload jwtPayload, UnSelectVideoRequest request);

    boolean reSelectVideo(JwtPayload jwtPayload, ReSelectVideoRequest request);

    PublishTaskDto getInfo(JwtPayload jwtPayload, Long taskId);

    BasePaginationResponse<PublishTaskStatisticsDto> getStatistics(JwtPayload jwtPayload, GetPublishTaskStatisticsRequest request);

    IdPageDto<ReceivedPublishTaskVideoDto> getReceivedVideoList(JwtPayload jwtPayload, IdPageRequest request);

    Boolean downloadVideo(JwtPayload jwtPayload, Long taskId);
}
