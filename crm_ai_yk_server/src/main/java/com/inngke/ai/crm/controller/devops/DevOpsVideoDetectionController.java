package com.inngke.ai.crm.controller.devops;

import com.inngke.ai.crm.dto.request.IdPageRequest;
import com.inngke.ai.crm.dto.request.devops.GetDetectionRequest;
import com.inngke.ai.crm.dto.request.devops.GetReDetectionListRequest;
import com.inngke.ai.crm.dto.request.devops.SaveDetectionResultRequest;
import com.inngke.ai.crm.dto.request.devops.SaveReDetectionRequest;
import com.inngke.ai.crm.dto.response.devops.VideoDetectionDto;
import com.inngke.ai.crm.dto.response.devops.VideoDetectionStatisticsDto;
import com.inngke.ai.crm.dto.response.devops.VideoReDetectionDto;
import com.inngke.ai.crm.service.devops.VideoDetectionService;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @chapter DevOps
 * @section 素材审核
 */
@RestController
@RequestMapping("/api/ai/devops/video/detection")
public class DevOpsVideoDetectionController {

    @Autowired
    private VideoDetectionService videoDetectionService;

    /**
     * 复检列表
     */
    @GetMapping("/re_detection/list")
    public BaseResponse<List<VideoReDetectionDto>> getReDetectionList(GetReDetectionListRequest request) {
        return videoDetectionService.getReDetectionList(request);
    }

    @GetMapping("/surplus/count")
    public BaseResponse<Integer> surplusCount(GetReDetectionListRequest request) {
        return videoDetectionService.getSurplusCount(request);
    }

    /**
     * 保存结果
     */
    @PutMapping("/re_detection/save")
    public BaseResponse<Boolean> saveReDetection(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody SaveReDetectionRequest request) {
        return videoDetectionService.saveReDetection(jwtPayload,request);
    }

    /**
     * 获取视频审核列表
     */
    @GetMapping("/list")
    public BaseResponse<BasePaginationResponse<VideoDetectionDto>> getDetection(GetDetectionRequest request) {
        return videoDetectionService.getDetection(request);
    }

    /**
     * 获取待检测(机器检测)列表
     */
    @GetMapping("/awaiting/list")
    public BaseResponse<List<VideoDetectionDto>> getAwaitingDetectionList(IdPageRequest request) {
        return videoDetectionService.getAwaitingDetectionList(request);
    }

    @PutMapping("/save/result")
    public BaseResponse<Boolean> saveDetectionResult(@RequestBody SaveDetectionResultRequest request) {
        return videoDetectionService.saveDetectionResult(request);
    }

    @GetMapping("/statistics")
    public BaseResponse<List<VideoDetectionStatisticsDto>> statistics() {
        return videoDetectionService.statistics();
    }
}
