package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.dto.request.creation.task.*;
import com.inngke.ai.crm.dto.response.creation.task.CreationTaskDto;
import com.inngke.ai.crm.dto.response.creation.task.StaffCreationTaskStateDto;
import com.inngke.ai.crm.service.creation.task.CreationTaskService;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @chapter 企业模块
 * @section 创作任务
 **/
@RestController
@RequestMapping("/api/ai/creation-task")
public class CreationTaskController {

    @Autowired
    private CreationTaskService creationTaskService;

    /**
     * 创建任务
     */
    @PostMapping
    public BaseResponse<CreationTaskDto> createCreationTask(
            @RequestAttribute JwtPayload jwtPayload,
            @Validated @RequestBody CreateCreationTaskRequest request) {
        return creationTaskService.createCreationTask(jwtPayload, request);
    }

    /**
     * 编辑任务
     */
    @PutMapping("/id/{id:\\d+}")
    public BaseResponse<CreationTaskDto> editCreationTask(
            @RequestAttribute JwtPayload jwtPayload,
            @PathVariable Long id,
            @RequestBody EditCreationTaskRequest request) {
        request.setId(id);
        return creationTaskService.editCreationTask(jwtPayload, request);
    }

    /**
     * 获取任务详情
     */
    @GetMapping("/id/{id:\\d+}")
    public BaseResponse<CreationTaskDto> getCreationTaskInfo(
            @RequestAttribute JwtPayload jwtPayload,
            @PathVariable Long id) {
        return creationTaskService.getCreationTaskInfo(id);
    }

    /**
     * 任务状态明细
     */
    @GetMapping("/id/{taskId:\\d+}/state")
    public BaseResponse<BasePaginationResponse<StaffCreationTaskStateDto>> searchStaffCreationTaskList(
            @RequestAttribute JwtPayload jwtPayload,
            @Validated SearchStaffCreationTaskListRequest request) {
        return creationTaskService.searchStaffCreationTaskList(jwtPayload, request);
    }

    /**
     * 搜索任务列表
     */
    @GetMapping("/search")
    public BaseResponse<BasePaginationResponse<CreationTaskDto>> searchCreationTaskList(
            @RequestAttribute JwtPayload jwtPayload,
            SearchCreationTaskListRequest request) {
        return creationTaskService.searchCreationTaskList(jwtPayload, request);
    }

    /**
     * 下发
     */
    @PostMapping("/id/{id:\\d+}/distribute")
    public BaseResponse<Boolean> distributeTask(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody DistributeTaskRequest request,
            @PathVariable Long id) {
        request.setId(id);
        return creationTaskService.distributeTask(jwtPayload, request);
    }

}
