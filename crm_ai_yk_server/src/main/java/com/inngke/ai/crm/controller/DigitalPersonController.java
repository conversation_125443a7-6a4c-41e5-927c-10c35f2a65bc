package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.dto.request.digital.person.CreateDigitalPersonRequest;
import com.inngke.ai.crm.dto.request.digital.person.GetDigitalPersonListRequest;
import com.inngke.ai.crm.dto.response.digital.person.DigitalPersonTagDto;
import com.inngke.ai.crm.dto.response.digital.person.DigitalPersonTemplateDto;
import com.inngke.ai.crm.service.DigitalPersonService;
import com.inngke.ai.dto.response.DigitalPersonVideoDto;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @chapter 数字人
 * @section 数字人接口
 */
@RestController
@RequestMapping("/api/ai/digital-person")
public class DigitalPersonController {

    @Autowired
    private DigitalPersonService digitalPersonService;

    /**
     * 生成数字人视频
     */
    @PostMapping("/video")
    public BaseResponse<Long> createDigitalPerson(
            @RequestBody CreateDigitalPersonRequest request) {
        return digitalPersonService.create(request);
    }

    /**
     * 获取数字人视频
     */
    @GetMapping("/video")
    public BaseResponse<DigitalPersonVideoDto> getDigitalPerson(@RequestParam Long id) {
        return digitalPersonService.getDigitalPerson(id);
    }

    /**
     * 获取数字人模版列表
     */
    @GetMapping("/template/list")
    public BaseResponse<List<DigitalPersonTemplateDto>> getDigitalPersonList(
            @RequestAttribute JwtPayload jwtPayload,
            GetDigitalPersonListRequest request) {
        return digitalPersonService.getDigitalPersonList(jwtPayload, request);
    }

    /**
     * 数字人详情
     */
    @GetMapping("/{id:\\d+}/template")
    public BaseResponse<DigitalPersonTemplateDto> getDigitalPersonInfo(
            @RequestAttribute JwtPayload jwtPayload, @PathVariable Long id) {
        return digitalPersonService.getDigitalPersonTemplate(id);
    }

    /**
     * 获取数字人标签列表
     */
    @GetMapping("/tag/list")
    public BaseResponse<List<DigitalPersonTagDto>> getDigitalPersonTagList() {
        return digitalPersonService.getDigitalPersonTagList();
    }

    /**
     * 获取人脸位置
     */
    @GetMapping("/face/position")
    public BaseResponse<List<Integer>> getFacePosition(@RequestParam("url") String url){
        return BaseResponse.success(digitalPersonService.getFaceCenterPosition(url));
    }
}
