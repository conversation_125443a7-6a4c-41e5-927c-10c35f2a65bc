package com.inngke.ai.crm.converter;

import com.inngke.ai.crm.db.crm.entity.CreationTask;
import com.inngke.ai.crm.db.crm.entity.CreationTaskStaffRelation;
import com.inngke.ai.crm.dto.enums.CreationTaskStaffStateEnum;
import com.inngke.ai.crm.dto.response.creation.task.StaffCreationTaskDto;
import com.inngke.ai.crm.dto.response.creation.task.StaffCreationTaskStateDto;
import com.inngke.common.utils.DateTimeUtils;

import java.util.Objects;
import java.util.Optional;

public class CreationTaskStaffConverter {
    public static StaffCreationTaskStateDto toStaffCreationTaskStateDto(CreationTaskStaffRelation creationTaskStaffRelation) {
        StaffCreationTaskStateDto staffCreationTaskStateDto = new StaffCreationTaskStateDto();
        staffCreationTaskStateDto.setId(creationTaskStaffRelation.getId());
        staffCreationTaskStateDto.setTaskId(creationTaskStaffRelation.getCreationTaskId());
        Optional.ofNullable(CreationTaskStaffStateEnum.parse(creationTaskStaffRelation.getState()))
                .ifPresent(state -> {
                    staffCreationTaskStateDto.setState(state.getState());
                    staffCreationTaskStateDto.setStateText(state.getName());
                });
        staffCreationTaskStateDto.setFinishedTime(DateTimeUtils.getMilli(creationTaskStaffRelation.getFinishTime()));
        return staffCreationTaskStateDto;

    }

    public static StaffCreationTaskDto toStaffCreationTaskDto(CreationTaskStaffRelation creationTaskStaffRelation, CreationTask creationTask) {
        StaffCreationTaskDto staffCreationTaskDto = new StaffCreationTaskDto();
        staffCreationTaskDto.setId(creationTaskStaffRelation.getCreationTaskId());
        Optional.ofNullable(CreationTaskStaffStateEnum.parse(creationTaskStaffRelation.getState()))
                .ifPresent(state -> {
                    staffCreationTaskDto.setState(state.getState());
                    staffCreationTaskDto.setStateText(state.getName());
                });
        if (Objects.nonNull(creationTask)){
            staffCreationTaskDto.setName(creationTask.getName());
            staffCreationTaskDto.setStartTime(DateTimeUtils.getMilli(creationTask.getStartTime()));
            staffCreationTaskDto.setEndTime(DateTimeUtils.getMilli(creationTask.getEndTime()));
            staffCreationTaskDto.setAiProduct(creationTask.getAiProduct());
            staffCreationTaskDto.setDescribe(creationTask.getTaskDesc());
        }
        return staffCreationTaskDto;
    }
}
