package com.inngke.ai.crm.converter;

import com.inngke.ai.crm.dto.response.AiGenerateTaskStatisticResponse;
import com.inngke.ai.crm.dto.response.CrmXiaoHongShuListDto;

/**
 * <AUTHOR>
 * @Date 2024/3/22 15:49
 */
public class XiaoHongShuDataConverter {
    
    public static CrmXiaoHongShuListDto toDetailDto(AiGenerateTaskStatisticResponse aiGenerateTaskStatisticResponse) {
        CrmXiaoHongShuListDto crmXiaoHongShuListDto = new CrmXiaoHongShuListDto();
        crmXiaoHongShuListDto.setReleaseTime(aiGenerateTaskStatisticResponse.getReleaseTime());
        crmXiaoHongShuListDto.setTitle(aiGenerateTaskStatisticResponse.getTitle());
        crmXiaoHongShuListDto.setStaffId(aiGenerateTaskStatisticResponse.getUserId());
        crmXiaoHongShuListDto.setStaffName(aiGenerateTaskStatisticResponse.getUserName());
        crmXiaoHongShuListDto.setDepartmentId(aiGenerateTaskStatisticResponse.getDepartmentId());
        crmXiaoHongShuListDto.setDepartmentName(aiGenerateTaskStatisticResponse.getDepartmentName());
        crmXiaoHongShuListDto.setViewCount(aiGenerateTaskStatisticResponse.getViewCount());
        crmXiaoHongShuListDto.setLikeCount(aiGenerateTaskStatisticResponse.getLikeCount());
        crmXiaoHongShuListDto.setCollectionCount(aiGenerateTaskStatisticResponse.getCollectionCount());
        crmXiaoHongShuListDto.setCommentCount(aiGenerateTaskStatisticResponse.getCommentCount());
        crmXiaoHongShuListDto.setXiaoHongShuNoteType(aiGenerateTaskStatisticResponse.getXiaoHongShuNoteType());
        crmXiaoHongShuListDto.setTaskCreateTime(aiGenerateTaskStatisticResponse.getTaskCreateTime());
        crmXiaoHongShuListDto.setReleaseStatus(aiGenerateTaskStatisticResponse.getReleaseStatus());
        crmXiaoHongShuListDto.setReleaseExternalId(aiGenerateTaskStatisticResponse.getReleaseExternalId());
        crmXiaoHongShuListDto.setCopyStatus(aiGenerateTaskStatisticResponse.getCopyStatus());

        return crmXiaoHongShuListDto;
    }
}
