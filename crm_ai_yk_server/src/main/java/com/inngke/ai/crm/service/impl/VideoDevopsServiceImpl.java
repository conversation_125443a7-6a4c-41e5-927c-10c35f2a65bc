package com.inngke.ai.crm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.ai.crm.db.crm.entity.AiGenerateVideoOutput;
import com.inngke.ai.crm.db.crm.entity.User;
import com.inngke.ai.crm.db.crm.entity.VideoQualityAudit;
import com.inngke.ai.crm.db.crm.manager.*;
import com.inngke.ai.crm.dto.request.video.VideoQualityAuditRequest;
import com.inngke.ai.crm.dto.request.video.VideoQuery4DevOpsRequest;
import com.inngke.ai.crm.service.VideoDevopsService;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.SnowflakeIdService;
import com.inngke.common.utils.DateTimeUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class VideoDevopsServiceImpl implements VideoDevopsService {

    @Autowired
    private SnowflakeIdService snowflakeIdService;

    @Autowired
    private UserManager userManager;

    @Autowired
    private VideoQualityAuditManager videoQualityAuditManager;

    @Autowired
    private AiGenerateVideoOutputManager aiGenerateVideoOutputManager;

    @Autowired
    private VideoMaterialManager videoMaterialManager;

    @Autowired
    private VideoCreateTaskManager videoCreateTaskManager;

    @Override
    public List<AiGenerateVideoOutput> videoList4Devops(VideoQuery4DevOpsRequest request) {
        String mobile = request.getMobile();
        Long userId = 0L;
        Long organizeId = request.getOrganizeId();
        if (!StringUtils.isEmpty(mobile)) {
            User user = userManager.getOne(
                    Wrappers.<User>query()
                            .eq(User.MOBILE, mobile)
                            .eq(organizeId != null && organizeId > 0, User.ORGANIZE_ID, organizeId)
                            .select(User.ID)
            );
            if (user == null) {
                return Lists.newArrayList();
            }
            userId = user.getId();
        }

        String createTimeStart = request.getCreateTimeStart();
        LocalDateTime timeStart = null;
        if (!StringUtils.isEmpty(createTimeStart)) {
            timeStart = DateTimeUtils.toLocalDateTime(createTimeStart);
        }
        String createTimeEnd = request.getCreateTimeEnd();
        LocalDateTime timeEnd = null;
        if (!StringUtils.isEmpty(createTimeEnd)) {
            timeEnd = DateTimeUtils.toLocalDateTime(createTimeEnd);
        }

        Integer qualityAudit = request.getQualityAudit();
        Boolean used = request.getUsed();
        Long lastId = request.getLastId();
        QueryWrapper<AiGenerateVideoOutput> filter = Wrappers.<AiGenerateVideoOutput>query()
                .eq(request.getTaskId() != null && request.getTaskId() > 0, AiGenerateVideoOutput.TASK_ID, request.getTaskId())
                .eq(userId != null && userId > 0, AiGenerateVideoOutput.CREATOR_ID, userId)
                .eq(organizeId != null && organizeId > 0, AiGenerateVideoOutput.ORGANIZE_ID, organizeId)
                .eq(qualityAudit != null, AiGenerateVideoOutput.QUALITY_AUDIT, qualityAudit)
                .eq(used != null, AiGenerateVideoOutput.USED, used)
                .ge(timeStart != null, AiGenerateVideoOutput.CREATE_TIME, timeStart)
                .lt(timeEnd != null, AiGenerateVideoOutput.CREATE_TIME, timeEnd)
                .lt(lastId != null && lastId > 0, AiGenerateVideoOutput.ID, lastId)
                .orderByDesc(AiGenerateVideoOutput.ID);

        List<AiGenerateVideoOutput> list = aiGenerateVideoOutputManager.list(filter.last(InngkeAppConst.STR_LIMIT + request.getPageSize()));
        Set<Long> taskIds = list.stream().map(AiGenerateVideoOutput::getTaskId).collect(Collectors.toSet());
        if (!CollectionUtils.isEmpty(taskIds)) {
            Map<Long, String> videoTaskHostMap = aiGenerateVideoOutputManager.list(
                    Wrappers.<AiGenerateVideoOutput>query()
                            .in(AiGenerateVideoOutput.TASK_ID, taskIds)
                            .select(AiGenerateVideoOutput.TASK_ID, AiGenerateVideoOutput.TASK_HOST)
            ).stream().collect(Collectors.toMap(AiGenerateVideoOutput::getTaskId, AiGenerateVideoOutput::getTaskHost));
            list.forEach(task -> {
                String host = videoTaskHostMap.get(task.getTaskId());
                if (StringUtils.hasLength(host)) {
                    task.setTaskHost(host);
                }
            });
        }
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean qualityAudit(JwtPayload jwtPayload, VideoQualityAuditRequest request) {
        Long videoGenerateId = request.getVideoGenerateId();
        AiGenerateVideoOutput aiGenerateVideoOutput = aiGenerateVideoOutputManager.getOne(
                Wrappers.<AiGenerateVideoOutput>query()
                        .eq(AiGenerateVideoOutput.ID, videoGenerateId)
                        .select(AiGenerateVideoOutput.ID)
        );
        if (aiGenerateVideoOutput == null) {
            throw new InngkeServiceException("视频不存在");
        }
        if (CollectionUtils.isEmpty(request.getItems())) {
            //没有意见
            aiGenerateVideoOutputManager.update(
                    Wrappers.<AiGenerateVideoOutput>update()
                            .eq(AiGenerateVideoOutput.ID, videoGenerateId)
                            .set(AiGenerateVideoOutput.QUALITY_AUDIT, 1)
            );
            videoQualityAuditManager.update(
                    Wrappers.<VideoQualityAudit>update()
                            .eq(VideoQualityAudit.AI_GENERATE_VIDEO_OUTPUT_ID, videoGenerateId)
                            .set(VideoQualityAudit.STATUS, -1)
            );
            return true;
        }

        Map<Long, VideoQualityAudit> audioMap = getQualityAuditList(videoGenerateId, true).stream().collect(Collectors.toMap(VideoQualityAudit::getId, Function.identity()));
        List<VideoQualityAudit> newVideoQualityAudits = Lists.newArrayList();
        request.getItems().forEach(audio -> {
            if (audio.getId() != null && audio.getId() > 0) {
                VideoQualityAudit existsAudio = audioMap.get(audio.getId());
                if (existsAudio == null) {
                    throw new InngkeServiceException("视频质量审核意见不存在，id=" + audio);
                }
                existsAudio.setStatus(1);
                existsAudio.setMessage(audio.getMessage());
                videoQualityAuditManager.update(
                        Wrappers.<VideoQualityAudit>update()
                                .eq(VideoQualityAudit.ID, audio.getId())
                                .set(VideoQualityAudit.STATUS, 1)
                                .set(VideoQualityAudit.MESSAGE, audio.getMessage())
                                .set(VideoQualityAudit.SCRIPT_INDEX, audio.getScriptIndex())
                                .set(VideoQualityAudit.VIDEO_TIME, audio.getVideoTime())
                                .set(VideoQualityAudit.TYPE, audio.getType())
                );

                audioMap.remove(audio.getId());
            } else {
                //新增
                VideoQualityAudit item = new VideoQualityAudit();
                item.setId(snowflakeIdService.getId());
                item.setAiGenerateVideoOutputId(videoGenerateId);
                item.setScriptIndex(audio.getScriptIndex());
                item.setVideoTime(audio.getVideoTime());
                item.setMessage(audio.getMessage());
                item.setAudioUserId(jwtPayload.getCid());
                item.setStatus(1);
                item.setType(audio.getType());
                item.setCreateTime(LocalDateTime.now());
                newVideoQualityAudits.add(item);
            }
        });
        if (!CollectionUtils.isEmpty(newVideoQualityAudits)) {
            videoQualityAuditManager.saveBatch(newVideoQualityAudits);
        }
        List<Long> removeIds = audioMap.values()
                .stream()
                .filter(item -> item.getId() != null && item.getId() > 0)
                .map(VideoQualityAudit::getId)
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(removeIds)) {
            videoQualityAuditManager.update(
                    Wrappers.<VideoQualityAudit>update()
                            .in(VideoQualityAudit.ID, removeIds)
                            .set(VideoQualityAudit.STATUS, -1)
            );
        }

        videoQualityAuditManager.update(
                Wrappers.<VideoQualityAudit>update()
                        .eq(VideoQualityAudit.AI_GENERATE_VIDEO_OUTPUT_ID, videoGenerateId)
                        .set(VideoQualityAudit.STATUS, 1)
        );
        aiGenerateVideoOutputManager.update(
                Wrappers.<AiGenerateVideoOutput>update()
                        .eq(AiGenerateVideoOutput.ID, videoGenerateId)
                        .set(AiGenerateVideoOutput.QUALITY_AUDIT, -1)
        );
        return true;
    }

    @Override
    public List<VideoQualityAudit> getQualityAuditList(Long videoGenerateId, boolean includeDeleted) {
        return videoQualityAuditManager.list(
                Wrappers.<VideoQualityAudit>query()
                        .eq(VideoQualityAudit.AI_GENERATE_VIDEO_OUTPUT_ID, videoGenerateId)
                        .eq(!includeDeleted, VideoQualityAudit.STATUS, 1)
        );
    }

    @Override
    public Boolean fixCount() {
        aiGenerateVideoOutputManager.list(
                Wrappers.<AiGenerateVideoOutput>query()
                        .ne(AiGenerateVideoOutput.MATERIAL_IDS, InngkeAppConst.EMPTY_STR)
                        .isNotNull(AiGenerateVideoOutput.MATERIAL_IDS)
                        .select(AiGenerateVideoOutput.MATERIAL_IDS)
        ).forEach(item -> {
            List<Long> materialIds = Arrays.stream(item.getMaterialIds().split(InngkeAppConst.COMMA_STR)).map(Long::valueOf).collect(Collectors.toList());
            videoMaterialManager.materialUse(materialIds);
        });
        return true;
    }
}
