package com.inngke.ai.crm.service.video.creator.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.inngke.ai.crm.controller.VideoProjectDraftApiController;
import com.inngke.ai.crm.core.CrmServiceConsts;
import com.inngke.ai.crm.core.util.CrmUtils;
import com.inngke.ai.crm.core.util.DifyUtils;
import com.inngke.ai.crm.db.crm.entity.*;
import com.inngke.ai.crm.db.crm.manager.*;
import com.inngke.ai.crm.dto.SimpleFormDto;
import com.inngke.ai.crm.dto.enums.AppConfigCodeEnum;
import com.inngke.ai.crm.dto.enums.VideoCreationStageEnum;
import com.inngke.ai.crm.dto.enums.VideoDraftTypeEnum;
import com.inngke.ai.crm.dto.form.CategorySelectOption;
import com.inngke.ai.crm.dto.request.video.VideoCreateWithMaterialRequest;
import com.inngke.ai.crm.dto.response.video.VideoCreateResult;
import com.inngke.ai.crm.dto.response.video.VideoProjectDraftDetail;
import com.inngke.ai.crm.service.DifyAppConfService;
import com.inngke.ai.crm.service.DifyService;
import com.inngke.ai.crm.service.cache.FreeFormConfigCache;
import com.inngke.ai.crm.service.form.dynamic.VideoAudioFormHandler;
import com.inngke.ai.crm.service.impl.BgmServiceImpl;
import com.inngke.ai.crm.service.video.creator.VideoCreatorService;
import com.inngke.ai.dto.OralVideoMaterial;
import com.inngke.ai.dto.SubtitleDto;
import com.inngke.ai.dto.VideoMaterialItem;
import com.inngke.ai.dto.enums.DigitalPersonDisplayEnum;
import com.inngke.ai.dto.request.*;
import com.inngke.ai.dto.utils.FormDataUtils;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.JsonService;
import com.inngke.common.service.SnowflakeIdService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class VideoCreateStoryboardServiceImpl implements VideoCreatorService {

    public static final int DEFAULT_TEXT_STYLE = 160; //白色黑边
    public static final int DEFAULT_FONT_SIZE = 18;
    public static final String DEFAULT_FONT_NAME = "抖音美好体";

    @Autowired
    private DifyAppConfService difyAppConfService;
    @Autowired
    private SnowflakeIdService snowflakeIdService;
    @Value("${server.url:}")
    private String aiServer;
    @Autowired
    private JsonService jsonService;
    @Autowired
    private JianyingResourceManager jianyingResourceManager;
    @Autowired
    private DifyService difyService;
    @Autowired
    private AppConfigManager appConfigManager;
    @Autowired
    private VideoBgmMaterialManager videoBgmMaterialManager;
    @Autowired
    private TtsConfigManager ttsConfigManager;
    @Autowired
    private MaterialCategoryManager materialCategoryManager;
    @Autowired
    private FreeFormConfigCache freeFormConfigCache;

    @Override
    public List<VideoDraftTypeEnum> getCreateType() {
        return Lists.newArrayList(VideoDraftTypeEnum.STORYBOARD, VideoDraftTypeEnum.MASHUP, VideoDraftTypeEnum.VIDEO_MASHUP);
    }

    @Override
    public VideoProjectDraftDetail genDraftScript(JwtPayload jwtPayload, VideoCreateWithMaterialRequest request) {
        return null;
    }

    @Override
    public VideoCreateResult createVideo(JwtPayload jwtPayload, VideoCreateWithMaterialRequest request) {
        return null;
    }

    @Override
    public VideoGenerateRequest buildVideoGenerateRequest(VideoCreateWithMaterialRequest request, Staff staff) {
        VideoGenerateRequest videoRequest = new VideoGenerateRequest().setVersion((int) (System.currentTimeMillis() / 1000)).setUserId(staff.getUserId()).setOrganizeId(staff.getOrganizeId()).setDraftId(request.getDraftId());

        Map<String, Object> promptMap = Optional.ofNullable(request.getPromptMap()).orElse(Maps.newHashMap());
        int appId = FormDataUtils.getInt(promptMap, FormDataUtils.FORM_KEY_APP_ID, 0);
        DifyAppConf difyApp = null;
        if (appId > 0) {
            difyApp = difyAppConfService.getVideoAppConf(appId);
            if (difyApp == null) {
                throw new InngkeServiceException("无法找到视频类型");
            }
        }

        videoRequest.setFormQuery(promptMap);

        setTaskId(videoRequest, request);

        VideoCreationStageEnum stage = Optional.ofNullable(VideoCreationStageEnum.getByCode(request.getStage())).orElse(VideoCreationStageEnum.FINISH_VIDEO);
        videoRequest.setStage(stage.getCode());

        videoRequest.setVideoLength(FormDataUtils.getInt(promptMap, FormDataUtils.FORM_KEY_LENGTH, 45));

        //设置AI服务地址
        videoRequest.setAiServer(StringUtils.isNotBlank(aiServer) ? aiServer : CrmUtils.getAiServerUrl());

        //设置分镜配置
        videoRequest.setScripts(request.getScripts());

        //字幕
        getSubtitle(request, videoRequest);

        String formConfigStr = difyApp == null ? null : difyApp.getFormColumnConfig2();
//        if (StringUtils.isEmpty(formConfigStr)) {
//            throw new InngkeServiceException("无法找到表单配置");
//        }
        Set<String> formKeys = StringUtils.isNotBlank(formConfigStr) ? jsonService.toObjectList(formConfigStr, SimpleFormDto.class).stream().map(SimpleFormDto::getKey).collect(Collectors.toSet()) : Sets.newHashSet();

        //设置 封面标题、封面图
        if (formKeys.contains(FormDataUtils.FORM_KEY_COVER_TITLE)) {
            setVideoCoverConfig(videoRequest);
        }

        //不在 创建内容 & 跳过阶段
        if (stage != VideoCreationStageEnum.CREATE_CONTENT_BY_VIDEO_KEY && stage != VideoCreationStageEnum.SKIP_SCRIPT && stage != VideoCreationStageEnum.CREATE_CONTENT_BY_USER_SCRIPT) {

            //字幕、字幕样式
            setSubtitleConfig(videoRequest);

            //素材原声
            setOriginAudio(videoRequest);

            //BGM，未设置为 null
            setBgmConfig(videoRequest, staff);

            //大字报
            if (formKeys.contains(FormDataUtils.FORM_KEY_VIDEO_WORD)) {
                setVideBigTitleConfig(videoRequest);
            }

            //数字人 & 音色配置
            setDigitalHumanConfigs(request, videoRequest);
            //检查素材库配置 （某些企业不需要必填素材库）
            //小程序不需要指定素材库
            if (Objects.isNull(request.getDraftId()) || request.getDraftId() == 0L) {
                Set<Long> sceneryShotCateIds = FormDataUtils.getCategoryIds(promptMap, FormDataUtils.FORM_KEY_CATEGORY_IDS);
                if (sceneryShotCateIds.isEmpty()) {
                    promptMap.put(FormDataUtils.FORM_KEY_CATEGORY_IDS, getOrganizeAllCategory(staff.getOrganizeId()));
                }
            } else {
                assertMaterialCategorySetting(request, videoRequest);
            }
        }

        //设置query
        setQuery(videoRequest, difyApp);

        //设置实景视频
        setRealVideos(videoRequest);

        //自选dify应用的appKey
        DifyConfig difyConfig = new DifyConfig().setUserId(difyService.getUser(staff.getUserId())).setApiKey(difyApp.getAppKey()).setWorkflow(Optional.ofNullable(difyApp.getWorkflow()).orElse(false));
        videoRequest.setDify(difyConfig);
        videoRequest.setVideoTypeName(difyApp.getName());

        //前帖、后帖视频
        videoRequest.setBeforeScript(request.getBeforeScript());
        videoRequest.setAfterScript(request.getAfterScript());

        if (Objects.isNull(request.getVideoCategoryId())) {
            String categoryId = FormDataUtils.getString(promptMap, "videoCategoryId");
            if (StringUtils.isNotBlank(categoryId)) {
                request.setVideoCategoryId(Long.valueOf(categoryId));
            }
        }

        videoRequest.setRoles(request.getRoles());

        //搜索类型 画面描述，字幕，混合
        String searchType = appConfigManager.getValueByCode(AppConfigCodeEnum.FORM_KEY_SEARCH_TYPE_VALUE.getCode());
        Optional.ofNullable(searchType).map(Integer::valueOf)
                .ifPresent(type -> videoRequest.getFormQuery().put(FormDataUtils.FORM_KEY_SEARCH_TYPE, type));

        // 设置贴片
        setVideoWidget(request, videoRequest);

        // 新版大字报
        videoRequest.setBigTitleConfig(request.getBigTitleConfig());

        return videoRequest;
    }

    private void setTaskId(VideoGenerateRequest videoRequest, VideoCreateWithMaterialRequest request) {
        Long draftId = request.getDraftId();
        Long taskId = request.getTaskId();
        Integer stage = request.getStage();
        if (draftId != null && draftId > 0) {
            // PC 版
            if (stage == null || VideoCreationStageEnum.FINISH_VIDEO.getCode().equals(stage)) {
                //完成创作，需要重新设置一个taskId
                taskId = null;
            }
        }
        if (taskId == null || taskId <= 0) {
            taskId = snowflakeIdService.getId();
        }
        request.setTaskId(taskId);
        videoRequest.setTaskId(taskId);
    }

    private static void getSubtitle(VideoCreateWithMaterialRequest request, VideoGenerateRequest videoRequest) {
        List<SubtitleDto> subtitles = request.getSubtitles();
        if (CollectionUtils.isEmpty(subtitles)) {
            return;
        }
        subtitles = subtitles.stream().filter(subtitle -> StringUtils.isNotBlank(subtitle.getText())).collect(Collectors.toList());
        videoRequest.setSubtitles(subtitles);
    }

    private void setVideoCoverConfig(VideoGenerateRequest videoRequest) {
        Map<String, Object> promptMap = videoRequest.getFormQuery();

        //封面标题
        String title = (String) promptMap.get(FormDataUtils.FORM_KEY_COVER_TITLE);
        if (StringUtils.isBlank(title)) {
            setVideoCoverImageConfig(videoRequest);
            return;
        }

        SubtitleConfig subtitleConfig = videoRequest.getSubtitleConfig();
        VideoCoverConfig coverConfig = getBaseSubtitleConfig(promptMap, VideoCoverConfig.class, subtitleConfig);
        if (coverConfig == null) {
            setVideoCoverImageConfig(videoRequest);
            return;
        }
        coverConfig.setContentText(title).setTimeBegin(0).setTimeEnd(10);

        if (!promptMap.containsKey(FormDataUtils.FORM_KEY_COVER_TEXT_STYLE)) {
            //未指定封面标题样式，则随机
            int jyTextResourceId = jianyingResourceManager.random("text");
            coverConfig.setStyleResourceId(jyTextResourceId);
        }
        videoRequest.setCover(coverConfig);

        //封面图
        setVideoCoverImageConfig(videoRequest);
    }

    private void setVideoCoverImageConfig(VideoGenerateRequest videoRequest) {
        Map<String, Object> promptMap = videoRequest.getFormQuery();
        VideoCoverConfig coverConfig = videoRequest.getCover();
        String coverImageUrl = (String) promptMap.get(FormDataUtils.FORM_KEY_COVER_IMG);
        if (StringUtils.isBlank(coverImageUrl)) {
            return;
        }
        if (coverConfig == null) {
            coverConfig = new VideoCoverConfig();
            videoRequest.setCover(coverConfig);
        }
        coverConfig.setImageUrl(coverImageUrl);
    }

    public static <T extends SubtitleConfig> T getBaseSubtitleConfig(Map<String, Object> promptMap, Class<T> clazz, SubtitleConfig defaultConfig) {
        String textStyleKey, fontSizeKey, fontNameKey;
        if (clazz == SubtitleConfig.class) {
            textStyleKey = FormDataUtils.FORM_KEY_SUBTITLE_TEXT_STYLE;
            fontSizeKey = FormDataUtils.FORM_KEY_SUBTITLE_FONT_SIZE;
            fontNameKey = FormDataUtils.FORM_KEY_SUBTITLE_FONT_NAME;
        } else if (clazz == VideoBigTitleConfig.class) {
            textStyleKey = FormDataUtils.FORM_KEY_BIG_TITLE_TEXT_STYLE;
            fontSizeKey = FormDataUtils.FORM_KEY_BIG_TITLE_FONT_SIZE;
            fontNameKey = FormDataUtils.FORM_KEY_BIG_TITLE_FONT_NAME;
        } else if (clazz == VideoCoverConfig.class) {
            textStyleKey = FormDataUtils.FORM_KEY_COVER_TEXT_STYLE;
            fontSizeKey = FormDataUtils.FORM_KEY_COVER_FONT_SIZE;
            fontNameKey = FormDataUtils.FORM_KEY_COVER_FONT_NAME;
        } else {
            return null;
        }

        String fontSizeStr = FormDataUtils.getString(promptMap, fontSizeKey, InngkeAppConst.EMPTY_STR);
        String fontName = FormDataUtils.getString(promptMap, fontNameKey, InngkeAppConst.EMPTY_STR);
        String textStyleStr = FormDataUtils.getString(promptMap, textStyleKey, InngkeAppConst.EMPTY_STR);
        int subtitlePositionY = FormDataUtils.getInt(promptMap, FormDataUtils.FORM_KEY_SUBTITLE_POSITION_Y, -200);

        int fontSize = DEFAULT_FONT_SIZE;
        if (StringUtils.isEmpty(fontSizeStr)) {
            if (defaultConfig != null && defaultConfig.getFontSize() != null) {
                fontSize = defaultConfig.getFontSize();
                if (fontSize == 0) {
                    //大字报或封面图
                    fontSize = DEFAULT_FONT_SIZE;
                }
            }
        } else {
            fontSize = Integer.parseInt(fontSizeStr);
        }

        if (StringUtils.isEmpty(fontName)) {
            if (defaultConfig != null && defaultConfig.getFontName() != null) {
                fontName = defaultConfig.getFontName();
            } else {
                fontName = DEFAULT_FONT_NAME;
            }
        }

        int subtitleConfigId = DEFAULT_TEXT_STYLE;
        if (StringUtils.isEmpty(textStyleStr)) {
            if (defaultConfig != null && defaultConfig.getStyleResourceId() != null) {
                subtitleConfigId = defaultConfig.getStyleResourceId();
            }
        } else {
            subtitleConfigId = Integer.parseInt(textStyleStr);
        }

        T config = BeanUtils.instantiateClass(clazz);
        config.setDisplay(fontSize != 0);
        config.setFontName(fontName);
        config.setFontSize(fontSize);
        config.setStyleResourceId(subtitleConfigId);
        config.setPositionY(subtitlePositionY);
        return config;
    }

    private void setSubtitleConfig(VideoGenerateRequest videoRequest) {
        Map<String, Object> promptMap = videoRequest.getFormQuery();
        String subtitleContent = promptMap.getOrDefault(FormDataUtils.FORM_KEY_ASIDES, InngkeAppConst.EMPTY_STR).toString();
        if (StringUtils.isBlank(subtitleContent)) {
            throw new InngkeServiceException("字幕内容未设置！");
        }
        SubtitleConfig subtitleConfig = getBaseSubtitleConfig(promptMap, SubtitleConfig.class, null);
        if (subtitleConfig == null) {
            throw new InngkeServiceException("字幕样式设置失败！");
        }
        int effectLevel = FormDataUtils.getInt(promptMap, FormDataUtils.FORM_KEY_EFFECT_LEVEL, 0);
        subtitleConfig.setEffectLevel(effectLevel);

        subtitleConfig.setContentText(subtitleContent);


        boolean subtitleOff = FormDataUtils.getBoolean(promptMap, FormDataUtils.FORM_KEY_SUBTITLE_OFF, false);
        subtitleConfig.setDisplay(!subtitleOff);

        //设置字幕位置
        subtitleConfig.setPositionY(FormDataUtils.getInt(promptMap, FormDataUtils.FORM_KEY_SUBTITLE_POSITION_Y, -200));

        videoRequest.setSubtitleConfig(subtitleConfig);
    }

    private void setOriginAudio(VideoGenerateRequest videoRequest) {
        Map<String, Object> promptMap = videoRequest.getFormQuery();
        boolean originAudio = Boolean.parseBoolean(Optional.ofNullable(promptMap.get(FormDataUtils.FORM_KEY_ORIGIN_AUDIO)).orElse("false").toString());
        videoRequest.setOriginAudio(originAudio);
    }

    private void setBgmConfig(VideoGenerateRequest videoRequest, Staff staff) {
        long organizeId = videoRequest.getOrganizeId();
        Map<String, Object> promptMap = videoRequest.getFormQuery();

        boolean bgmOpen = FormDataUtils.getBoolean(promptMap, FormDataUtils.FORM_KEY_BGM_OPEN, true);
        if (!bgmOpen) {
            //未开启BGM
            return;
        }

        String chooseBgm = Optional.ofNullable(promptMap.get(FormDataUtils.FORM_KEY_CHOOSE_MUSIC)).orElse(InngkeAppConst.EMPTY_STR).toString();
        long bgmId = StringUtils.isBlank(chooseBgm) ? 0L : Long.parseLong(chooseBgm);
        VideoBgmMaterial videoBgmMaterial;
        if (bgmId == 0) {
            //未指定
            String bgmLevelStr = Optional.ofNullable(promptMap.get(FormDataUtils.FORM_KEY_BGM_TYPE)).orElse(InngkeAppConst.EMPTY_STR).toString();
            int bgmType = StringUtils.isBlank(bgmLevelStr) ? 1 : Integer.parseInt(bgmLevelStr);
            Long userId = 0L;
            if (BgmServiceImpl.MY_MUSIC_TYPE.equals(bgmType)) {
                userId = staff.getUserId();
            }
            videoBgmMaterial = videoBgmMaterialManager.random(organizeId, userId, bgmType);
        } else {
            videoBgmMaterial = videoBgmMaterialManager.getById(bgmId);
        }
        if (videoBgmMaterial == null) {
            return;
        }
        double volume = getGbmVolume(videoRequest, videoBgmMaterial);
        VideoBgmConfig bgmConfig = new VideoBgmConfig();
        String bgmUrl = videoBgmMaterial.getOptimizedUrl();
        if (StringUtils.isBlank(bgmUrl)) {
            bgmUrl = videoBgmMaterial.getUrl();
        }
        bgmConfig.setId(videoBgmMaterial.getId());
        bgmConfig.setUrl(bgmUrl);
        bgmConfig.setStyle(videoBgmMaterial.getType());
        Integer duration = videoBgmMaterial.getClipDuration();
        if (duration == null || duration.equals(0)) {
            duration = videoBgmMaterial.getDuration();
        }
        Integer clipStart = videoBgmMaterial.getClipStart();
        if (clipStart == null) {
            clipStart = 0;
        }
        bgmConfig.setDuration(duration);
        bgmConfig.setStart(clipStart);
        bgmConfig.setVolume(volume);
        videoRequest.setBgm(bgmConfig);
    }

    private void setVideBigTitleConfig(VideoGenerateRequest videoRequest) {
        Map<String, Object> promptMap = videoRequest.getFormQuery();
        SubtitleConfig subtitleConfig = videoRequest.getSubtitleConfig();
        String videoWord = FormDataUtils.getString(promptMap, FormDataUtils.FORM_KEY_VIDEO_WORD, InngkeAppConst.EMPTY_STR);
        if (StringUtils.isBlank(videoWord)) {
            //没有大字报
            return;
        }

        VideoBigTitleConfig config = getBaseSubtitleConfig(promptMap, VideoBigTitleConfig.class, subtitleConfig);
        if (config == null) {
            return;
        }
        config.setContentText(videoWord);
        videoRequest.setBigTitle(config);
    }

    private void setDigitalHumanConfigs(VideoCreateWithMaterialRequest request, VideoGenerateRequest videoRequest) {
        int digitalPersonSwitch = getDigitalPersonSwitch(request);
        Map<String, Object> promptMap = request.getPromptMap();
        boolean digitalPersonOpen = FormDataUtils.getBoolean(promptMap, FormDataUtils.FORM_KEY_DIGITAL_PERSON_OPEN, false);
        boolean audioOn = FormDataUtils.getBoolean(promptMap, FormDataUtils.FORM_KEY_AUDIO_OPEN, true);

        List<VideoDigitalHumanConfig> digitalHumanConfigs = request.getDigitalHumanConfigs();
        if (!CollectionUtils.isEmpty(digitalHumanConfigs)) {
            Map<Integer, VideoAudioConfig> audioMap = Maps.newHashMap();
            digitalHumanConfigs.forEach(item -> {
                VideoAudioConfig audioConfig = item.getAudioConfig();
                if (!digitalPersonOpen) {
                    item.setTemplateId(null);
                }
                audioMap.put(audioConfig.getId(), audioConfig);
            });
            //完善数据
            ttsConfigManager.list(Wrappers.<TtsConfig>query().in(TtsConfig.ID, audioMap.keySet())).forEach(ttsConfig -> {
                VideoAudioConfig audioConfig = audioMap.get(ttsConfig.getId());
                audioConfig.setVoice(audioOn ? ttsConfig.getVoiceType() : VideoAudioConfig.NO_VOICE_VALUE);
                audioConfig.setStyle(ttsConfig.getStyle());
                audioConfig.setPlatform(ttsConfig.getPlatform());
            });
            videoRequest.setDigitalHumanConfigs(digitalHumanConfigs);
            return;
        }

        //小程序场景：尝试从 promptMap 中提取信息
        VideoDigitalHumanConfig digitalHumanConfig = new VideoDigitalHumanConfig().setAudioConfig(getAudioConfig(promptMap));
        if (digitalPersonSwitch > 0) {
            //有数字人
            long digitalPersonTemplateId = FormDataUtils.getLong(promptMap, FormDataUtils.FORM_KEY_DIGITAL_PERSON_TEMPLATE_ID, -1);
            digitalHumanConfig.setTemplateId(digitalPersonTemplateId);
        }
        videoRequest.setDigitalHumanConfigs(Lists.newArrayList(digitalHumanConfig));
    }

    private Set<CategorySelectOption> getOrganizeAllCategory(Long organizeId) {
        List<MaterialCategory> materialCategoryList = materialCategoryManager.getByOrganizeIdType(organizeId, MaterialCategory.TYPE_VIDEO);

        return materialCategoryList.stream().map(cate -> {
            CategorySelectOption categorySelectOption = new CategorySelectOption();
            categorySelectOption.setValue(cate.getId().toString());
            categorySelectOption.setTitle(cate.getName());
            return categorySelectOption;
        }).collect(Collectors.toSet());
    }

    private void assertMaterialCategorySetting(VideoCreateWithMaterialRequest request, VideoGenerateRequest videoRequest) {
        Map<String, Object> promptMap = request.getPromptMap();
        int videoType = FormDataUtils.getInt(promptMap, FormDataUtils.FORM_KEY_VIDEO_TYPE, 1);
        if (videoType == 2) {
            //实拍视频，可以没有空镜素材库
            return;
        }

        Set<Long> sceneryShotCateIds = FormDataUtils.getCategoryIds(promptMap, FormDataUtils.FORM_KEY_CATEGORY_IDS);
        if (!sceneryShotCateIds.isEmpty()) {
            //已经指定了空镜素材库
            return;
        }

        List<OralVideoMaterial> oralVideoMaterialList = FormDataUtils.getOralVideoMaterialList(promptMap, FormDataUtils.FORM_KEY_ORAL_VIDEO_MATERIAL);
        if (!CollectionUtils.isEmpty(oralVideoMaterialList)) {
            //指定了素材口播素材
            return;
        }

        Set<Long> scenePathIds = FormDataUtils.getCategoryIds(promptMap, FormDataUtils.FORM_KEY_SCENE_PATH_IDS);
        if (scenePathIds.isEmpty()) {
            throw new InngkeServiceException("未指定素材库！");
        }
    }

    private void setQuery(VideoGenerateRequest videoRequest, DifyAppConf difyApp) {
        int stage = videoRequest.getStage();
        if (!VideoCreationStageEnum.SKIP_SCRIPT.getCode().equals(stage)) {
            //除了跳过，合成Query内容
            if (difyApp == null) {
                throw new InngkeServiceException("未指定应用类型！");
            }
            Map<String, Object> promptMap = videoRequest.getFormQuery();
            String query = mergeDifyRequest(difyApp.getName(), difyApp.getFormColumnConfig(), promptMap);
            videoRequest.setQuery(query);
        } else if (VideoCreationStageEnum.CREATE_CONTENT_BY_USER_SCRIPT.getCode().equals(stage)) {
            // 通过自定义脚本，生成视频内容
            //字幕人工编排
            videoRequest.setQuery("视频字幕：\n" + videoRequest.getAsides());
        }
    }

    private static void setRealVideos(VideoGenerateRequest videoRequest) {
        Map<String, Object> promptMap = videoRequest.getFormQuery();
        List<Map<String, Object>> originalVideos = (List) promptMap.get(FormDataUtils.FORM_KEY_ORIGINAL_VIDEO);
        if (!CollectionUtils.isEmpty(originalVideos)) {
            List<VideoMaterialItem> realVideos = originalVideos.stream().map(item -> {
                String url = (String) item.get("url");
                if (StringUtils.isEmpty(url)) {
                    return null;
                }
                Integer duration = null;
                String durationStr = Optional.ofNullable(item.get("duration")).orElse(InngkeAppConst.EMPTY_STR).toString();
                if (StringUtils.isNotBlank(durationStr)) {
                    duration = Math.toIntExact(Math.round(Double.parseDouble(durationStr) * 1000));
                }
                VideoMaterialItem videoMaterial = new VideoMaterialItem();
                videoMaterial.setUrl(url);
                videoMaterial.setDuration(duration);
                videoMaterial.setClipStart(0);
                videoMaterial.setClipDuration(duration);
                videoMaterial.setWidth((Integer) item.get("width"));
                videoMaterial.setHeight((Integer) item.get("height"));
                return videoMaterial;
            }).filter(video -> video != null && video.getUrl() != null).collect(Collectors.toList());
            videoRequest.setRealVideos(realVideos);
        }
    }

    private void setVideoWidget(VideoCreateWithMaterialRequest request, VideoGenerateRequest videoRequest) {
        videoRequest.setWidgets(request.getWidgets());
    }

    private double getGbmVolume(VideoGenerateRequest request, VideoBgmMaterial videoBgmMaterial) {
        if (videoBgmMaterial == null || videoBgmMaterial.getVolume() == 0) {
            return -8.0;
        }
        //统一调整到 -30dB
        return (-3000 - videoBgmMaterial.getVolume()) / 100.0;
    }

    private int getDigitalPersonSwitch(VideoCreateWithMaterialRequest request) {
        Map<String, Object> promptMap = request.getPromptMap();
        return FormDataUtils.getInt(promptMap, FormDataUtils.FORM_KEY_DIGITAL_PERSON_SWITCH, DigitalPersonDisplayEnum.NO_DIGITAL_PERSON.getCode());
    }

    private VideoAudioConfig getAudioConfig(Map<String, Object> promptMap) {
        int asideConfigId = FormDataUtils.getInt(promptMap, VideoAudioFormHandler.STR_ASIDE_CONFIG, 0);
        if (asideConfigId == 0) {
            //无配音
            VideoAudioConfig videoAsideConfig = new VideoAudioConfig();
            videoAsideConfig.setId(0);
            videoAsideConfig.setVoice(VideoAudioConfig.NO_VOICE_VALUE);
            return videoAsideConfig;
        }
        if (asideConfigId == -1) {
            //无配音
            VideoAudioConfig audioConfig = new VideoAudioConfig();
            audioConfig.setId(0);
            audioConfig.setVoice(VideoAudioConfig.NO_VOICE_VALUE);
            return audioConfig;
        }
        TtsConfig ttsConfig = ttsConfigManager.getOne(Wrappers.<TtsConfig>query().eq(TtsConfig.ID, asideConfigId).eq(TtsConfig.STATUS, 1));
        if (ttsConfig == null) {
            ttsConfig = ttsConfigManager.random();

            if (ttsConfig == null) {
                throw new InngkeServiceException("语音配置不存在");
            }
        }

        return toVideoAudioConfig(ttsConfig);
    }

    private static VideoAudioConfig toVideoAudioConfig(TtsConfig ttsConfig) {
        VideoAudioConfig VideoAudioConfig = new VideoAudioConfig();
        VideoAudioConfig.setId(ttsConfig.getId());
        VideoAudioConfig.setVoice(ttsConfig.getVoiceType());
        VideoAudioConfig.setStyle(ttsConfig.getStyle());
        VideoAudioConfig.setPlatform(ttsConfig.getPlatform());
        VideoAudioConfig.setSpeedRatio((int) (ttsConfig.getSpeedRatio() * 100));
        VideoAudioConfig.setVolume(0.0);
        return VideoAudioConfig;
    }

    protected String mergeDifyRequest(String appName, String formConfig, Map inputs) {
        Set<String> excludeKeys = freeFormConfigCache.get();
        return DifyUtils.getDifyQuery(appName, formConfig, inputs, excludeKeys);
    }
}
