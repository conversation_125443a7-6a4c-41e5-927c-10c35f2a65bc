package com.inngke.ai.crm.dto.request;

import java.io.Serializable;

public class CustomerFollowListRequest implements Serializable {
    /**
     * 租户ID
     *
     * @demo 1
     */
    private Integer tenantId;

    /**
     * 客户ID，即customer.id
     *
     * @demo 12332123
     */
    private Long customerId;

    /**
     * 员工ID，即staff.id
     *
     * @demo 12332123
     */
    private Long staffId;

    /**
     * 最小的跟进记录ID，如果有值时，表示查询小于此ID的数据
     *
     * @demo 123
     */
    private Long minCustomerFollowId;

    public Integer getTenantId() {
        return tenantId;
    }

    public void setTenantId(Integer tenantId) {
        this.tenantId = tenantId;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public Long getMinCustomerFollowId() {
        return minCustomerFollowId;
    }

    public void setMinCustomerFollowId(Long minCustomerFollowId) {
        this.minCustomerFollowId = minCustomerFollowId;
    }
}
