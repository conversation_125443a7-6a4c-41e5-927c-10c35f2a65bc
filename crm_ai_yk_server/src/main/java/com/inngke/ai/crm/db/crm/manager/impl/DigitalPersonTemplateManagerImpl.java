/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager.impl;

import com.inngke.ai.crm.db.crm.entity.DigitalPersonTemplate;
import com.inngke.ai.crm.db.crm.dao.DigitalPersonTemplateDao;
import com.inngke.ai.crm.db.crm.manager.DigitalPersonTemplateManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-10
 */
@Service
public class DigitalPersonTemplateManagerImpl extends ServiceImpl<DigitalPersonTemplateDao, DigitalPersonTemplate> implements DigitalPersonTemplateManager {

    @Autowired
    private DigitalPersonTemplateDao digitalPersonTemplateDao;

    @Override
    public DigitalPersonTemplate getDeletedById(Long id) {
        return digitalPersonTemplateDao.getDeletedById(id);
    }
}
