package com.inngke.ai.crm.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.inngke.ai.crm.api.browser.dto.BrowserTaskRequest;
import com.inngke.ai.crm.api.xhs.FrontCanvasApi;
import com.inngke.ai.crm.api.xhs.XiaoHongShuPublishApi;
import com.inngke.ai.crm.api.xhs.dto.*;
import com.inngke.ai.crm.client.common.GeoServiceForAi;
import com.inngke.ai.crm.client.common.UploadServiceForAi;
import com.inngke.ai.crm.converter.XiaoHongShuDataConverter;
import com.inngke.ai.crm.core.CrmServiceConsts;
import com.inngke.ai.crm.core.config.AiGcConfig;
import com.inngke.ai.crm.db.crm.entity.*;
import com.inngke.ai.crm.db.crm.manager.*;
import com.inngke.ai.crm.dto.enums.AiGenerateTaskStatusEnum;
import com.inngke.ai.crm.dto.enums.AiProductIdEnum;
import com.inngke.ai.crm.dto.request.*;
import com.inngke.ai.crm.dto.response.*;
import com.inngke.ai.crm.dto.response.ai.AiInputXiaoHongShuDto;
import com.inngke.ai.crm.dto.response.ai.AiOutputXiaoHongShuDto;
import com.inngke.ai.crm.service.CrmAiGeneratorService;
import com.inngke.ai.crm.service.XiaoHongShuService;
import com.inngke.ai.crm.service.es.AiGenerateTaskEsService;
import com.inngke.ai.crm.service.message.CrmMessageManagerService;
import com.inngke.ai.crm.service.message.CrmMessageTypeEnum;
import com.inngke.ai.crm.service.message.content.CrmAiGenerateMessageContext;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.Lock;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.JsonService;
import com.inngke.common.service.LockService;
import com.inngke.common.service.SnowflakeIdService;
import com.inngke.common.utils.DateTimeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-12-21 15:53
 **/
@Service
public class XiaoHongShuServiceImpl implements XiaoHongShuService {

    private static final Logger logger = LoggerFactory.getLogger(XiaoHongShuServiceImpl.class);
    private static final String XHS_GET_QR_CODE_IP_TASK = CrmServiceConsts.CACHE_KEY_PRE + "xhs:get-qr-code-ip:";

    @Autowired
    private JsonService jsonService;

    @Autowired
    private AiGenerateXhsManager aiGenerateXhsManager;

    @Autowired
    private UserXiaoHongShuManager userXiaoHongShuManager;

    @Autowired
    private UserManager userManager;

    @Autowired
    private AiGenerateTaskReleaseManager aiGenerateTaskReleaseManager;

    @Autowired
    private CrmMessageManagerService crmMessageManagerService;

    @Autowired
    private AiGenerateTaskManager aiGenerateTaskManager;

    @Autowired
    private SnowflakeIdService snowflakeIdService;

    @Autowired
    private LockService lockService;

    @Autowired
    private GeoServiceForAi geoServiceForAi;

    @Autowired
    private XiaoHongShuPublishApi xiaoHongShuPublishApi;

    @Autowired
    private AiGcConfig aiGcConfig;

    @Autowired
    private UploadServiceForAi uploadServiceForAi;

    @Autowired
    private FrontCanvasApi frontCanvasApi;

    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private RedisTemplate<String,CrmXiaoHongShuPublishRequest> ipTaskRedisTemplate;

    @Autowired
    private CrmAiGeneratorService crmAiGeneratorService;

    @Autowired
    private AiGenerateTaskEsService aiGenerateTaskEsService;

    @Autowired
    private AiGenerateTaskIoManager aiGenerateTaskIoManager;

    private String XIAO_HONG_SHU_PUBLISH_LOCK = "crm_ai_yk:lock:xiaoHongShuPublish:";

    private String XIAO_HONG_SHU_OPTION_LOCK = "crm_ai_yk:lock:xiaoHongShuOption:";

    @Override
    public BaseResponse<GetUserXiaoHongShuInfoDto> getUserXiaoHongShuInfo(GetUserXiaoHongShuInfoRequest request) {
        return BaseResponse.success(userXiaoHongShuManager.xiaoHongShuInfo(request.getUserId(), request.getIp()));
    }

    @Override
    public BaseResponse<CrmXiaoHongShuPublishDto> getPublish(CrmXiaoHongShuPublishRequest request) {
        AiGenerateTaskRelease release = aiGenerateTaskReleaseManager.getXiaoHongShuAiGenerateTaskRelease(request.getAiGenerateTaskId());
        GetUserXiaoHongShuInfoDto userXiaoHongShuInfoDto = userXiaoHongShuManager.xiaoHongShuInfo(request.getUserId(), request.getIp());
        CrmXiaoHongShuPublishDto result = getPublishResult(userXiaoHongShuInfoDto, Objects.isNull(release) ? null : release.getStatus());
        return BaseResponse.success(result);
    }

    private CrmXiaoHongShuPublishDto getPublishResult(GetUserXiaoHongShuInfoDto userXiaoHongShuInfoDto, Integer status) {
        CrmXiaoHongShuPublishDto result = new CrmXiaoHongShuPublishDto();
        result.setXiaoHongShuInfo(userXiaoHongShuInfoDto);
        result.setStatus(-1);
        if (Objects.nonNull(status)) {
            result.setStatus(status);
        }
        return result;
    }

    @Override
    public BaseResponse<CrmXiaoHongShuPublishDto> publish(CrmXiaoHongShuPublishRequest request) {
        Lock lock = lockService.getLock(XIAO_HONG_SHU_PUBLISH_LOCK + request.getUserId(), aiGcConfig.getXhsPublishLockTime());
        if (Objects.isNull(lock)) {
            return BaseResponse.error("笔记发布间隔" + (aiGcConfig.getXhsPublishLockTime() / 60) + "分钟，请稍后再试！");
        }
        String ip = request.getIp();
        userXiaoHongShuManager.updateIpAddr(request.getUserId(), ip);

        logger.info("发布小红书请求参数：{}", jsonService.toJson(request));
        GetUserXiaoHongShuInfoDto userXiaoHongShuInfoDto = userXiaoHongShuManager.xiaoHongShuInfo(request.getUserId(), ip);
        if (Boolean.TRUE.equals(userXiaoHongShuInfoDto.getLoginExpire())) {
            lock.unlock();
            return BaseResponse.success(getPublishResult(userXiaoHongShuInfoDto, -1));
        }
        try {
            // 已经存在并却不是发布失败
            AiGenerateTaskRelease release = aiGenerateTaskReleaseManager.getXiaoHongShuAiGenerateTaskRelease(request.getAiGenerateTaskId());
            if (Objects.nonNull(release) && !release.getStatus().equals(-1)) {
                return getPublish(request);
            }
            // 不存在直接插入
            boolean isFirst = Objects.isNull(release);
            if (isFirst) {
                release = publishSave(request);
            }
            AiGenerateTaskRelease update = new AiGenerateTaskRelease();
            update.setId(release.getId());
            update.setReleaseTime(LocalDateTime.now());
            update.setStatus(0);
            update.setRetryCount(Optional.ofNullable(release.getRetryCount()).orElse(0) + (isFirst ? 0 : 1));
            // 调用发布小红书发布
            if (xiaoHongShuPublish(request)) {
                aiGenerateTaskReleaseManager.updateById(update);

                // 跟新es数据
                aiGenerateTaskEsService.updateEsDocByIds(List.of(request.getAiGenerateTaskId()));
                return BaseResponse.success(getPublishResult(userXiaoHongShuInfoDto, 0));
            }
            lock.unlock();
            return BaseResponse.error("发布失败");
        } catch (Exception e) {
            return BaseResponse.error(e.getMessage());
        }
    }

    @Override
    public BaseResponse<Boolean> sendMobileCode(CrmSendMobileCodeRequest request) {
        Lock lock = lockService.getLock(XIAO_HONG_SHU_OPTION_LOCK + request.getUserId(), 2);
        if (Objects.isNull(lock)) {
            return BaseResponse.error("操作频繁");
        }
        try {
            userXiaoHongShuManager.updateIpAddr(request.getUserId(), request.getIp());

            String provinceId = geoServiceForAi.ipGetProvinceId(request.getIp());

            XiaoHongShuMobileCodeRequest xiaoHongShuMobileCodeRequest = new XiaoHongShuMobileCodeRequest();
            xiaoHongShuMobileCodeRequest.setMobile(request.getMobile());
            xiaoHongShuMobileCodeRequest.setProvinceId(provinceId);
            xiaoHongShuMobileCodeRequest.setUserId(request.getUserId());
            BaseResponse<Object> response = xiaoHongShuPublishApi.mobileCode(xiaoHongShuMobileCodeRequest);
            logger.info("小红书手机号验证码返回：{},mobile:{}", jsonService.toJson(response), request.getMobile());
            return BaseResponse.responseSuccess(response) ? BaseResponse.success(true) : BaseResponse.error("登录失败请重试");
        } catch (Exception e) {
            logger.info("小红书手机号验证码异常：", e);
            return BaseResponse.error("登录失败请重试");
        } finally {
            lock.unlock();
        }
    }

    @Override
    public BaseResponse<GetUserXiaoHongShuInfoDto> xiaoHongShuLogin(CrmMobileLoginRequest request) {
        logger.info("用户登录请求参数：{}", jsonService.toJson(request));
        Lock lock = lockService.getLock(XIAO_HONG_SHU_OPTION_LOCK + request.getUserId(), 60);
        if (Objects.isNull(lock)) {
            return BaseResponse.error("登录中！");
        }
        try {
            userXiaoHongShuManager.updateIpAddr(request.getUserId(), request.getIp());

            String provinceId = geoServiceForAi.ipGetProvinceId(request.getIp());

            XiaoHongShuLoginRequest xiaoHongShuLoginRequest = new XiaoHongShuLoginRequest();
            xiaoHongShuLoginRequest.setCode(request.getCode());
            xiaoHongShuLoginRequest.setMobile(request.getMobile());
            xiaoHongShuLoginRequest.setProvinceId(provinceId);
            xiaoHongShuLoginRequest.setUserId(request.getUserId());
            BaseResponse<XiaoHongShuLoginDto> response = xiaoHongShuPublishApi.login(xiaoHongShuLoginRequest);
            logger.info("小红书手机号登录返回：{},mobile:{}", jsonService.toJson(response), request.getMobile());
            if (!BaseResponse.responseSuccessWithNonNullData(response)) {
                return BaseResponse.error("登录失败，请刷新重试！");
            }
            XiaoHongShuLoginDto data = response.getData();

            UserXiaoHongShu userXiaoHongShu = userXiaoHongShuManager.getById(request.getUserId());
            if (Objects.isNull(userXiaoHongShu)) {
                UserXiaoHongShu save = new UserXiaoHongShu();
                save.setId(request.getUserId());
                save.setMobile(request.getMobile());
                save.setAvatar(data.getAvatar());
                save.setCookies(jsonService.toJson((Serializable) data.getCookies()));
                save.setRedId(data.getRedId());
                save.setUserName(data.getUserName());
                save.setCreateTime(LocalDateTime.now());
                save.setUpdateTime(LocalDateTime.now());
                userXiaoHongShuManager.save(save);
            }
            userXiaoHongShuManager.update(Wrappers.<UserXiaoHongShu>update()
                    .set(UserXiaoHongShu.MOBILE, request.getMobile())
                    .set(UserXiaoHongShu.MOBILE, request.getMobile())
                    .set(UserXiaoHongShu.AVATAR, data.getAvatar())
                    .set(UserXiaoHongShu.COOKIES, jsonService.toJson((Serializable) data.getCookies()))
                    .set(UserXiaoHongShu.RED_ID, data.getRedId())
                    .set(UserXiaoHongShu.USER_NAME, data.getUserName())
                    .set(UserXiaoHongShu.EXPIRE_TIME, null)
                    .eq(UserXiaoHongShu.ID, request.getUserId()));

            GetUserXiaoHongShuInfoDto getUserXiaoHongShuInfoDto = new GetUserXiaoHongShuInfoDto();
            getUserXiaoHongShuInfoDto.setLoginExpire(false);
            getUserXiaoHongShuInfoDto.setMobile(request.getMobile());
            getUserXiaoHongShuInfoDto.setAvatar(data.getAvatar());


            return BaseResponse.success(getUserXiaoHongShuInfoDto);
        } finally {
            lock.unlock();
        }
    }


    @Override
    public BaseResponse<XhsQrCodeLoginState> getLoginQrCode(JwtPayload jwtPayload, Long taskId, String ip) {
        Lock lock = lockService.getLock(XIAO_HONG_SHU_OPTION_LOCK + "get-login-qr-code:" + jwtPayload.getCid(), 10);
        if (Objects.isNull(lock)) {
            return BaseResponse.error("操作频繁");
        }
        try {
            //记录用户获取二维码的ip和任务id
            if (Objects.nonNull(taskId)){
                CrmXiaoHongShuPublishRequest ipTask = new CrmXiaoHongShuPublishRequest();
                ipTask.setUserId(jwtPayload.getCid());
                ipTask.setAiGenerateTaskId(taskId);
                ipTask.setIp(ip);
                ipTaskRedisTemplate.opsForValue().set(XHS_GET_QR_CODE_IP_TASK + jwtPayload.getCid(), ipTask, 10, TimeUnit.MINUTES);
            }

            userXiaoHongShuManager.updateIpAddr(jwtPayload.getCid(), ip);

            String provinceId = geoServiceForAi.ipGetProvinceId(ip);

            BrowserTaskRequest browserTaskRequest = new BrowserTaskRequest();
            browserTaskRequest.setUserId(jwtPayload.getCid());
            browserTaskRequest.setProvinceId(provinceId);
            browserTaskRequest.setUseProxy(true);

            return Optional.ofNullable(xiaoHongShuPublishApi.getLoginQrCode(browserTaskRequest))
                    .map(BaseResponse::getData).map(BaseResponse::success)
                    .orElse(BaseResponse.error("获取二维码失败"));
        } finally {
            lock.unlock();
        }
    }

    @Override
    public BaseResponse<XhsQrCodeLoginState> getLoginQrCodeState(JwtPayload jwtPayload, String ip) {
        String provinceId = geoServiceForAi.ipGetProvinceId(ip);

        BrowserTaskRequest browserTaskRequest = new BrowserTaskRequest();
        browserTaskRequest.setUserId(jwtPayload.getCid());
        browserTaskRequest.setProvinceId(provinceId);
        browserTaskRequest.setUseProxy(true);
        Map<String,String> query = jsonService.toObject(jsonService.toJson(browserTaskRequest), Map.class);

        return Optional.ofNullable(xiaoHongShuPublishApi.getLoginQrCodeState(query))
                .map(BaseResponse::getData).map(BaseResponse::success)
                .orElse(BaseResponse.error("获取二维码状态失败"));
    }

    @Override
    public Boolean qrCodeLoginCallback(Long userId, XiaoHongShuLoginDto data) {
        UserXiaoHongShu userXiaoHongShu = userXiaoHongShuManager.getById(userId);
        if (Objects.isNull(userXiaoHongShu)) {
            UserXiaoHongShu save = new UserXiaoHongShu();
            save.setId(userId);
            save.setAvatar(data.getAvatar());
            save.setCookies(jsonService.toJson((Serializable) data.getCookies()));
            save.setRedId(data.getRedId());
            save.setUserName(data.getUserName());
            save.setCreateTime(LocalDateTime.now());
            save.setUpdateTime(LocalDateTime.now());
            userXiaoHongShuManager.save(save);
        }

        userXiaoHongShuManager.update(Wrappers.<UserXiaoHongShu>update()
                .set(UserXiaoHongShu.AVATAR, data.getAvatar())
                .set(UserXiaoHongShu.COOKIES, jsonService.toJson((Serializable) data.getCookies()))
                .set(UserXiaoHongShu.RED_ID, data.getRedId())
                .set(UserXiaoHongShu.USER_NAME, data.getUserName())
                .set(UserXiaoHongShu.EXPIRE_TIME, null)
                .eq(UserXiaoHongShu.ID, userId));

        //发布文章
        CrmXiaoHongShuPublishRequest ipTask = ipTaskRedisTemplate.opsForValue().get(XHS_GET_QR_CODE_IP_TASK + userId);
        if (Objects.nonNull(ipTask)){
            AsyncUtils.runAsync(()-> xiaoHongShuPublish(ipTask));
        }

        return true;
    }

    @Override
    public BaseResponse<Boolean> publishNotify(XiaoHongShuPublishNotifyRequest request) {
        logger.info("小红书回调通知请求参数：{}", jsonService.toJson(request));
        List<AiGenerateTaskRelease> list = aiGenerateTaskReleaseManager.list(new QueryWrapper<AiGenerateTaskRelease>()
                .eq(AiGenerateTaskRelease.TYPE, 1)
                .eq(AiGenerateTaskRelease.AI_GENERATE_TASK_ID, request.getYkId()));
        if (CollectionUtils.isEmpty(list)) {
            logger.info("小红书回调通知找不到对应的参数：{}", request.getYkId());
            return BaseResponse.error("ykId不存在：" + request.getYkId());
        }
        AiGenerateTaskRelease release = list.get(0);
        if (!release.getStatus().equals(0)) {
            logger.info("小红书回调通知状态已修改：{}", request.getYkId());
            return BaseResponse.error("状态已修改：");
        }
        AiGenerateTaskRelease update = new AiGenerateTaskRelease();
        update.setId(release.getId());
        update.setStatus(request.getStatus());
        update.setExternalId(request.getXhsId());
        if (Objects.nonNull(request.getPostTime())) {
            update.setReleaseTime(DateTimeUtils.MillisToLocalDateTime(request.getPostTime()));
        }

        // 发布通知
        publishNotifyMsg(request.getYkId(), request.getStatus());

        // 跟新发布状态
        boolean result = aiGenerateTaskReleaseManager.updateById(update);

        // 同步es
        aiGenerateTaskEsService.updateEsDocByIds(List.of(release.getAiGenerateTaskId()));

        return BaseResponse.success(result);
    }

    @Override
    public void publishNotifyMsg(Long taskId, Integer status) {
        AiGenerateTask task = aiGenerateTaskManager.getById(taskId);
        if (Objects.isNull(task)) {
            return;
        }
        AiGenerateTaskIo taskIo = aiGenerateTaskIoManager.getById(taskId);
        if (taskIo == null) {
            return;
        }
        User user = userManager.getById(task.getUserId());
        if (Objects.isNull(user)) {
            return;
        }

        String outputs = taskIo.getOutputs();
        AiOutputXiaoHongShuDto outputXiaoHongShuDto = jsonService.toObject(outputs, AiOutputXiaoHongShuDto.class);
        // 发送通知
        CrmAiGenerateMessageContext crmMessageContext = new CrmAiGenerateMessageContext();
        crmMessageContext.setMessageType(CrmMessageTypeEnum.XIAO_HONG_SHU_PUBLISH);
        crmMessageContext.setId(taskId);
        crmMessageContext.setType(status.equals(1) ? "发布成功" : "发布失败");
        crmMessageContext.setName(outputXiaoHongShuDto.getTitle());
        crmMessageContext.setProductId(task.getAiProductId());
        crmMessageContext.setAppPubOpenId(user.getMpOpenId());
        crmMessageManagerService.send(crmMessageContext);
    }

    @Override
    public BasePaginationResponse<CrmXiaoHongShuListDto> getList(Long userId, XhsListRequest xhsListRequest) {

        GetAiTaskStatisticPagingRequest getAiTaskStatisticPagingRequest = new GetAiTaskStatisticPagingRequest();
        BeanUtils.copyProperties(xhsListRequest, getAiTaskStatisticPagingRequest);

        getAiTaskStatisticPagingRequest.setAiProductId(AiProductIdEnum.XIAO_HOME_SHU.getType());
        BasePaginationResponse<AiGenerateTaskStatisticResponse> data = crmAiGeneratorService
                .getAiGenerateTaskStatistic(userId
                        , getAiTaskStatisticPagingRequest).getData();

        List<AiGenerateTaskStatisticResponse> list = data.getList();
        List<CrmXiaoHongShuListDto> result = list.stream().map(XiaoHongShuDataConverter::toDetailDto)
                .collect(Collectors.toList());

        BasePaginationResponse<CrmXiaoHongShuListDto> xiaoHongShuDetailDtoBasePaginationResponse = new BasePaginationResponse<>();
        xiaoHongShuDetailDtoBasePaginationResponse.setTotal(data.getTotal());
        xiaoHongShuDetailDtoBasePaginationResponse.setList(result);
        return xiaoHongShuDetailDtoBasePaginationResponse;
    }

    @Override
    public BaseResponse<PublishInfoDto> publishInfo(PublishInfoRequest request) {
        PublishInfoDto result = new PublishInfoDto();

        AiGenerateTask task = aiGenerateTaskManager.getById(request.getId());
        AiGenerateTaskRelease release = aiGenerateTaskReleaseManager.getXiaoHongShuAiGenerateTaskRelease(request.getId());
        if (Objects.isNull(task) || Objects.isNull(release)) {
            return BaseResponse.error("发布消息不存在");
        }
        AiGenerateTaskIo taskIo = aiGenerateTaskIoManager.getById(request.getId());
        if (taskIo == null) {
            return BaseResponse.error("发布消息不存在");
        }
        AiInputXiaoHongShuDto inputXiaoHongShuDto = jsonService.toObject(taskIo.getInputs(), AiInputXiaoHongShuDto.class);
        AiOutputXiaoHongShuDto outputXiaoHongShuDto = jsonService.toObject(taskIo.getOutputs(), AiOutputXiaoHongShuDto.class);

        UserXiaoHongShu userXiaoHongShu = userXiaoHongShuManager.getById(request.getUserId());
        if (Objects.isNull(userXiaoHongShu)) {
            return BaseResponse.error("用户消息不存在");
        }

        result.setAvatar(userXiaoHongShu.getAvatar());
        result.setUserName(userXiaoHongShu.getUserName());
        result.setRedId(userXiaoHongShu.getRedId());
        result.setImages(inputXiaoHongShuDto.getImages());
        if (CollectionUtils.isEmpty(result.getImages()) && !StringUtils.isEmpty(inputXiaoHongShuDto.getImage())) {
            result.setImages(Lists.newArrayList(inputXiaoHongShuDto.getImage()));
        }
        result.setTitle(outputXiaoHongShuDto.getTitle());
        result.setStatus(release.getStatus());

        return BaseResponse.success(result);
    }

    private boolean xiaoHongShuPublish(CrmXiaoHongShuPublishRequest request) {
        AiGenerateTask task = aiGenerateTaskManager.getById(request.getAiGenerateTaskId());
        if (Objects.isNull(task) || !task.getStatus().equals(2)) {
            return false;
        }
        AiGenerateTaskIo taskIo = aiGenerateTaskIoManager.getById(task.getId());
        if (taskIo == null) {
            return false;
        }
        UserXiaoHongShu userXiaoHongShu = userXiaoHongShuManager.getById(request.getUserId());

        String outputs = taskIo.getOutputs();
        String inputs = taskIo.getInputs();

        AiOutputXiaoHongShuDto outputXiaoHongShuDto = jsonService.toObject(outputs, AiOutputXiaoHongShuDto.class);
        AiInputXiaoHongShuDto inputXiaoHongShuDto = jsonService.toObject(inputs, AiInputXiaoHongShuDto.class);

        List<XiaoHongShuCookiesDto> cookiesDtoList = jsonService.toObjectList(userXiaoHongShu.getCookies(), XiaoHongShuCookiesDto.class);

        String provinceId = geoServiceForAi.ipGetProvinceId(request.getIp());

        XiaoHongShuPublishRequest xiaoHongShuPublishRequest = new XiaoHongShuPublishRequest();
        xiaoHongShuPublishRequest.setImages(inputXiaoHongShuDto.getImages());
        xiaoHongShuPublishRequest.setTitle(outputXiaoHongShuDto.getTitle());
        xiaoHongShuPublishRequest.setContent(outputXiaoHongShuDto.getContent());
        xiaoHongShuPublishRequest.setMobile(userXiaoHongShu.getMobile());
        xiaoHongShuPublishRequest.setProvinceId(provinceId);
        if (CollectionUtils.isEmpty(xiaoHongShuPublishRequest.getImages()) && !StringUtils.isEmpty(inputXiaoHongShuDto.getImage())) {
            xiaoHongShuPublishRequest.setImages(Lists.newArrayList(inputXiaoHongShuDto.getImage()));
        }

        if (!StringUtils.isEmpty(outputXiaoHongShuDto.getTag())) {
            xiaoHongShuPublishRequest.setTags(Arrays.stream(outputXiaoHongShuDto.getTag().split(" ")).collect(Collectors.toList()));
        } else {
            List<String> tags = getTags(xiaoHongShuPublishRequest);
            xiaoHongShuPublishRequest.setTags(tags);
        }

        xiaoHongShuPublishRequest.setYkId(task.getId());
        xiaoHongShuPublishRequest.setCookies(cookiesDtoList);
        xiaoHongShuPublishRequest.setUserId(request.getUserId());

        AsyncUtils.runAsync(() -> {
            // 判断是是否需要生成Ai标注
            aiMark(task, xiaoHongShuPublishRequest);

            logger.info("小红书发布请求信息：{}", jsonService.toJson(xiaoHongShuPublishRequest));
            BaseResponse<XiaoHongShuPublishDto> response = xiaoHongShuPublishApi.publish(xiaoHongShuPublishRequest);
            logger.info("小红书发布返回信息：{},aiGenerateTaskId:{}", jsonService.toJson(response), request.getAiGenerateTaskId());
        });
        return true;
    }

    private void aiMark(AiGenerateTask task, XiaoHongShuPublishRequest xiaoHongShuPublishRequest) {
        Long id = task.getId();
        AiGenerateXhs xhs = aiGenerateXhsManager.getById(id);
        if (Objects.isNull(xhs)) {
            return;
        }
        Boolean aiMark = xhs.getAiMark();
        List<String> images = xiaoHongShuPublishRequest.getImages();
        String imageMarkParam = xhs.getImageMarkParam();
        List<CrmXhsOutputsImageRequest> list = jsonService.toObjectList(imageMarkParam, CrmXhsOutputsImageRequest.class);
        if (CollectionUtils.isEmpty(list) || CollectionUtils.isEmpty(images)) {
            return;
        }
        List<String> imagesResult = new ArrayList<>(images.size());

        Map<String, CrmXhsOutputsImageRequest> originalImageUrlMap = list.stream()
                .collect(Collectors.toMap(CrmXhsOutputsImageRequest::getOriginalImageUrl, Function.identity()));
        for (String image : images) {
            CrmXhsOutputsImageRequest crmXhsOutputsImageRequest = originalImageUrlMap.get(image);
            // ai标注关了并且没有编辑过
            if (!Boolean.TRUE.equals(aiMark) && Boolean.FALSE.equals(xhs.getEditStatus())) {
                imagesResult.add(image);
                return;
            }
            if (!StringUtils.isEmpty(crmXhsOutputsImageRequest.getGenerateImageUrl())) {
                imagesResult.add(crmXhsOutputsImageRequest.getGenerateImageUrl());
                continue;
            }
            imagesResult.add(image);
        }
        xiaoHongShuPublishRequest.setImages(imagesResult);
    }



    private String generateImageBase64(Map<String, Object> generateParam) {
        try {
            String param = JSON.toJSONString(generateParam);
            param = param.replaceAll("\"", "\\\\\"");
            String cmd = String.format("%s \"%s\"", aiGcConfig.getAiMarkShell(), param);
            logger.info("执行ai打标脚本：{}", cmd);
            // windows
            if (System.getProperty("os.name").contains("Windows")) {
                return execWindows(cmd);
            }
            return execLinux(cmd);
        } catch (Exception e) {
            logger.info("脚本生成图片标注失败：", e);
        }
        return null;
    }

    public String execWindows(String cmd) throws IOException {
        // 调用脚本
        Process process = Runtime.getRuntime().exec(cmd);
        InputStream inputStream = process.getInputStream();
        byte[] bytes = inputStream.readAllBytes();
        inputStream.close();
        return new String(bytes);
    }

    public String execLinux(String cmd) throws IOException {
        ProcessBuilder processBuilder = new ProcessBuilder();
        processBuilder.command("/bin/bash", "-c", cmd);
        processBuilder.redirectErrorStream(true);
        Process process = null;
        process = processBuilder.start();
        InputStream inputStream = process.getInputStream();
        byte[] bytes = inputStream.readAllBytes();
        inputStream.close();
        return new String(bytes);
    }

    private static List<String> getTags(XiaoHongShuPublishRequest xiaoHongShuPublishRequest) {
        String content = xiaoHongShuPublishRequest.getContent();
        int index = content.indexOf("#");
        if (index == -1) {
            return null;
        }
        xiaoHongShuPublishRequest.setContent(content.substring(0, index));
        String tags = content.substring(index);
        if (StringUtils.isEmpty(tags)) {
            return null;
        }
        List<String> temp = Arrays.stream(tags.split("#")).collect(Collectors.toList());
        List<String> result = new ArrayList<>();
        for (String s : temp) {
            if (StringUtils.isEmpty(s)) {
                continue;
            }
            int sIndex = s.indexOf("\n");
            if (sIndex == -1) {
                result.add("#" + s.trim());
                continue;
            }
            result.add("#" + s.substring(0, sIndex));
            String substring = s.substring(sIndex);
            xiaoHongShuPublishRequest.setContent(xiaoHongShuPublishRequest.getContent() + substring);
        }
        return result;
    }


    private AiGenerateTaskRelease publishSave(CrmXiaoHongShuPublishRequest request) {
        AiGenerateTask task = aiGenerateTaskManager.getById(request.getAiGenerateTaskId());
        if (Objects.isNull(task) || !task.getStatus().equals(AiGenerateTaskStatusEnum.SUCCESS.getCode())) {
            throw new InngkeServiceException("小红书生成文案不存在");
        }
        AiGenerateTaskRelease release = new AiGenerateTaskRelease();
        release.setId(snowflakeIdService.getId());
        release.setType(1);
        release.setStatus(-1);
        LocalDateTime now = LocalDateTime.now();
        release.setCreateTime(now);
        release.setUpdateTime(now);
        release.setAiGenerateTaskId(request.getAiGenerateTaskId());
        release.setReleaseTime(now);
        release.setRetryCount(0);
        aiGenerateTaskReleaseManager.save(release);

        // 添加到es
        aiGenerateTaskEsService.addEsDocByIds(List.of(request.getAiGenerateTaskId()));
        return release;
    }


}
