package com.inngke.ai.crm.core.config;

import com.google.common.collect.Lists;
import com.inngke.ai.crm.db.crm.manager.AppConfigManager;
import com.inngke.ai.crm.dto.enums.AppConfigCodeEnum;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023-09-12 15:45
 **/
@Configuration
public class CrmWxConfig {

    @Bean
    public RSAAutoCertificateConfig config(AppConfigManager appConfigManager) {

        Map<String, String> map = appConfigManager.getValueByCodeList(Lists.newArrayList(
                AppConfigCodeEnum.WX_PAY_MERCHANT_ID.getCode(),
                AppConfigCodeEnum.WX_PAY_PRIVATE_KEY.getCode(),
                AppConfigCodeEnum.WX_PAY_MERCHANT_SERIAL_NUMBER.getCode(),
                AppConfigCodeEnum.WX_PAY_API_V3_KEY.getCode()));

        String merchantId = map.get(AppConfigCodeEnum.WX_PAY_MERCHANT_ID.getCode());
        String privateKey = map.get(AppConfigCodeEnum.WX_PAY_PRIVATE_KEY.getCode());
        String merchantSerialNumber = map.get(AppConfigCodeEnum.WX_PAY_MERCHANT_SERIAL_NUMBER.getCode());
        String apiV3Key = map.get(AppConfigCodeEnum.WX_PAY_API_V3_KEY.getCode());
        return new RSAAutoCertificateConfig.Builder()
                .merchantId(merchantId)
                .privateKey(privateKey)
                .merchantSerialNumber(merchantSerialNumber)
                .apiV3Key(apiV3Key)
                .build();
    }


}
