package com.inngke.ai.crm.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.ai.crm.db.crm.entity.DifyAppConf;
import com.inngke.ai.crm.db.crm.entity.Product;
import com.inngke.ai.crm.db.crm.manager.ProductManager;
import com.inngke.ai.crm.dto.form.CommonFormConfig;
import com.inngke.ai.crm.dto.form.FormTypeEnum;
import com.inngke.ai.crm.dto.form.SelectFormConfig;
import com.inngke.ai.crm.dto.form.SelectOption;
import com.inngke.ai.crm.dto.response.pro.ProAiArticleTemplateDto;
import com.inngke.ai.crm.service.ProductService;
import com.inngke.common.service.JsonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class ProductServiceImpl implements ProductService {
    @Autowired
    private ProductManager productManager;

    @Autowired
    private JsonService jsonService;

    @Override
    public Product getProductById(Long id) {
        return productManager.getById(id);
    }

    @Override
    public void setProductFormConfig(Long userOrganizeId, List<DifyAppConf> appConfigList, ProAiArticleTemplateDto proAiArticleTemplateDto) {
        // 小红书-商品列表
        List<Product> productList = productManager.list(Wrappers.<Product>query().eq(Product.ORGANIZE_ID, userOrganizeId));
        if (!CollectionUtils.isEmpty(productList)) {
            //判断应用是否开启使用商品，若开启则将商品选项填入表单中
            appConfigList.forEach(organizeAppConfig -> {
                if (Boolean.TRUE.equals(organizeAppConfig.getHasProduct())) {
                    List<CommonFormConfig> config = proAiArticleTemplateDto.getFormColumnConfigMap().computeIfAbsent(
                            organizeAppConfig.getId().toString(),
                            id -> jsonService.toObjectList(jsonService.toJson((Serializable) proAiArticleTemplateDto.getFormColumnConfig()), CommonFormConfig.class)
                    );
                    config.add(0, new SelectFormConfig()
                            .setInputType("button")
                            .setSelectOptions(productList.stream().map(product -> {
                                SelectOption selectOption = new SelectOption();
                                selectOption.setValue(product.getId());
                                selectOption.setTitle(product.getName());
                                return selectOption;
                            }).collect(Collectors.toList()))
                            .setKey("product_ids")
                            .setLabel("选择商品")
                            .setType(FormTypeEnum.SELECT_BUTTON.getType()));
                }
            });
        }
    }
}
