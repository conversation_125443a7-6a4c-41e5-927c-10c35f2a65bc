package com.inngke.ai.crm.db.crm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.inngke.ai.crm.dto.enums.CoinOrderStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2023-09-17 20:28
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class CoinOrder implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 雪花ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 订单Id，wx_trade_id.out_trade_id
     */
    private Long orderId;

    /**
     * 组织ID
     */
    private Long organizeId;

    /**
     * 虚拟币套餐Id
     */
    private Long coinProductId;

    /**
     * 用户Id
     */
    private Long userId;

    /**
     * 状态，0=未购买成功，1=购买成功
     *
     * @see CoinOrderStatusEnum
     */
    private Integer status;

    /**
     * 标题
     */
    private String title;

    /**
     * 价格
     */
    private Integer amount;


    /**
     * 原价
     */
    private Integer orgAmount;

    /**
     * 积分
     */
    private Integer coin;

    /**
     * 过期时间
     */
    private LocalDateTime expireTime;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;



    public static final String ID = "id";

    public static final String ORDER_ID = "order_id";

    public static final String ORGANIZE_ID = "organize_id";

    public static final String COIN = "coin";

    public static final String COIN_PRODUCT_ID = "coin_product_id";

    public static final String USER_ID = "user_id";

    public static final String TITLE = "title";

    public static final String AMOUNT = "amount";

    public static final String ORG_AMOUNT = "org_amount";

    public static final String STATUS = "status";

    public static final String EXPIRE_TIME = "expire_time";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";


}
