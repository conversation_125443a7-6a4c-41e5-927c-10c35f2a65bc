package com.inngke.ai.crm.service.cache;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.inngke.ai.crm.db.crm.manager.AppConfigManager;
import com.inngke.ai.crm.dto.enums.AppConfigCodeEnum;
import com.inngke.common.core.InngkeAppConst;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;

@Component
public class BgmTypeCache {
    private static final String STR_INVISIBLE = "invisible_";

    private LoadingCache<Integer, BgmTypeCacheData> cache;

    @Autowired
    private AppConfigManager appConfigManager;

    public BgmTypeCache() {
        cache = Caffeine.newBuilder()
                .expireAfterWrite(1, TimeUnit.MINUTES) // 写入后过期时间设为1分钟
                .refreshAfterWrite(1, TimeUnit.MINUTES) // 自动刷新时间设为1分钟
                .build(this::loadFromDb);
    }

    public BgmTypeCacheData get() {
        return cache.get(0, orgId -> loadFromDb(0));
    }

    private BgmTypeCacheData loadFromDb(int organizeId) {
        BgmTypeCacheData data = new BgmTypeCacheData();
        String configStr = appConfigManager.getValueByCode(AppConfigCodeEnum.AI_VIDEO_BGM_TYPE_CONF.getCode());
        if (configStr == null) {
            return data;
        }
        Arrays.stream(configStr.split(InngkeAppConst.TURN_LINE))
                .map(String::trim)
                .filter(line -> !line.isEmpty() && !line.startsWith(InngkeAppConst.SHARP_STR))
                .forEach(line -> {
                    String[] columns = line.split(InngkeAppConst.CLN_STR);
                    String firstColumn = columns[0];
                    if (NumberUtils.isDigits(firstColumn)) {
                        //如果是数字，分类字义
                        data.bgmTypes.put(Integer.parseInt(firstColumn), columns[1].trim());
                    } else if (firstColumn.startsWith(STR_INVISIBLE)) {
                        //不展示的分类配置
                        int invisibleOrganize = Integer.parseInt(firstColumn.substring(STR_INVISIBLE.length()));
                        Set<Integer> invisibleTypes = Sets.newHashSet();
                        Arrays.stream(columns[1].split(InngkeAppConst.COMMA_STR))
                                .map(String::trim)
                                .filter(item -> !item.isEmpty())
                                .forEach(item -> {
                                    invisibleTypes.add(Integer.parseInt(item.trim()));
                                });
                        data.invisibleTypeMap.put(invisibleOrganize, invisibleTypes);
                    }
                });
        return data;
    }

    public static class BgmTypeCacheData {
        Map<Integer, String> bgmTypes = Maps.newLinkedHashMap();

        Map<Integer, Set<Integer>> invisibleTypeMap = Maps.newHashMap();

        public void foreach(int organizeId, BiConsumer<Integer, String> biConsumer) {
            Set<Integer> orgInvisibleTypes = invisibleTypeMap.getOrDefault(organizeId, invisibleTypeMap.getOrDefault(0, Sets.newHashSet()));
            bgmTypes.forEach((typeId, typeName) -> {
                if (orgInvisibleTypes.contains(typeId)) {
                    return;
                }
                biConsumer.accept(typeId, typeName);
            });
        }
    }
}
