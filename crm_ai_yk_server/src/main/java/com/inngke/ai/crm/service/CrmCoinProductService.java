package com.inngke.ai.crm.service;

import com.inngke.ai.crm.dto.request.CoinProductListRequest;
import com.inngke.ai.crm.dto.request.CrmActivityFreeVipRequest;
import com.inngke.ai.crm.dto.request.CrmPcCoinRechargeRequest;
import com.inngke.ai.crm.dto.response.ActivityFreeVipDto;
import com.inngke.ai.crm.dto.response.CoinProductListDto;
import com.inngke.common.dto.response.BaseResponse;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-09-19 15:38
 **/
public interface CrmCoinProductService {
    BaseResponse<List<CoinProductListDto>> list(CoinProductListRequest request);

    BaseResponse<ActivityFreeVipDto> activityFreeVip(CrmActivityFreeVipRequest request);

    BaseResponse<List<CoinProductListDto>> listPc(CoinProductListRequest request);

    BaseResponse<Boolean> pcCoinReCharge(CrmPcCoinRechargeRequest request);
}
