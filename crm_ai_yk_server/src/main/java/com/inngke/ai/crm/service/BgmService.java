package com.inngke.ai.crm.service;

import com.inngke.ai.crm.db.crm.entity.VideoBgmMaterial;
import com.inngke.ai.crm.dto.request.UserBgmUploadRequest;
import com.inngke.common.dto.JwtPayload;

import java.util.List;

public interface BgmService {
    
    /**
     * 用户上传BGM
     * 
     * @param jwtPayload 当前用户
     * @return 上传成功后的BGM信息
     */
    VideoBgmMaterial uploadUserBgm(JwtPayload jwtPayload, UserBgmUploadRequest request);
    
    /**
     * 用户删除BGM
     * 
     * @param jwtPayload 当前用户
     * @param bgmIds BGM ID列表
     * @return 删除结果
     */
    Boolean deleteUserBgm(JwtPayload jwtPayload, List<Long> bgmIds);

    /**
     * 获取BGM详情
     */
    VideoBgmMaterial getBgm(long bgmId);
}