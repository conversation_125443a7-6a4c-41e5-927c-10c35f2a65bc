package com.inngke.ai.crm.dto.form;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;



@Data
@Accessors(chain = true)
@JsonTypeInfo(
        use = JsonTypeInfo.Id.NAME,
        include = JsonTypeInfo.As.EXISTING_PROPERTY,
        property = "type"
)
@JsonSubTypes({
        @JsonSubTypes.Type(value = InputFormConfig.class, name = CommonFormConfig.TYPE_INPUT),
        @JsonSubTypes.Type(value = TextareaFormConfig.class, name = CommonFormConfig.TYPE_TEXTAREA),
        @JsonSubTypes.Type(value = SelectFormConfig.class, name = CommonFormConfig.TYPE_SELECT_BUTTON),
        @JsonSubTypes.Type(value = TtsSelectFormConfig.class, name = CommonFormConfig.TYPE_TTS),
        @JsonSubTypes.Type(value = UploadFormConfig.class, name = CommonFormConfig.TYPE_UPLOAD),
        @JsonSubTypes.Type(value = MusicSelectFormConfig.class, name = CommonFormConfig.TYPE_MUSIC),
        @JsonSubTypes.Type(value = CardSelectFormConfig.class, name = CommonFormConfig.TYPE_SELECT_CARD),
        @JsonSubTypes.Type(value = SelectTagFormConfig.class, name = CommonFormConfig.TYPE_SELECT_TAG),
        @JsonSubTypes.Type(value = DigitalPersonFormConfig.class, name = CommonFormConfig.TYPE_DIGITAL_PERSON),
})
public class CommonFormConfig implements Serializable {
    public static final String TYPE_INPUT = "input";
    public static final String TYPE_TEXTAREA = "textarea";
    public static final String TYPE_SELECT_BUTTON = "select-button";
    public static final String TYPE_SELECT_CARD = "select-card";
    public static final String TYPE_MUSIC = "music";
    public static final String TYPE_UPLOAD = "upload";
    public static final String TYPE_TTS = "tts";
    public static final String TYPE_SELECT_TAG = "select-tag";
    public static final String TYPE_DIGITAL_PERSON = "metaHuman";

    /**
     * 表单类型
     */
    private String type;

    /**
     * 标题，为空时没有标题
     */
    private String label;

    /**
     * 副标题，为空时没有副标题
     */
    private String subTitle;

    /**
     * 详情展示标题，优先级高于label。为空时，详情展示标题为label
     */
    private String detailShowTitle;

    /**
     * 是否必填，为true 且 label有标题时，会在标题后面追加必填标识。同时对当前表单项做必填校验。默认为false
     */
    private Boolean required;

    /**
     * 表单字段
     */
    private String key;

    /**
     * 受控字段: 用于级联disabledValue选择，可同时受控于多个字段。如果受控的字段不存在或值不为指定的value时，当前项不可见。
     * 使用fromKeys时，key可以重复。key的值会取当前可见的项的值
     */
    private List<FormKeyItem> fromKeys;

    /**
     * 受控字段的关系: and为全部匹配，or为部分匹配，not为排除匹配。默认为"and"
     *
     * @see FromKeyTypeEnum
     */
    private String fromKeyType;

    /**
     * 禁用时赋值，在fromKeys生效时启用。当前项不可见时，将本项的值切换到指定内容。为空表示删除字段（后端接口使用）
     */
    private Serializable disabledValue;

    /**
     * 默认值: 为空时，默认为""
     */
    private Serializable defaultValue;
}
