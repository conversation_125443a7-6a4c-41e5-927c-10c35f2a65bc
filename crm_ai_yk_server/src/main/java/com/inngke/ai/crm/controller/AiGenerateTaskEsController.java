package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.dto.request.GetAiTaskEsDocsRequest;
import com.inngke.ai.crm.dto.response.AiGenerateTaskEsDoc;
import com.inngke.ai.crm.service.es.AiGenerateTaskEsService;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.ip.common.dto.response.EsDocsResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @chapter AI
 * @section AI生成
 * <AUTHOR>
 * @Date 2024/3/22 13:27
 */
@RestController
@RequestMapping("/api/ai-task")
public class AiGenerateTaskEsController {

    @Autowired
    AiGenerateTaskEsService aiGenerateTaskEsService;

    /**
     * 获取AI任务列表(ES)
     */
    @GetMapping("/docs")
    public BaseResponse<EsDocsResponse<AiGenerateTaskEsDoc>> getDocs(GetAiTaskEsDocsRequest request) {
        return aiGenerateTaskEsService.getDocList(request.getLastAiTaskId(), request.getPageSize());
    }

}
