package com.inngke.ai.crm.mq.process.aigc;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inngke.ai.crm.db.crm.entity.AiGenerateTask;
import com.inngke.ai.crm.db.crm.entity.Coin;
import com.inngke.ai.crm.db.crm.entity.CoinLog;
import com.inngke.ai.crm.db.crm.entity.CoinMinusLog;
import com.inngke.ai.crm.db.crm.manager.AiGenerateTaskManager;
import com.inngke.ai.crm.db.crm.manager.CoinLogManager;
import com.inngke.ai.crm.db.crm.manager.CoinManager;
import com.inngke.ai.crm.db.crm.manager.CoinMinusLogManager;
import com.inngke.ai.crm.dto.enums.AiGenerateEventEnum;
import com.inngke.ai.crm.dto.enums.AiGenerateTaskStatusEnum;
import com.inngke.ai.crm.dto.enums.CoinLogEventTypeEnum;
import com.inngke.ai.crm.dto.response.ai.AiGenerateMqPayload;
import com.inngke.ai.crm.service.AiLockService;
import com.inngke.common.dto.Lock;
import com.inngke.common.service.JsonService;
import com.inngke.common.service.SnowflakeIdService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-09-01 16:41
 **/
@Service
public class AiGenerateFailProcess implements AiGenerateProcess {

    private static final Logger logger = LoggerFactory.getLogger(AiGenerateFailProcess.class);

    @Autowired
    private AiGenerateTaskManager aiGenerateTaskManager;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private CoinLogManager coinLogManager;

    @Autowired
    private CoinMinusLogManager coinMinusLogManager;

    @Autowired
    private SnowflakeIdService snowflakeIdService;


    @Autowired
    private CoinManager coinManager;

    @Autowired
    private AiLockService aiLockService;

    @Override
    public AiGenerateEventEnum event() {
        return AiGenerateEventEnum.FAIL;
    }

    @Override
    public void handle(AiGenerateMqPayload payload) {
        AiGenerateTask aiGenerateTask = aiGenerateTaskManager.getById(payload.getId());
        if (Objects.isNull(aiGenerateTask)) {
            logger.info("AiGenerateFailProcess找不到数据：{}", jsonService.toJson(payload));
            return;
        }
        if (AiGenerateTaskStatusEnum.SUCCESS.getCode().equals(aiGenerateTask.getStatus())) {
            logger.info("AiGenerateFailProcess,AIGC生成已经成功：{}", jsonService.toJson(aiGenerateTask));
            return;
        }
        Long userId = aiGenerateTask.getUserId();
        Lock lock = aiLockService.getCoinOperateLock(userId, true);
        try {
            Long id = aiGenerateTask.getId();
            List<CoinLog> coinLogList = coinLogManager.getByEventLogId(userId, id);
            if (CollectionUtils.isEmpty(coinLogList)) {
                logger.info("AiGenerateFailProcess无需返还数据，userId:{},id:{}", userId, id);
                return;
            }
            if (coinLogList.size() > 1) {
                logger.info("AiGenerateFailProcess有多条数据，userId:{},id:{}", userId, id);

                AiGenerateTask updateAiTask = new AiGenerateTask();
                updateAiTask.setId(aiGenerateTask.getId());
                updateAiTask.setStatus(AiGenerateTaskStatusEnum.FAIL.getCode());
                aiGenerateTaskManager.updateById(updateAiTask);
                return;
            }
            CoinLog coinLog = coinLogList.get(0);
            List<CoinMinusLog> coinMinusLogs = coinMinusLogManager.getByCoinLogId(coinLog.getId());
            Set<Long> coinIds = coinMinusLogs.stream().map(CoinMinusLog::getCoinId).collect(Collectors.toSet());
            Map<Long, Integer> minusCoinMap = coinMinusLogs.stream().collect(Collectors.toMap(CoinMinusLog::getCoinId, CoinMinusLog::getMinusCoin));

            List<Coin> coinList = getByCoinIds(coinIds);

            CoinLog coinLogRollback = rollbackCinLog(coinLog);
            List<Coin> coinRollback = rollbackCoin(coinList, minusCoinMap);
            List<CoinMinusLog> coinMinusLogRollback = rollbackCoinMinusLog(coinMinusLogs, coinLogRollback.getId());

            aiGenerateTaskManager.createRollback(coinLogRollback, coinRollback, coinMinusLogRollback, aiGenerateTask.getId());

        } finally {
            lock.unlock();
        }
    }

    private CoinLog rollbackCinLog(CoinLog coinLog) {
        CoinLog result = new CoinLog();
        result.setId(snowflakeIdService.getId());
        result.setUserId(coinLog.getUserId());
        result.setCoinId(coinLog.getCoinId());
        result.setCoin(-coinLog.getCoin());
        result.setEventType(CoinLogEventTypeEnum.AIGC_XIAO_HONG_SHU_ROLLBACK.getCode());
        result.setEventLogId(coinLog.getEventLogId());
        result.setCreateTime(LocalDateTime.now());
        return result;
    }

    private List<Coin> rollbackCoin(List<Coin> coinList, Map<Long, Integer> minusCoinMap) {
        List<Coin> result = new ArrayList<>(coinList.size());

        coinList.forEach(item -> {
            Coin coin = new Coin();
            coin.setId(item.getId());
            coin.setCoin(item.getCoin() + Optional.ofNullable(minusCoinMap.get(item.getId())).orElse(0));

            result.add(coin);
        });

        return result;
    }

    private List<CoinMinusLog> rollbackCoinMinusLog(List<CoinMinusLog> list, Long coinLogId) {
        List<CoinMinusLog> result = new ArrayList<>(list.size());
        list.forEach(item -> {
            CoinMinusLog coinMinusLog = new CoinMinusLog();
            coinMinusLog.setId(snowflakeIdService.getId());
            coinMinusLog.setCoinLogId(coinLogId);
            coinMinusLog.setCoinId(item.getCoinId());
            coinMinusLog.setMinusCoin(-item.getMinusCoin());
            coinMinusLog.setCreateTime(LocalDateTime.now());
            result.add(coinMinusLog);
        });

        return result;
    }

    private List<Coin> getByCoinIds(Set<Long> coinIds) {
        if (CollectionUtils.isEmpty(coinIds)) {
            return new ArrayList<>();
        }
        return coinManager.list(new QueryWrapper<Coin>()
                .in(Coin.ID, coinIds));
    }


}
