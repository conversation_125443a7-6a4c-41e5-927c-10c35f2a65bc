package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.dto.request.ActivationVipRequest;
import com.inngke.ai.crm.dto.request.BaseUserId;
import com.inngke.ai.crm.dto.request.VipInfoListDto;
import com.inngke.ai.crm.dto.request.base.UserIdRequest;
import com.inngke.ai.crm.dto.response.vip.UserVipInfoDto;
import com.inngke.ai.crm.dto.response.vip.VipInfoDto;
import com.inngke.ai.crm.service.VipService;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @chapter vip模块
 * @section vip管理
 */
@RestController
@RequestMapping("/api/ai/vip")
public class VipController {

    @Autowired
    private VipService vipService;

    /**
     * 获取会员类型列表(PC)
     */
    @GetMapping("/list/manage")
    public BaseResponse<List<VipInfoListDto>> getVipTypeList(@RequestAttribute JwtPayload jwtPayload) {
        UserIdRequest request = new UserIdRequest();
        request.setUserId(jwtPayload.getCid());
        return vipService.getVipTypeList(request);
    }

    /**
     * 获取会员类型列表(MP)
     */
    @GetMapping("/list")
    public BaseResponse<List<VipInfoListDto>> getVipTypeListMp(@RequestAttribute JwtPayload jwtPayload) {
        BaseUserId request = new BaseUserId();
        request.setUserId(jwtPayload.getCid());
        return vipService.getVipTypeListMp(request);
    }

    /**
     * 获取会员卡信息
     */
    @GetMapping("/{activityCode}")
    public BaseResponse<UserVipInfoDto> getUserVipInfo(
            @RequestAttribute JwtPayload jwtPayload,
            @PathVariable String activityCode) {
        return vipService.getUserVipInfo(activityCode);
    }

    /**
     * 激活会员卡
     */
    @PostMapping("/activation")
    public BaseResponse<String> activationVip(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody ActivationVipRequest request) {
        request.setUserId(jwtPayload.getCid());
        return vipService.activationVip(request);
    }

    /**
     * 购买会员卡
     */
    @PostMapping("/buy")
    public BaseResponse<Boolean> buyVip(@RequestAttribute JwtPayload jwtPayload, @RequestParam Long vipId) {
        return null;
    }
}
