package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.dto.response.UserInfoDto;
import com.inngke.ai.crm.service.qunfeng.QunFengService;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;


/**
 * @chapter 群峰服务市场
 * @section 群峰接口
 */
@RestController
@RequestMapping("/api/ai/qun-feng/")
public class QunFengController {

    @Autowired
    private QunFengService qunFengService;

    /**
     * 检查群峰用户订单
     */
    @PostMapping("/order/check")
    public BaseResponse<UserInfoDto> checkQunFengOrder(
            @RequestAttribute JwtPayload jwtPayload,
            HttpServletRequest httpServletRequest
    ){
        return qunFengService.checkUserOrders(jwtPayload, httpServletRequest.getServerName());
    }
}
