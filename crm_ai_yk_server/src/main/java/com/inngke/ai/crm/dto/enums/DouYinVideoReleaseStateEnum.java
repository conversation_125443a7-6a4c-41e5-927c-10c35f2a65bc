package com.inngke.ai.crm.dto.enums;

public enum DouYinVideoReleaseStateEnum {

    RELEASE_ERROR(-1, "发布失败",""),
    UN_RELEASE(0, "未发布",""),
    IN_RELEASE(1, "发布中","发布任务已提交，预计需要等待10~60秒"),
    RELEASED(2, "发布成功","任务已发布成功");

    private final Integer code;

    private final String name;

    private final String message;


    public static DouYinVideoReleaseStateEnum parse(Integer code){
        for (DouYinVideoReleaseStateEnum value : DouYinVideoReleaseStateEnum.values()) {
            if (value.code.equals(code)){
                return value;
            }
        }
        return null;
    }

    DouYinVideoReleaseStateEnum(Integer code, String name, String message) {
        this.code = code;
        this.name = name;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getMessage() {
        return message;
    }
}
