package com.inngke.ai.crm.controller.script;

import com.inngke.ai.crm.db.crm.manager.ScriptTypeManager;
import com.inngke.ai.crm.dto.request.script.*;
import com.inngke.ai.crm.dto.response.script.ScriptContentDetailResponse;
import com.inngke.ai.crm.dto.response.script.ScriptContentTitleDto;
import com.inngke.ai.crm.dto.response.script.ScriptTypeListResponse;
import com.inngke.ai.crm.dto.response.script.ScriptTypeTreeDto;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @chapter AI
 * @section 话术管理
 * <AUTHOR>
 * @Date 2024/4/3 10:11
 */
@RestController
@RequestMapping("/api/ai/script")
public class ScriptController {

    @Autowired
    ScriptTypeManager scriptTypeManager;

    /**
     * 获取code对应话术分类
     */
    @GetMapping("/getScriptTypeListByCode/{code}")
    public BaseResponse<List<ScriptTypeListResponse>> getScriptTypeListByCode(@PathVariable String code, @RequestAttribute JwtPayload jwtPayload) {
        return BaseResponse.success(scriptTypeManager.getScriptTypeListByCode(code, jwtPayload));
    }

    /**
     * 获取话术标题和内容长度分页列表
     */
    @GetMapping("/getScriptContentTitlePage")
    public BaseResponse<BasePaginationResponse<ScriptContentTitleDto>> getScriptContentTitlePage(ScriptContentTitleRequest scriptContentTitleRequest, @RequestAttribute JwtPayload jwtPayload) {
        return BaseResponse.success(scriptTypeManager.getScriptContentTitlePage(scriptContentTitleRequest, jwtPayload));
    }

    /**
     * 保存话术内容
     */
    @PostMapping("saveScriptContent")
    public BaseResponse<Boolean> saveScriptContent(@RequestBody ScriptContentRequest scriptContentRequest, @RequestAttribute JwtPayload jwtPayload) {
        return BaseResponse.success(scriptTypeManager.saveScriptContent(scriptContentRequest, jwtPayload));
    }

    /**
     * 删除话术内容
     */
    @DeleteMapping("deleteScriptContent/{contentId}")
    public BaseResponse<Boolean> deleteScriptContent(@PathVariable Long contentId, @RequestAttribute JwtPayload jwtPayload) {
        return BaseResponse.success(scriptTypeManager.deleteScriptContent(contentId));
    }

    /**
     * 编辑话术内容
     */
    @PutMapping("editScriptContent")
    public BaseResponse<Boolean> editScriptContent(@RequestBody ScriptContentEditRequest scriptContentEditRequest, @RequestAttribute JwtPayload jwtPayload) {
        return BaseResponse.success(scriptTypeManager.editScriptContent(scriptContentEditRequest));
    }

    /**
     * 根据话术内容ID获取话术详情
     */
    @GetMapping("getScriptContent/{contentId}")
    public BaseResponse<ScriptContentDetailResponse> getScriptContentDto(@PathVariable Long contentId, @RequestAttribute JwtPayload jwtPayload) {
        return BaseResponse.success(scriptTypeManager.getScriptContentDto(contentId));
    }

    /**
     * 保存话术分类
     */
    @PostMapping("saveScriptType")
    public BaseResponse<Boolean> saveScriptType(@RequestBody ScriptTypeSaveRequest scriptTypeSaveRequest, @RequestAttribute JwtPayload jwtPayload) {
        return BaseResponse.success(scriptTypeManager.saveScriptType(scriptTypeSaveRequest, jwtPayload));
    }

    /**
     * 删除话术分类
     */
    @DeleteMapping("deleteScriptType/{typeId}")
    public BaseResponse<Boolean> deleteScriptType(@PathVariable Long typeId, @RequestAttribute JwtPayload jwtPayload) {
        return BaseResponse.success(scriptTypeManager.deleteScriptType(typeId));
    }

    /**
     * 编辑话术分类
     */
    @PutMapping("editScriptType")
    public BaseResponse<Boolean> editScriptType(@RequestBody ScriptTypeEditRequest scriptTypeEditRequest, @RequestAttribute JwtPayload jwtPayload) {
        return BaseResponse.success(scriptTypeManager.editScriptType(scriptTypeEditRequest));
    }

    /**
     * 获取code对应的话术二级分类列表
     */
    @GetMapping("getScriptTypeList")
    public BaseResponse<List<ScriptTypeTreeDto>> getScriptTypeList(@RequestAttribute JwtPayload jwtPayload) {
        return BaseResponse.success(scriptTypeManager.getScriptTypeList(jwtPayload));
    }

    /**
     * 获取话术内容列表
     */
    @GetMapping("/getScriptContentList")
    public BaseResponse<BasePaginationResponse<ScriptContentDetailResponse>> getScriptContentList(ScriptContentPageRequest scriptContentPageRequest, @RequestAttribute JwtPayload jwtPayload) {
        return BaseResponse.success(scriptTypeManager.getScriptContentList(scriptContentPageRequest, jwtPayload));
    }
}
