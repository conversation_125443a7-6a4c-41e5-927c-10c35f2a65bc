package com.inngke.ai.crm.service.oauth;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.inngke.ai.crm.client.common.WxMpCommonServiceClientForAi;
import com.inngke.ai.crm.client.common.WxThirdPlatformServiceClientForAi;
import com.inngke.ai.crm.core.config.AiGcConfig;
import com.inngke.ai.crm.core.util.WeChatUtil;
import com.inngke.ai.crm.dto.enums.TripartiteEnum;
import com.inngke.ai.crm.service.CrmUserService;
import com.inngke.common.dto.request.BaseBidOptRequest;
import com.inngke.common.exception.InngkeErrorException;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.JsonService;
import com.inngke.common.wx.rpc.api.mp.MpAuthApi;
import com.inngke.common.wx.rpc.api.mp.WxMpModifyDomainApi;
import com.inngke.common.wx.rpc.dto.request.mp.GetUserPhoneRequest;
import com.inngke.common.wx.rpc.dto.response.mp.PhoneInfoDTO;
import com.inngke.common.wx.rpc.dto.response.mp.SessionResponse;
import com.inngke.common.wx.rpc.dto.response.mp.UserPhoneNumberResponse;
import com.inngke.ip.common.dto.request.GetAppConfRequest;
import com.inngke.ip.common.dto.request.GetWxTpAccountRequest;
import com.inngke.ip.common.dto.response.GetWxTpAccountDto;
import com.inngke.ip.common.dto.response.WxAccessTokenDto;
import com.inngke.ip.common.dto.response.WxAppConfDto;
import com.inngke.ip.common.dto.response.WxTpAccessTokenDto;
import com.inngke.ip.common.service.wx.WxMpCommonService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

@Component
public class WxTripartiteOauthService implements TripartiteOauthService {

    private static final Logger logger = LoggerFactory.getLogger(WxTripartiteOauthService.class);

    @Autowired
    private AiGcConfig aiGcConfig;
    @Autowired
    private WxThirdPlatformServiceClientForAi wxThirdPlatformServiceClientForAi;
    @Autowired
    private MpAuthApi mpAuthApi;
    @Autowired
    private WxMpCommonServiceClientForAi wxMpCommonService;
    @Autowired
    private WxMpModifyDomainApi wxMpModifyDomainApi;
    @Autowired
    private CrmUserService crmUserService;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Autowired
    private JsonService jsonService;

    @Override
    public TripartiteEnum getTripartite() {
        return TripartiteEnum.WX;
    }

    @Override
    public SessionResponse code2Session(String code) {
        Integer bid = aiGcConfig.getBid();
        GetAppConfRequest getAppConfRequest = new GetAppConfRequest();
        getAppConfRequest.setBid(bid);
        getAppConfRequest.setType(0);
        WxAppConfDto wxAppConfDto = wxThirdPlatformServiceClientForAi.getWxAppConf(getAppConfRequest);
        if (wxAppConfDto == null || StringUtils.isEmpty(wxAppConfDto.getId())) {
            logger.info("AI获取微信小程序登录配置错误1");
            return null;
        }

        GetWxTpAccountRequest getWxTpAccountRequest = new GetWxTpAccountRequest();
        getWxTpAccountRequest.setBid(bid);
        getWxTpAccountRequest.setWxTpAccountId(wxAppConfDto.getWxTpAccountId());
        GetWxTpAccountDto getWxTpAccountDto = wxThirdPlatformServiceClientForAi.getWxTpAccount(getWxTpAccountRequest);
        if (getWxTpAccountDto == null || StringUtils.isEmpty(getWxTpAccountDto.getTpAppId())) {
            return null;
        }

        BaseBidOptRequest mpLoginRequest = new BaseBidOptRequest();
        mpLoginRequest.setBid(bid);
        WxAccessTokenDto wxAccessTokenDto = wxThirdPlatformServiceClientForAi.getAccessToken(mpLoginRequest);
        if (wxAccessTokenDto == null || StringUtils.isEmpty(wxAccessTokenDto.getAccessToken())) {
            return null;
        }

        String appId = wxAppConfDto.getId();
        String tpAppId = getWxTpAccountDto.getTpAppId();
        String accessToken = wxAccessTokenDto.getAccessToken();

        return mpAuthApi.codeToSessionComponent(appId, code, "authorization_code", tpAppId, accessToken);
    }

    @Override
    public String getPhoneNumber(String code, Long userId, String iv, String encryptedData) {
        return !StringUtils.isEmpty(code) ? getMobileByCode(code) : getMobileByEncryptedData(userId, encryptedData, iv);
    }

    private String getMobileByCode(String code) {
        Integer bid = aiGcConfig.getBid();
        if (StringUtils.isEmpty(code)) {
            throw new InngkeErrorException("获取手机号失败，请重新授权");
        }
        WxTpAccessTokenDto wxTpAccessTokenDto = wxMpCommonService.getAccessToken(bid);
        if (wxTpAccessTokenDto == null || StringUtils.isEmpty(wxTpAccessTokenDto.getAccessToken())) {
            throw new InngkeServiceException("获取小程序令牌失败");
        }
        GetUserPhoneRequest phoneRequest = new GetUserPhoneRequest();
        phoneRequest.setCode(code);
        UserPhoneNumberResponse phoneInfo = wxMpModifyDomainApi.getUserPhone(wxTpAccessTokenDto.getAccessToken(), phoneRequest);
        if (phoneInfo.getErrCode() != 0) {
            throw new InngkeServiceException(phoneInfo.getErrMsg());
        }
        PhoneInfoDTO phone = phoneInfo.getPhoneInfo();
        if (phone == null || org.springframework.util.StringUtils.isEmpty(phone.getPhoneNumber())) {
            throw new InngkeErrorException("获取手机号失败，请重新授权");
        }
        return phone.getPhoneNumber();
    }

    private String getMobileByEncryptedData(Long cid, String encryptedData, String iv) {
        Integer bid = aiGcConfig.getBid();
        String sessionKey = crmUserService.getSessionKey(bid, cid, TripartiteEnum.WX.getCode());
        if (org.springframework.util.StringUtils.isEmpty(encryptedData) && org.springframework.util.StringUtils.isEmpty(iv)) {
            throw new InngkeServiceException("获取手机号参数不正确");
        }

        String mobile = WeChatUtil.decodeData(encryptedData, sessionKey, iv);
        if (org.springframework.util.StringUtils.isEmpty(mobile)) {
            throw new InngkeServiceException("获取手机号失败");
        }
        PhoneInfoDTO phoneInfoDTO = jsonService.toObject(mobile, PhoneInfoDTO.class);
        return phoneInfoDTO.getPhoneNumber();
    }


}
