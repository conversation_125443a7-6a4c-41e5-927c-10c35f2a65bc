package com.inngke.ai.crm.dto.response.creation.task;

import com.inngke.ai.crm.dto.response.creation.task.CreationTaskDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;


@Data
public class StaffCreationTaskDto implements Serializable {

    private Long id;

    /**
     * 任务名称
     */
    private String name;

    /**
     * 0:未完成 1:已完成
     */
    private Integer state;

    /**
     * 状态
     */
    private String stateText;

    /**
     * 任务开始时间
     */
    private Long startTime;

    /**
     * 任务结束时间
     */
    private Long endTime;

    /**
     * 任务表述
     */
    private String describe;

    /**
     * 任务类型 详见ai_product
     */
    private Integer aiProduct;

    /**
     * 创建人id
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Long createTime;

}
