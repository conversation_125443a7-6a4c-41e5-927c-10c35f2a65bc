package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.dto.request.CrmMiniAppLoginResponse;
import com.inngke.ai.crm.dto.request.CrmMpMiniAppLoginRequest;
import com.inngke.ai.crm.dto.request.CrmWebMiniAppAuthRequest;
import com.inngke.ai.crm.dto.request.CrmWebScanQrcodeRequest;
import com.inngke.ai.crm.dto.response.CrmWebMiniAppAuthDto;
import com.inngke.ai.crm.service.CrmLoginService;
import com.inngke.ai.crm.service.CrmUserService;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @chapter AI
 * @section 登录
 * @since 2023-10-17 10:22
 **/
@RestController
@RequestMapping("/api/ai/login")
public class CrmLoginController {

    @Autowired
    private CrmLoginService crmLoginService;

    /**
     * PC申请授权信息
     */
    @PostMapping("/qrcode")
    public BaseResponse<CrmWebMiniAppAuthDto> miniAppAuth(
            @RequestBody CrmWebMiniAppAuthRequest request
    ) {
        return crmLoginService.miniAppAuth(request);
    }

    /**
     * PC查询二维码授权结果
     */
    @GetMapping("/qrcode")
    public BaseResponse<CrmMiniAppLoginResponse> getQrcodeResult(@Validated CrmWebScanQrcodeRequest request) {
        return crmLoginService.getQrcodeResult(request);
    }

    /**
     * 小程序授权登录
     */
    @PostMapping("/miniApp-login")
    public BaseResponse<CrmMiniAppLoginResponse> miniAppAuthLogin(
            @RequestHeader(value = "x-forwarded-for", required = false) String ips,
            @RequestBody @Validated CrmMpMiniAppLoginRequest request,
            @RequestAttribute JwtPayload jwtPayload) {

        request.setUserId(jwtPayload.getCid());

        return crmLoginService.miniAppAuthLogin(request);
    }
}
