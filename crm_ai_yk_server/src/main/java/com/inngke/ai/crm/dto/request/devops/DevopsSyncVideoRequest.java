package com.inngke.ai.crm.dto.request.devops;

import java.io.Serializable;
import java.util.List;

public class DevopsSyncVideoRequest implements Serializable {

    /**
     * 构建任务id
     */
    private Long taskId;

    /**
     * 素材库id
     */
    private Long groupId;

    /**
     * 企业名称
     */
    private String brandName;

    /**
     * 文件路径
     */
    private List<String> filePathList;

    /**
     * 旋转角度
     */
    private Integer rotate;

    /**
     * 是否包含文件名称
     */
    private Boolean needFileName;

    public Long getTaskId() {
        return taskId;
    }

    public DevopsSyncVideoRequest setTaskId(Long taskId) {
        this.taskId = taskId;
        return this;
    }

    public Long getGroupId() {
        return groupId;
    }

    public DevopsSyncVideoRequest setGroupId(Long groupId) {
        this.groupId = groupId;
        return this;
    }

    public String getBrandName() {
        return brandName;
    }

    public DevopsSyncVideoRequest setBrandName(String brandName) {
        this.brandName = brandName;
        return this;
    }

    public List<String> getFilePathList() {
        return filePathList;
    }

    public DevopsSyncVideoRequest setFilePathList(List<String> filePathList) {
        this.filePathList = filePathList;
        return this;
    }

    public Integer getRotate() {
        return rotate;
    }

    public DevopsSyncVideoRequest setRotate(Integer rotate) {
        this.rotate = rotate;
        return this;
    }

    public Boolean getNeedFileName() {
        return needFileName;
    }

    public DevopsSyncVideoRequest setNeedFileName(Boolean needFileName) {
        this.needFileName = needFileName;
        return this;
    }
}
