package com.inngke.ai.crm.service;

import com.inngke.ai.crm.dto.request.org.DistributionVipRequest;
import com.inngke.ai.crm.dto.request.org.GetOrgDistributionLogPagingRequest;
import com.inngke.ai.crm.dto.response.org.OrganizeDistributionLogDto;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;

public interface UserVipService {
    BaseResponse<Boolean> distributionVip(DistributionVipRequest request);
}
