package com.inngke.ai.crm.service.schedule;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.ai.crm.core.CrmServiceConsts;
import com.inngke.ai.crm.db.crm.entity.PublishTask;
import com.inngke.ai.crm.db.crm.entity.PublishVideo;
import com.inngke.ai.crm.db.crm.manager.PublishTaskManager;
import com.inngke.ai.crm.db.crm.manager.PublishVideoManager;
import com.inngke.common.dto.Lock;
import com.inngke.common.service.LockService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.sql.Wrapper;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class PublishTaskSchedule {

    @Autowired
    private PublishVideoManager publishVideoManager;
    @Autowired
    private PublishTaskManager publishTaskManager;
    @Autowired
    private LockService lockService;

    /**
     * 每隔1分钟检查数据库回收一次未发布切已过期的视频
     */
    @Scheduled(fixedDelay = 60 * 1000)
    public void retrieveExpiredTasks() {
        Lock lock = lockService.getLock(CrmServiceConsts.CACHE_LOCK_KEY_PRE + "retrieve:expired:tasks", 3);
        if (Objects.isNull(lock)) {
            return;
        }

        List<Long> unfiredTaskIds = publishTaskManager.list(Wrappers.<PublishTask>query()
                .gt(PublishTask.EXPIRATION_TIME, LocalDateTime.now())
                .select(PublishTask.ID)
        ).stream().map(PublishTask::getId).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(unfiredTaskIds)) {
            return;
        }

        publishVideoManager.update(Wrappers.<PublishVideo>update()
                .in(PublishVideo.PUBLISH_TASK_ID, unfiredTaskIds)
                .eq(PublishVideo.PUBLISH_STATE, 1)
                .lt(PublishVideo.LOCK_TIME, LocalDateTime.now())

                .set(PublishVideo.PUBLISH_STATE, 0)
                .set(PublishVideo.LOCK_TIME, null)
                .set(PublishVideo.USER_ID, 0)
        );
    }
}
