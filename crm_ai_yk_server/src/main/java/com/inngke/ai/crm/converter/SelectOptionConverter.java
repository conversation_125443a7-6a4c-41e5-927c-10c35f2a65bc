package com.inngke.ai.crm.converter;

import com.inngke.ai.crm.core.util.FileUtils;
import com.inngke.ai.crm.db.crm.entity.TtsConfig;
import com.inngke.ai.crm.db.crm.entity.VideoBgmMaterial;
import com.inngke.ai.crm.dto.form.SelectOption;
import com.inngke.common.core.InngkeAppConst;
import org.springframework.util.StringUtils;

public class SelectOptionConverter {
    public static SelectOption toSelectOption(TtsConfig ttsConfig, String icon) {
        SelectOption selectOption = new SelectOption();
        selectOption.setIcon(icon);
        selectOption.setValue(ttsConfig.getId());
        selectOption.setTitle(ttsConfig.getTitle());
        return selectOption;
    }

    public static SelectOption toSelectOption(VideoBgmMaterial videoBgmMaterial) {
        SelectOption selectOption = new SelectOption();
        selectOption.setIcon("https://cos-publish-1301920668.cos.ap-shanghai.myqcloud.com/1/default/3fb57b649a31a4bf90b45d65938c1446.png");
        selectOption.setValue(videoBgmMaterial.getId());
        String fileName = videoBgmMaterial.getFileName();
        if (StringUtils.isEmpty(fileName)) {
            fileName = "未知音乐";
        } else {
            int index = fileName.lastIndexOf(InngkeAppConst.DOT_STR);
            if (index != -1) {
                fileName = fileName.substring(0, index);
            }
        }
        selectOption.setTitle(fileName);
        selectOption.setUrl(videoBgmMaterial.getUrl());
        return selectOption;
    }
}
