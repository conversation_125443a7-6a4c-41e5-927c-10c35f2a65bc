package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.dto.request.BaseImportRequest;
import com.inngke.ai.crm.dto.request.org.DeleteStaffRequest;
import com.inngke.ai.crm.dto.request.org.EditStaffRequest;
import com.inngke.ai.crm.dto.request.org.GetOrgStaffPagingRequest;
import com.inngke.ai.crm.dto.request.org.GetStaffInfoRequest;
import com.inngke.ai.crm.dto.request.org.staff.CreateStaffRequest;
import com.inngke.ai.crm.dto.response.org.StaffItemDto;
import com.inngke.ai.crm.service.EmployeeService;
import com.inngke.ai.crm.service.StaffImportService;
import com.inngke.ai.crm.service.StaffService;
import com.inngke.common.core.config.sign.ApiSignature;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @chapter 企业模块
 * @section 员工管理
 */
@RestController
@RequestMapping("/api/ai/organize/staff")
public class OrganizeStaffController {


    @Autowired
    private EmployeeService employeeService;

    @Autowired
    private StaffService staffService;

    @Autowired
    private StaffImportService staffImportService;

    /**
     * 创建员工
     */
    @PostMapping
    public BaseResponse<Boolean> createStaff(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody CreateStaffRequest request) {
        return staffService.createStaff(jwtPayload, request);
    }

    /**
     * 导入员工
     */
    @PostMapping("/import")
    public BaseResponse<BasePaginationResponse<StaffItemDto>> importStaff(
            @RequestAttribute JwtPayload jwtPayload, @RequestBody BaseImportRequest request) {
        request.setUserId(jwtPayload.getCid());
        return employeeService.importStaff(request);
    }


    /**
     * 编辑员工
     */
    @PutMapping("/{staffId:\\d+}")
    public BaseResponse<Boolean> editStaff(
            @RequestAttribute JwtPayload jwtPayload,
            @PathVariable Long staffId,
            @RequestBody EditStaffRequest request) {
        request.setStaffId(staffId);

        return staffService.editStaff(jwtPayload, request);
    }

    /**
     * 获取员工详情
     */
    @GetMapping("/{staffId:\\d+}")
    public BaseResponse<StaffInfoDto> getStaffInfo(@RequestAttribute JwtPayload jwtPayload, @PathVariable Long staffId) {
        GetStaffInfoRequest request = new GetStaffInfoRequest();
        request.setUserId(jwtPayload.getCid());
        request.setStaffId(staffId);

        return staffService.getStaffInfo(jwtPayload, request);
    }

    /**
     * 退出企业
     */
    @DeleteMapping("/{staffId:\\d+}/quit")
    public BaseResponse<Boolean> staffQuit(@RequestAttribute JwtPayload jwtPayload, @PathVariable Long staffId) {
        DeleteStaffRequest request = new DeleteStaffRequest();
        request.setUserId(jwtPayload.getCid());
        request.setStaffId(staffId);

        return staffService.quit(jwtPayload, request);
    }

    /**
     * 删除员工
     */
    @DeleteMapping("/{staffId:\\d+}")
    public BaseResponse<Boolean> deleteStaff(@RequestAttribute JwtPayload jwtPayload, @PathVariable Long staffId) {
        DeleteStaffRequest request = new DeleteStaffRequest();
        request.setUserId(jwtPayload.getCid());
        request.setStaffId(staffId);

        return staffService.delete(jwtPayload, request);
    }

    /**
     * 员工列表
     */
    @GetMapping("/paging")
    public BaseResponse<BasePaginationResponse<StaffItemDto>> getStaffPaging(
            @RequestAttribute JwtPayload jwtPayload, GetOrgStaffPagingRequest request) {
        return staffService.getStaffPaging(jwtPayload, request);
    }

    /**
     * 员工列表（退出）
     */
    @GetMapping("/quit/paging")
    public BaseResponse<BasePaginationResponse<StaffItemDto>> getQuiteStaffPaging(
            @RequestAttribute JwtPayload jwtPayload, GetOrgStaffPagingRequest request) {

        request.setUserId(jwtPayload.getCid());
        return employeeService.getQuiteStaffPaging(request);
    }

    /**
     * 部门-员工导入
     *
     * @param organizeId 组织ID
     * @param csvData    CSV文件内容，包含一行标题
     */
    @ApiSignature(signature = false)
    @PostMapping("/import/{organizeId:\\d+}")
    public BaseResponse<List<String>> importStaff(
            @PathVariable int organizeId,
            @RequestBody String csvData
    ) {
        return BaseResponse.success(staffImportService.staffImport(organizeId, csvData));
    }

}
