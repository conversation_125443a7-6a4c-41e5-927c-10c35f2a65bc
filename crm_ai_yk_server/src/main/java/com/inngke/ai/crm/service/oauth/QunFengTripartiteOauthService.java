package com.inngke.ai.crm.service.oauth;

import com.inngke.ai.crm.dto.enums.TripartiteEnum;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.utils.JsonUtil;
import com.inngke.common.wx.rpc.dto.response.mp.SessionResponse;
import com.inngke.ip.ai.qunfeng.api.QunFengOauthApi;
import com.inngke.ip.ai.qunfeng.dto.QunFengBaseResponse;
import com.inngke.ip.ai.qunfeng.dto.QunFengUserDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.Optional;

@Service
public class QunFengTripartiteOauthService implements TripartiteOauthService {

    private static final Logger logger = LoggerFactory.getLogger(QunFengTripartiteOauthService.class);

    @Autowired
    private QunFengOauthApi qunFengOauthApi;

    @Override
    public TripartiteEnum getTripartite() {
        return TripartiteEnum.QUN_FENG;
    }

    @Override
    public SessionResponse code2Session(String code) {
        QunFengBaseResponse<QunFengUserDto> response = qunFengOauthApi.getUserInfo(code);
        QunFengUserDto qunFengUserDto = Optional.ofNullable(response).map(QunFengBaseResponse::getData).orElse(null);
        if (Objects.isNull(qunFengUserDto)){
            logger.info("获取用户信息失败response:{},code:{}", JsonUtil.toJsonString(response), code);
            throw new InngkeServiceException("获取用户信息失败");
        }

        SessionResponse sessionResponse = new SessionResponse();
        sessionResponse.setOpenId(qunFengUserDto.getId().toString());

        return sessionResponse;
    }

    @Override
    public String getPhoneNumber(String code, Long userId, String iv, String encryptedData) {
        return null;
    }
}
