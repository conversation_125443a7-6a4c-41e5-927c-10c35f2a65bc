package com.inngke.ai.crm.dto.enums;

/**
 * <AUTHOR>
 * @since 2023-08-31 19:55
 **/
public enum CoinDispatchTypeEnum {
    REGISTER(1, "注册"),
    INVITE(2, "邀请"),
    ORDER(3, "订单"),
    RECEIVE_SHARE(4, "领取分享"),

    MANUAL(5, "后台添加"),

    VIP(6, "【VIP】卡到账"),
    S_VIP(7, "【SVIP】卡到账"),

    ORGANIZE_RECHARGE(8, "企业充值"),

    RELEASE_REWARD(9, "发布奖励"),

    QUN_FENG(10,"群峰")

    ;

    private final Integer code;

    private final String msg;


    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public static CoinDispatchTypeEnum parse(Integer code){
        for (CoinDispatchTypeEnum coinDispatchTypeEnum : CoinDispatchTypeEnum.values()) {
            if (coinDispatchTypeEnum.code.equals(code)){
                return coinDispatchTypeEnum;
            }
        }

        return null;
    }

    CoinDispatchTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
