/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.entity;

import java.time.LocalDateTime;
import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ProAiTask implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private Long pid;

    /**
     * 模型
     */
    private Integer typeId;

    /**
     * 请求消息
     */
    private String message;

    /**
     * 返回结果
     */
    private String response;

    /**
     * 状态 -1:失败 0:待处理 1:成功
     */
    private Integer status;

    /**
     * 错误信息
     */
    private String error;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    public static final String ID = "id";

    public static final String TYPE_ID = "type_id";

    public static final String MESSAGE = "message";

    public static final String RESPONSE = "response";

    public static final String ERROR = "error";

    public static final String STATUS = "status";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String PID = "pid";
}
