package com.inngke.ai.crm.service.devops;

import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.inngke.ai.crm.db.crm.entity.VideoMaterial;
import com.inngke.ai.crm.db.crm.manager.VideoMaterialDraftManager;
import com.inngke.ai.crm.db.crm.manager.VideoMaterialManager;
import com.inngke.ai.crm.dto.request.IdPageRequest;
import com.inngke.ai.crm.dto.request.devops.GetDetectionRequest;
import com.inngke.ai.crm.dto.request.devops.GetReDetectionListRequest;
import com.inngke.ai.crm.dto.request.devops.SaveDetectionResultRequest;
import com.inngke.ai.crm.dto.request.devops.SaveReDetectionRequest;
import com.inngke.ai.crm.dto.response.devops.VideoDetectionStatisticsDto;
import com.inngke.ai.crm.dto.response.devops.VideoDetectionDto;
import com.inngke.ai.crm.dto.response.devops.VideoReDetectionDto;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class VideoDetectionService {

    @Autowired
    private VideoMaterialManager videoMaterialManager;

    @Autowired
    private VideoMaterialDraftManager videoMaterialDraftManager;

    public BaseResponse<List<VideoReDetectionDto>> getReDetectionList(GetReDetectionListRequest request) {
        List<VideoMaterial> reDetectionList = videoMaterialManager.getReDetectionList(
                request.getLastId(), request.getDirId(), request.getPageSize()
        );

        return BaseResponse.success(reDetectionList.stream().map(videoMaterial -> {
            List<Integer> correctShakeSeconds = toSecondList(videoMaterial.getCorrectShakeSeconds());
            List<Integer> errorShakeSeconds = toSecondList(videoMaterial.getErrorShakeSeconds());

            return toSecondList(videoMaterial.getShakeSeconds()).stream().filter(
                    second -> !correctShakeSeconds.contains(second) && !errorShakeSeconds.contains(second)
            ).map(second -> {
                VideoReDetectionDto videoReDetection = new VideoReDetectionDto();
                videoReDetection.setId(videoMaterial.getId());
                videoReDetection.setUrl(videoMaterial.getUrl());
                videoReDetection.setSecond(second);
                return videoReDetection;
            }).sorted(Comparator.comparingInt(VideoReDetectionDto::getSecond)).collect(Collectors.toList());
        }).filter(CollectionUtils::isNotEmpty).flatMap(List::stream).collect(Collectors.toList()));
    }

    public BaseResponse<Boolean> saveReDetection(JwtPayload jwtPayload, SaveReDetectionRequest request) {
        Long detectionUserId = jwtPayload.getSid();

        if (Objects.isNull(request.getStatus())) {
            return BaseResponse.success(true);
        }

        VideoMaterial videoMaterial = videoMaterialManager.getById(request.getId());

        List<Integer> correctShakeSeconds = toSecondList(videoMaterial.getCorrectShakeSeconds());
        List<Integer> errorShakeSeconds = toSecondList(videoMaterial.getErrorShakeSeconds());
        if (request.getStatus() == 1) {
            correctShakeSeconds.add(request.getSecond());
            errorShakeSeconds.remove(request.getSecond());
        } else {
            errorShakeSeconds.add(request.getSecond());
            correctShakeSeconds.remove(request.getSecond());
        }

        //todo 同步向量库
        return BaseResponse.success(videoMaterialManager.updateDetectionResult(
                detectionUserId, videoMaterial.getId(), toSecondStr(correctShakeSeconds), toSecondStr(errorShakeSeconds)
        ));
    }

    private List<Integer> toSecondList(String secondsStr) {
        if (StringUtils.isBlank(secondsStr)) {
            return Lists.newArrayList();
        }
        return Lists.newArrayList(Splitter.on(InngkeAppConst.COMMA_STR).split(secondsStr))
                .stream().filter(StringUtils::isNotBlank).map(Integer::valueOf).collect(Collectors.toList());
    }

    private String toSecondStr(List<Integer> secondList) {
        if (CollectionUtils.isEmpty(secondList)) {
            return InngkeAppConst.EMPTY_STR;
        }

        return Joiner.on(InngkeAppConst.COMMA_STR).join(Sets.newHashSet(secondList).stream().sorted().collect(Collectors.toList()));
    }

    public BaseResponse<Integer> getSurplusCount(GetReDetectionListRequest request) {
        return BaseResponse.success(
                videoMaterialManager.getReDetectionSurplusCount(request.getLastId(), request.getDirId())
        );
    }

    public BaseResponse<BasePaginationResponse<VideoDetectionDto>> getDetection(GetDetectionRequest request) {
        BasePaginationResponse<VideoDetectionDto> response = new BasePaginationResponse<>();
        response.setList(Lists.newArrayList());
        response.setTotal(0);
        if (Objects.isNull(request.getDirId()) || Objects.isNull(request.getStatus())) {
            return BaseResponse.success(response);
        }

        //materialId仅在构建成功状态搜索
        if (request.getStatus() != 2){
            request.setMaterialId(null);
        }
        response.setList(
                videoMaterialDraftManager.getDetectionList(
                        request.getDirId().toString(), request.getStatus(), request.getMaterialId(),
                        request.getPageNo(), request.getPageSize()
                )
        );

        response.setTotal(
                videoMaterialDraftManager.getDetectionCount(
                        request.getDirId().toString(), request.getStatus(), request.getMaterialId()
                )
        );

        return BaseResponse.success(response);
    }

    public BaseResponse<List<VideoDetectionDto>> getAwaitingDetectionList(IdPageRequest request) {
        return BaseResponse.success(videoMaterialManager.getAwaitingList(
                        Optional.ofNullable(request.getLastId()).orElse(0L), request.getPageSize()
                ).stream().map(videoMaterial -> {
                    VideoDetectionDto videoDetectionDto = new VideoDetectionDto();
                    videoDetectionDto.setId(videoMaterial.getId());
                    videoDetectionDto.setUrl(videoMaterial.getUrl());
                    return videoDetectionDto;
                }).collect(Collectors.toList())
        );
    }

    public BaseResponse<Boolean> saveDetectionResult(SaveDetectionResultRequest request) {
        return BaseResponse.success(videoMaterialManager.saveDetectionResult(
                request.getId(), request.getShakeSeconds()
        ));
    }

    public BaseResponse<List<VideoDetectionStatisticsDto>> statistics() {
        return BaseResponse.success(videoMaterialManager.statistics());
    }
}
