package com.inngke.ai.crm.service.impl;

import com.google.common.collect.Lists;
import com.inngke.ai.crm.client.common.WxThirdPlatformServiceClientForAi;
import com.inngke.ai.crm.core.config.AiGcConfig;
import com.inngke.ai.crm.core.util.RsaUtils;
import com.inngke.ai.crm.db.crm.entity.*;
import com.inngke.ai.crm.db.crm.manager.*;
import com.inngke.ai.crm.dto.enums.*;
import com.inngke.ai.crm.dto.request.CrmQueryOrderRequest;
import com.inngke.ai.crm.dto.request.WxPrepayRequest;
import com.inngke.ai.crm.dto.request.WxRefundRequest;
import com.inngke.ai.crm.dto.response.CrmQueryOrderDto;
import com.inngke.ai.crm.dto.response.WxPayNotifyDto;
import com.inngke.ai.crm.dto.response.WxPrePayDto;
import com.inngke.ai.crm.service.AiLockService;
import com.inngke.ai.crm.service.CrmWxPayService;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.JsonService;
import com.inngke.common.service.SnowflakeIdService;
import com.inngke.ip.common.dto.request.GetAppConfRequest;
import com.inngke.ip.common.dto.response.WxAppConfDto;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.core.exception.ServiceException;
import com.wechat.pay.java.core.exception.ValidationException;
import com.wechat.pay.java.core.notification.NotificationParser;
import com.wechat.pay.java.core.notification.RequestParam;
import com.wechat.pay.java.service.payments.jsapi.JsapiService;
import com.wechat.pay.java.service.payments.jsapi.model.Amount;
import com.wechat.pay.java.service.payments.jsapi.model.*;
import com.wechat.pay.java.service.payments.model.Transaction;
import com.wechat.pay.java.service.refund.RefundService;
import com.wechat.pay.java.service.refund.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2023-09-12 09:31
 **/
@Service
public class CrmWxPayServiceImpl implements CrmWxPayService {

    private static final Logger logger = LoggerFactory.getLogger(CrmWxPayServiceImpl.class);

    @Autowired
    private SnowflakeIdService snowflakeIdService;

    @Autowired
    private WxTradeFlowManager wxTradeFlowManager;

    @Autowired
    private UserManager userManager;

    @Autowired
    private RSAAutoCertificateConfig wxPayConfig;

    @Autowired
    private WxThirdPlatformServiceClientForAi wxThirdPlatformServiceClientForAi;

    @Autowired
    private AppConfigManager appConfigManager;

    @Autowired
    private AiGcConfig aiGcConfig;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private CoinProductManager coinProductManager;

    @Autowired
    private CoinOrderManager coinOrderManager;

    @Autowired
    private AiLockService lockService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private CoinManager coinManager;

    @Autowired
    private CoinLogManager coinLogManager;

    @Autowired
    private UserVipManager userVipManager;

    public static final String WX_PREPAY = "crm_ai_yk:wxPrepay:";

    @Override
    public BaseResponse<Boolean> refund(WxRefundRequest request) {
        WxTradeFlow wxTradeFlow = wxTradeFlowManager.getById(request.getId());
        if (Objects.isNull(wxTradeFlow) || StringUtils.isEmpty(wxTradeFlow.getTransactionId())) {
            return BaseResponse.error("用户未支付");
        }
        Long amount = 1L;

        Long outRefundNo = snowflakeIdService.getId();

        String notify = appConfigManager.getValueByCode(AppConfigCodeEnum.WX_PAY_REFUND_NOTIFY.getCode());

        RefundService refundService = new RefundService.Builder().config(wxPayConfig).build();
        CreateRequest createRequest = new CreateRequest();
        createRequest.setOutTradeNo(wxTradeFlow.getOutTradeNo());
        createRequest.setOutRefundNo(outRefundNo.toString());
        AmountReq amountReq = new AmountReq();
        amountReq.setRefund(amount);
        amountReq.setTotal(wxTradeFlow.getAmount().longValue());
        amountReq.setCurrency("CNY");
        createRequest.setAmount(amountReq);
        createRequest.setNotifyUrl(notify);
        Refund refund = refundService.create(createRequest);

        WxTradeFlow saveDate = new WxTradeFlow();
        saveDate.setId(snowflakeIdService.getId());
        saveDate.setUserId(wxTradeFlow.getUserId());
        saveDate.setOutTradeNo(wxTradeFlow.getOutTradeNo());
        saveDate.setOutRequestNo(outRefundNo.toString());
        saveDate.setTransactionId(refund.getRefundId());
        saveDate.setTradeType(WxTradeFlowTradeTypeEnum.REFUND.getCode());
        saveDate.setAmount(amount.intValue());
        saveDate.setTradeStatus(WxTradeFlowTradeStatusEnum.NOT_PAY.getCode());
        LocalDateTime now = LocalDateTime.now();
        saveDate.setCreateTime(now);
        saveDate.setUpdateTime(now);

        wxTradeFlowManager.save(saveDate);

        return BaseResponse.success(true);
    }

    private String getWxPrepayKey(Long userId, Long coinProductId) {
        return WX_PREPAY + userId + ":" + coinProductId;
    }

    @Override
    public BaseResponse<WxPrePayDto> prepay(WxPrepayRequest request) {
        User user = userManager.getById(request.getUserId());
        if (Objects.isNull(user)) {
            return BaseResponse.error("用户不存在");
        }
        CoinProduct coinProduct = coinProductManager.getById(request.getCoinProductId());
        if (Objects.isNull(coinProduct) || !coinProduct.getEnable().equals(1)) {
            return BaseResponse.error("套餐已下架");
        }
        // 校验
        prepayCheck(request, coinProduct);

        String redisKey = getWxPrepayKey(request.getUserId(), request.getCoinProductId());
        String value = (String) redisTemplate.opsForValue().get(redisKey);
        if (Objects.nonNull(value)) {
            return BaseResponse.success(jsonService.toObject(value, WxPrePayDto.class));
        }

        Integer amountNum = coinProduct.getAmount();
        String title = coinProduct.getTitle();

        String appId = getAppId();
        Map<String, String> map = appConfigManager.getValueByCodeList(Lists.newArrayList(AppConfigCodeEnum.WX_PAY_MERCHANT_ID.getCode(), AppConfigCodeEnum.WX_PAY_PRIVATE_KEY.getCode(), AppConfigCodeEnum.WX_PAY_PAY_NOTIFY.getCode()));

        String privateKey = map.get(AppConfigCodeEnum.WX_PAY_PRIVATE_KEY.getCode());
        String merChantId = map.get(AppConfigCodeEnum.WX_PAY_MERCHANT_ID.getCode());
        String notify = map.get(AppConfigCodeEnum.WX_PAY_PAY_NOTIFY.getCode());

        JsapiService service = new JsapiService.Builder().config(wxPayConfig).build();

        Long outTradeNo = snowflakeIdService.getId();
        PrepayRequest prepayRequest = prepayRequest(merChantId, appId, user.getMpOpenId(), outTradeNo, amountNum, notify, title);

        // 调用下单方法，得到应答
        PrepayResponse response = service.prepay(prepayRequest);
        String prepayId = response.getPrepayId();
        WxPrePayDto wxPrePayDto = new WxPrePayDto();
        wxPrePayDto.setId(outTradeNo);
        wxPrePayDto.setPackageStr("prepay_id=" + prepayId);
        wxPrePayDto.setTimeStamp(String.valueOf(Instant.now().getEpochSecond()));
        wxPrePayDto.setNonceStr(UUID.randomUUID().toString().replace("-", ""));
        try {
            String content = appId + InngkeAppConst.TURN_LINE + wxPrePayDto.getTimeStamp() + InngkeAppConst.TURN_LINE + wxPrePayDto.getNonceStr() + InngkeAppConst.TURN_LINE + wxPrePayDto.getPackageStr() + InngkeAppConst.TURN_LINE;
            String sign = RsaUtils.sign(content, privateKey);
            wxPrePayDto.setPaySign(sign);

            WxTradeFlow wxTradeFlow = wxTradeFlow(user.getId(), outTradeNo, amountNum);
            CoinOrder coinOrder = coinOrder(outTradeNo, request.getUserId(), coinProduct);

            coinOrderManager.saveOrder(wxTradeFlow, coinOrder);

            // 设置redis
            redisTemplate.opsForValue().set(redisKey, jsonService.toJson(wxPrePayDto), 110, TimeUnit.MINUTES);

            return BaseResponse.success(wxPrePayDto);
        } catch (Exception e) {
            logger.info("生成微信预付款异常:", e);
            return BaseResponse.error("生成订单失败");
        }
    }

    private void prepayCheck(WxPrepayRequest request, CoinProduct coinProduct) {
        if (coinProduct.getType().equals(CoinProductTypeEnum.FIRST_ORDER.getCode())) {
            Integer count = coinOrderManager.getUserBuyCount(request.getUserId(), coinProduct.getId());
            if (!count.equals(0)) {
                throw new InngkeServiceException("超出最大购买次数");
            }
        }
    }

    private CoinOrder coinOrder(Long orderId, Long userId, CoinProduct coinProduct) {
        CoinOrder result = new CoinOrder();
        result.setId(snowflakeIdService.getId());
        result.setOrderId(orderId);
        result.setCoinProductId(coinProduct.getId());
        result.setUserId(userId);
        result.setStatus(0);
        LocalDateTime now = LocalDateTime.now();
        result.setExpireTime(now.plusHours(2));
        result.setCreateTime(now);
        result.setUpdateTime(now);
        result.setTitle(coinProduct.getTitle());
        result.setOrgAmount(coinProduct.getOrgAmount());
        result.setAmount(coinProduct.getAmount());
        result.setCoin(coinProduct.getCoin());
        return result;
    }

    private PrepayRequest prepayRequest(String merChantId, String appId, String openId, Long outTradeNo, Integer amountNum, String notify, String title) {
        PrepayRequest prepayRequest = new PrepayRequest();
        prepayRequest.setMchid(merChantId);
        prepayRequest.setOutTradeNo(outTradeNo.toString());
        prepayRequest.setAppid(appId);
        prepayRequest.setDescription(title);
        prepayRequest.setNotifyUrl(notify);
        Amount amount = new Amount();
        amount.setTotal(amountNum);
        amount.setCurrency("CNY");
        prepayRequest.setAmount(amount);
        Payer payer = new Payer();
        payer.setOpenid(openId);
        prepayRequest.setPayer(payer);
        return prepayRequest;
    }

    /**
     * 获取AppId
     */
    private String getAppId() {
        GetAppConfRequest getAppConfRequest = new GetAppConfRequest();
        getAppConfRequest.setBid(aiGcConfig.getBid());
        getAppConfRequest.setType(0);
        WxAppConfDto wxAppConfDto = wxThirdPlatformServiceClientForAi.getWxAppConf(getAppConfRequest);
        return wxAppConfDto.getId();
    }

    private WxTradeFlow wxTradeFlow(Long userId, Long outTradeNo, Integer amount) {
        WxTradeFlow wxTradeFlow = new WxTradeFlow();
        wxTradeFlow.setId(outTradeNo);
        wxTradeFlow.setUserId(userId);
        wxTradeFlow.setOutTradeNo(outTradeNo.toString());
        wxTradeFlow.setOutRequestNo(outTradeNo.toString());
        wxTradeFlow.setTradeType(WxTradeFlowTradeTypeEnum.ORDER.getCode());
        wxTradeFlow.setAmount(amount);
        wxTradeFlow.setTradeStatus(WxTradeFlowTradeStatusEnum.NOT_PAY.getCode());
        LocalDateTime now = LocalDateTime.now();
        wxTradeFlow.setCreateTime(now);
        wxTradeFlow.setUpdateTime(now);
        return wxTradeFlow;
    }


    @Override
    public BaseResponse<CrmQueryOrderDto> queryOrder(CrmQueryOrderRequest request) {
        String metChantId = appConfigManager.getValueByCode(AppConfigCodeEnum.WX_PAY_MERCHANT_ID.getCode());

        QueryOrderByOutTradeNoRequest queryOrderByOutTradeNoRequest = new QueryOrderByOutTradeNoRequest();

        queryOrderByOutTradeNoRequest.setMchid(metChantId);
        queryOrderByOutTradeNoRequest.setOutTradeNo(request.getOutTradeNo());
        try {
            JsapiService service = new JsapiService.Builder().config(wxPayConfig).build();
            Transaction transaction = service.queryOrderByOutTradeNo(queryOrderByOutTradeNoRequest);
            Transaction.TradeStateEnum tradeState = transaction.getTradeState();
            // 支付成功
            if (Transaction.TradeStateEnum.SUCCESS.equals(tradeState)) {

            }
            return BaseResponse.success();
        } catch (ServiceException e) {
            logger.info("code={}, message={},response body={}", e.getErrorCode(), e.getErrorMessage(), e.getResponseBody());
            return BaseResponse.error("查询失败");
        }
    }

    @Override
    public WxPayNotifyDto payNotify(RequestParam requestParam) {
        logger.info("微信支付异步通知请求参数：{}", requestParam.toString());

        com.wechat.pay.java.service.partnerpayments.app.model.Transaction transaction = getNotifyEntity(requestParam, com.wechat.pay.java.service.partnerpayments.app.model.Transaction.class);

        String transactionId = transaction.getTransactionId();
        String outTradeNo = transaction.getOutTradeNo();

        logger.info("微信支付异步通知transactionId:{},outTradeNo:{}", transactionId, outTradeNo);

        // 加锁
        lockService.getWxPayNotifyLock(outTradeNo, true);

        WxTradeFlow wxTradeFlow = wxTradeFlowManager.getByOutTradeNo(outTradeNo);
        if (Objects.isNull(wxTradeFlow)) {
            throw new InngkeServiceException(HttpStatus.SERVICE_UNAVAILABLE.getReasonPhrase());
        }
        // 已经成功
        if (WxTradeFlowTradeStatusEnum.SUCCESS.getCode().equals(wxTradeFlow.getTradeStatus())) {
            return WxPayNotifyDto.success();
        }

        com.wechat.pay.java.service.partnerpayments.app.model.Transaction.TradeStateEnum tradeState = transaction.getTradeState();
        // 支付成功
        if (!WxTradeFlowTradeStatusEnum.SUCCESS.getCode().equals(wxTradeFlow.getTradeStatus()) && com.wechat.pay.java.service.partnerpayments.app.model.Transaction.TradeStateEnum.SUCCESS.equals(tradeState) && !wxTradeFlow.getTradeStatus().equals(WxTradeFlowTradeStatusEnum.SUCCESS.getCode())) {
            payNotifyPaySuccess(wxTradeFlow.getId(), transactionId);
        }
        return WxPayNotifyDto.success();
    }

    private void payNotifyPaySuccess(Long outTradeId, String transactionId) {
        CoinOrder coinOrder = coinOrderManager.getByOrderId(outTradeId);
        if (Objects.isNull(coinOrder)) {
            logger.info("微信支付回调，找不到CoinOrder：{}", outTradeId);
            return;
        }
        CoinProduct coinProduct = coinProductManager.getById(coinOrder.getCoinProductId());
        if (Objects.isNull(coinProduct)) {
            logger.info("微信支付回调，找不到CoinProduct：outTradeId:{},coinProductId:{}", outTradeId, coinOrder.getCoinProductId());
            return;
        }
        WxTradeFlow wxTradeFlowUpdate = new WxTradeFlow();
        wxTradeFlowUpdate.setId(outTradeId);
        wxTradeFlowUpdate.setTradeStatus(WxTradeFlowTradeStatusEnum.SUCCESS.getCode());
        wxTradeFlowUpdate.setTransactionId(transactionId);
        wxTradeFlowUpdate.setTradeDate(LocalDateTime.now());

        CoinOrder coinOrderUpdate = new CoinOrder();
        coinOrderUpdate.setId(coinOrder.getId());
        coinOrderUpdate.setStatus(CoinOrderStatusEnum.SUCCESS.getCode());


        // 周期月卡
        CoinProductPeriodTypeEnum code = CoinProductPeriodTypeEnum.getByCode(coinProduct.getPeriodType());
        if (Objects.nonNull(code) && !code.getUserVipCount().equals(0)) {
            payNotifyPaySuccessPeriodType1(coinOrder, coinProduct, wxTradeFlowUpdate, coinOrderUpdate, outTradeId);
        }
        // 一次性
        if (coinProduct.getPeriodType().equals(0)) {
            payNotifyPaySuccessPeriodType0(coinOrder, coinProduct, wxTradeFlowUpdate, coinOrderUpdate, outTradeId);
        }

        String redisKey = getWxPrepayKey(coinOrder.getUserId(), coinOrder.getCoinProductId());
        redisTemplate.delete(redisKey);
    }

    private void payNotifyPaySuccessPeriodType1(CoinOrder coinOrder, CoinProduct coinProduct, WxTradeFlow wxTradeFlowUpdate, CoinOrder coinOrderUpdate, Long outTradeId) {
        Long userId = coinOrder.getUserId();
        User user = userManager.getById(userId);
        if (Objects.isNull(user)) {
            logger.info("微信支付回调，找不到用户不存在：{}", userId);
            return;
        }
        Coin coin = null;
        CoinLog coinLog = null;
        User userUpdate = null;
        UserVip userVip = payNotifyPaySuccessUserVip(coinOrder.getId(), coinProduct, user);

        // 没有VIP直接变成ID，并且插入积分
        if (userManager.userVipExpire(user.getCurrentVipExpiredTime())) {
            coin = coinManager.createCoin(coinProduct.getCoin(), userId, outTradeId, coinManager.getTypeByVipType(coinProduct.getVipType()), userManager.getVipExpireTimeAfterDistribute(null, userVip));
            coinLog = coinLogManager.createCoinLog(coin, outTradeId, coinLogManager.getTypeByVipType(coinProduct.getVipType()));
            userVip.setRemainCount(userVip.getRemainCount() - 1);

            userUpdate = new User();
            userUpdate.setId(userId);
            userUpdate.setCurrentVipType(userVip.getVipType());
            userUpdate.setCurrentVipId(userVip.getId());
            userUpdate.setCurrentVipExpiredTime(userManager.getVipExpireTimeAfterDistribute(null, userVip));
        }
        coinOrderManager.orderPaySuccess(wxTradeFlowUpdate, coinOrderUpdate, coin, coinLog, userVip, userUpdate);
    }

    private void payNotifyPaySuccessPeriodType0(CoinOrder coinOrder, CoinProduct coinProduct, WxTradeFlow wxTradeFlowUpdate, CoinOrder coinOrderUpdate, Long outTradeId) {
        Long userId = coinOrder.getUserId();
        Coin coin = payNotifyPaySuccessCoin(coinProduct, userId, outTradeId);
        CoinLog coinLog = payNotifyPaySuccessCoinLog(coin, userId, outTradeId);
        coinOrderManager.orderPaySuccess(wxTradeFlowUpdate, coinOrderUpdate, coin, coinLog, null, null);
    }

    private UserVip payNotifyPaySuccessUserVip(Long coinOrderId, CoinProduct coinProduct, User user) {
        UserVip userVip = new UserVip();
        userVip.setId(snowflakeIdService.getId());
        userVip.setOrganizeId(0L);
        userVip.setUserId(user.getId());
        userVip.setMobile(user.getMobile());
        userVip.setRealName(user.getRealName());
        userVip.setCoinProductId(coinProduct.getId());
        userVip.setCoinOrderId(coinOrderId);
        userVip.setVipType(coinProduct.getVipType());
        userVip.setPeriodType(coinProduct.getPeriodType());
        userVip.setCoin(coinProduct.getCoin());

        CoinProductPeriodTypeEnum code = CoinProductPeriodTypeEnum.getByCode(coinProduct.getPeriodType());
        if (Objects.isNull(code)) {
            logger.info("payNotifyPaySuccessUserVip获取CoinProductPeriodTypeEnum失败:{}", coinProduct.getPeriodType());
            throw new InngkeServiceException("payNotifyPaySuccessUserVip获取CoinProductPeriodTypeEnum失败");
        }

        userVip.setRemainCount(code.getUserVipCount());
        userVip.setTotalCount(code.getUserVipCount());
        userVip.setEnable(true);
        userVip.setCreateTime(LocalDateTime.now());
        return userVip;
    }

    private Coin payNotifyPaySuccessCoin(CoinProduct coinProduct, Long userId, Long outTradeId) {
        Coin coin = new Coin();
        coin.setId(snowflakeIdService.getId());
        coin.setUserId(userId);
        coin.setCoin(coinProduct.getCoin());
        coin.setDispatchId(coin.getId());
        coin.setDispatchType(coinManager.getTypeByVipType(coinProduct.getVipType()).getCode());
        coin.setDispatchSrcId(outTradeId);
        coin.setTotalCoin(coin.getCoin());
        coin.setExpireTime(coinManager.notExpireTime());
        LocalDateTime now = LocalDateTime.now();
        coin.setCreateTime(now);
        coin.setUpdateTime(now);
        return coin;
    }

    private CoinLog payNotifyPaySuccessCoinLog(Coin coin, Long userId, Long outTradeId) {
        CoinLog coinLog = new CoinLog();
        coinLog.setId(snowflakeIdService.getId());
        coinLog.setUserId(userId);
        coinLog.setCoinId(coin.getId());
        coinLog.setCoin(coin.getCoin());
        coinLog.setEventType(CoinLogEventTypeEnum.ORDER.getCode());
        coinLog.setEventLogId(outTradeId);
        coinLog.setCreateTime(LocalDateTime.now());
        return coinLog;
    }

    /**
     * 一次性充值产品
     */

    private <T> T getNotifyEntity(RequestParam requestParam, Class<T> tClass) {
        try {
            NotificationParser parser = new NotificationParser(wxPayConfig);
            T result = parser.parse(requestParam, tClass);
            // 如果处理失败，应返回 4xx/5xx 的状态码，例如 500 INTERNAL_SERVER_ERROR
            if (Objects.isNull(result)) {
                throw new InngkeServiceException(HttpStatus.UNAUTHORIZED.getReasonPhrase());
            }
            return result;
        } catch (ValidationException e) {
            throw new InngkeServiceException(HttpStatus.UNAUTHORIZED.getReasonPhrase());
        }
    }

    @Override
    public WxPayNotifyDto refundNotify(RequestParam requestParam) {
        RefundNotification transaction = getNotifyEntity(requestParam, RefundNotification.class);
        String outRefundNo = transaction.getOutRefundNo();

        WxTradeFlow wxTradeFlow = wxTradeFlowManager.getByOutRequestNo(outRefundNo);
        if (Objects.isNull(wxTradeFlow)) {
            throw new InngkeServiceException(HttpStatus.SERVICE_UNAVAILABLE.getReasonPhrase());
        }

        Status refundStatus = transaction.getRefundStatus();
        String refundId = transaction.getRefundId();

        // 退款成功
        if (Status.SUCCESS.equals(refundStatus)) {
            WxTradeFlow update = new WxTradeFlow();
            update.setId(wxTradeFlow.getId());
            update.setTradeStatus(WxTradeFlowTradeStatusEnum.SUCCESS.getCode());
            update.setTransactionId(refundId);
            update.setTradeDate(LocalDateTime.now());
            wxTradeFlowManager.updateById(update);
        }

        return WxPayNotifyDto.success();
    }


}
