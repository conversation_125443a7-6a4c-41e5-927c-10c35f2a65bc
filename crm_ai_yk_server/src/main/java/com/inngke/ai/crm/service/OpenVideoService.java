package com.inngke.ai.crm.service;

import com.inngke.ai.crm.dto.request.video.OpenVideoGenerateRequest;
import com.inngke.ai.crm.dto.request.video.OpenVideoTaskQuery;
import com.inngke.ai.crm.dto.response.video.OpenVideoTaskDto;

import java.util.List;

public interface OpenVideoService {
    Long create(OpenVideoGenerateRequest request);

    OpenVideoTaskDto getTaskState(long taskId);

    List<OpenVideoTaskDto> taskList(OpenVideoTaskQuery request);
}
