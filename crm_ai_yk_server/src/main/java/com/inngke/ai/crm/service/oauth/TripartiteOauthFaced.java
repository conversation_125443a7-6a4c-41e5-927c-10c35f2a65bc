package com.inngke.ai.crm.service.oauth;

import com.inngke.ai.crm.dto.enums.TripartiteEnum;
import com.inngke.common.wx.rpc.dto.response.mp.SessionResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class TripartiteOauthFaced {

    @Autowired
    private List<TripartiteOauthService> tripartiteOauthServices;

    public SessionResponse code2Session(TripartiteEnum tripartite, String code) {
        for (TripartiteOauthService tripartiteOauthService : tripartiteOauthServices) {
            if (tripartiteOauthService.getTripartite().equals(tripartite)) {
                return tripartiteOauthService.code2Session(code);
            }
        }

        return null;
    }

    public String getPhoneNumber(TripartiteEnum tripartite, String code, Long userId, String iv, String encryptedData) {
        for (TripartiteOauthService tripartiteOauthService : tripartiteOauthServices) {
            if (tripartiteOauthService.getTripartite().equals(tripartite)) {
                return tripartiteOauthService.getPhoneNumber(code, userId, iv, encryptedData);
            }
        }

        return null;
    }

}
