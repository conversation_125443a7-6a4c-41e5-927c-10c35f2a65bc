package com.inngke.ai.crm.dto.enums;

public enum VideoCreationStageEnum {
    CREATE_CONTENT_BY_VIDEO_KEY(10, "通过视频关键信息，生成视频内容"),
    SKIP_SCRIPT(11, "跳过脚本"),
    CREATE_CONTENT_BY_USER_SCRIPT(12, "通过用户脚本，生成视频内容"),
    MATCH_MATERIAL(20, "素材匹配"),
    RE_MATCH_MATERIAL(30, "素材重新匹配"),
    FINISH_VIDEO(100, "完成视频创作");

    private final Integer code;
    private final String desc;

    VideoCreationStageEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static VideoCreationStageEnum getByCode(Integer code) {
        for (VideoCreationStageEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
