package com.inngke.ai.crm.dto.response.material;

import com.inngke.ai.dto.BaseVideoMaterial;
import lombok.Data;

import java.util.List;

@Data
public class MaterialDto extends BaseVideoMaterial {

    /**
     * 素材ID
     *
     * @demo 12345
     */
    private Long id;

    /**
     * -2=识别内容失败 -1=已删除 0=初始化 1=识别内容成功
     */
    private Integer status;

    /**
     * 分类ID列表
     *
     * @demo [1, 2]
     */
    private List<MaterialCategoryDto> categoryList;

    /**
     * 创建时间
     */
    private Long createTime;
}