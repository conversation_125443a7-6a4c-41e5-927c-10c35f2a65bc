package com.inngke.ai.crm.dto.request.org;

import com.inngke.ai.crm.dto.response.org.StaffItemDto;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class DistributionVipRequest implements Serializable {

    /**
     * 数量
     *
     * @demo 100
     */
    @Min(1)
    private Integer num;

    /**
     * 会员Id
     */
    @NotNull
    private Long productId;

    /**
     * 前端不用传
     */
    private Long userId;

    /**
     * 员工列表
     */
    @NotEmpty
    private List<StaffItemDto> staffList;


    private Boolean free;

    public static Integer fee(DistributionVipRequest request, Integer amount) {
        Integer num = request.getNum();
        List<StaffItemDto> staffList = request.getStaffList();
        int size = staffList.size();
        return size * num * amount;
    }
}
