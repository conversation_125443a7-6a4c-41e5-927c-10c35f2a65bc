/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager;

import com.inngke.ai.crm.db.crm.entity.MaterialCategory;
import com.inngke.ai.crm.db.crm.entity.VideoMaterial;
import com.baomidou.mybatisplus.extension.service.IService;
import com.inngke.ai.crm.dto.response.devops.VideoDetectionStatisticsDto;

import java.util.List;

/**
 * <p>
 * 多媒体-素材库 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-27
 */
public interface VideoMaterialManager extends IService<VideoMaterial> {
    void materialUse(List<Long> materialIds);

    List<VideoMaterial> getReDetectionList(Long lastId, Long dirId, Integer pageSize);

    Boolean updateDetectionResult(Long detectionUserId, Long videoMaterialId, String correctShakeSeconds, String errorShakeSeconds);

    Integer getReDetectionSurplusCount(Long lastId, Long dirId);

    List<VideoMaterial> getAwaitingList(Long lastId, Integer pageSize);

    Boolean saveDetectionResult(Long id, String shakeSeconds);

    List<VideoMaterial> getByIds(List<Long> materialIds,String... columns);

    VideoMaterial getBySourceMd5(String sourceMd5);

    List<VideoDetectionStatisticsDto> statistics();

    List<VideoMaterial> getRecommendList();

    void saveCopyRecommendMaterial(List<VideoMaterial> videoMaterials, List<MaterialCategory> materialCategoryList);
}
