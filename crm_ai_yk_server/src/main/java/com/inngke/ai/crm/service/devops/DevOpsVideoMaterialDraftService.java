package com.inngke.ai.crm.service.devops;

import com.inngke.ai.crm.db.crm.entity.VideoMaterialDir;
import com.inngke.ai.crm.db.crm.entity.VideoMaterialDraft;
import com.inngke.ai.crm.db.crm.manager.VideoMaterialDirManager;
import com.inngke.ai.crm.db.crm.manager.VideoMaterialDraftManager;
import com.inngke.ai.crm.dto.request.IdPageRequest;
import com.inngke.ai.crm.dto.request.devops.AddVideoMaterialDraftRequest;
import com.inngke.ai.crm.dto.request.devops.AuditVideoMaterialDraftRequest;
import com.inngke.ai.crm.dto.request.devops.SetBuildFinishRequest;
import com.inngke.ai.crm.dto.response.devops.VideoMaterialDraftDto;
import com.inngke.common.core.utils.SnowflakeHelper;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.service.LockService;
import com.inngke.common.utils.JsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class DevOpsVideoMaterialDraftService {

    private static final String VIDEO_MATERIAL_DRAFT_DIR_LOCK = "video_material_draft";

    @Autowired
    private VideoMaterialDraftManager videoMaterialDraftManager;
    @Autowired
    private VideoMaterialDirManager videoMaterialDirManager;
    @Autowired
    private LockService lockService;

    public BaseResponse<Boolean> exist(String sourceMd5) {
        return BaseResponse.success(videoMaterialDraftManager.existBySourceMd5(sourceMd5));
    }

    public BaseResponse<Long> add(AddVideoMaterialDraftRequest request) {
        if (videoMaterialDraftManager.existBySourceMd5(request.getSourceMd5())) {
            return BaseResponse.success(videoMaterialDraftManager.getBySourceMd5(request.getSourceMd5()).getId());
        }

        VideoMaterialDraft videoMaterialDraft = toVideoMaterialDraft(request);

        String srcPath = request.getSrcPath();

        File srcFile = new File(srcPath);

        String dir = srcFile.getParent();

        List<VideoMaterialDir> dirTreeList = videoMaterialDirManager.getDirTreeListNotExistSave(dir);

        Long organizeId = null;
        String categoryIds = null;

        for (VideoMaterialDir videoMaterialDir : dirTreeList) {
            if (Objects.nonNull(videoMaterialDir.getOrganizeId()) && videoMaterialDir.getOrganizeId() > 0L) {
                organizeId = videoMaterialDir.getOrganizeId();
            }
            if (StringUtils.isNotBlank(videoMaterialDir.getCategoryIds())) {
                categoryIds = videoMaterialDir.getCategoryIds();
            }
        }

        videoMaterialDraft.setOrganizeId(organizeId);
        videoMaterialDraft.setCategoryIds(categoryIds);
        videoMaterialDraft.setDirIds(JsonUtil.toJsonString(dirTreeList.stream().map(VideoMaterialDir::getId).collect(Collectors.toList())));

        videoMaterialDraftManager.save(videoMaterialDraft);

        return BaseResponse.success(videoMaterialDraft.getId());
    }

    private VideoMaterialDraft toVideoMaterialDraft(AddVideoMaterialDraftRequest request) {
        VideoMaterialDraft videoMaterialDraft = new VideoMaterialDraft();
        videoMaterialDraft.setId(SnowflakeHelper.getId());
        videoMaterialDraft.setUrl(request.getUrl());
        videoMaterialDraft.setSourceMd5(request.getSourceMd5());
        videoMaterialDraft.setSrcPath(request.getSrcPath());
        videoMaterialDraft.setWidth(request.getWidth());
        videoMaterialDraft.setHeight(request.getHeight());
        videoMaterialDraft.setVideoDuration(request.getDuration());
        videoMaterialDraft.setCreateTime(LocalDateTime.now());

        return videoMaterialDraft;
    }

    public BaseResponse<Boolean> audit(AuditVideoMaterialDraftRequest request) {
        return BaseResponse.success(
                videoMaterialDraftManager.audit(request.getIds(), request.getStatus(), request.getRotate())
        );
    }

    public BaseResponse<List<VideoMaterialDraftDto>> getWaitingBuildList(IdPageRequest request) {
        return BaseResponse.success(
                videoMaterialDraftManager.getWaitingBuildList(
                        Optional.ofNullable(request.getLastId()).orElse(0L), request.getPageSize()
                ).stream().map(this::toVideoMaterialDraftDto).collect(Collectors.toList())
        );
    }

    private VideoMaterialDraftDto toVideoMaterialDraftDto(VideoMaterialDraft videoMaterialDraft) {
        VideoMaterialDraftDto videoMaterialDraftDto = new VideoMaterialDraftDto();
        videoMaterialDraftDto.setId(videoMaterialDraft.getId());
        videoMaterialDraftDto.setMaterialId(videoMaterialDraft.getMaterialId());
        videoMaterialDraftDto.setStatus(videoMaterialDraft.getStatus());
        videoMaterialDraftDto.setUrl(videoMaterialDraft.getUrl());
        videoMaterialDraftDto.setOrganizeId(videoMaterialDraft.getOrganizeId());
        videoMaterialDraftDto.setCategoryIds(videoMaterialDraft.getCategoryIds());
        videoMaterialDraftDto.setVertical(videoMaterialDraft.getVertical());
        videoMaterialDraftDto.setDirIds(videoMaterialDraft.getDirIds());
        videoMaterialDraftDto.setSourceMd5(videoMaterialDraft.getSourceMd5());
        videoMaterialDraftDto.setSrcPath(videoMaterialDraft.getSrcPath());
        videoMaterialDraftDto.setWidth(videoMaterialDraft.getWidth());
        videoMaterialDraftDto.setHeight(videoMaterialDraft.getHeight());
        videoMaterialDraftDto.setVideoDuration(videoMaterialDraft.getVideoDuration());
        videoMaterialDraftDto.setRotate(videoMaterialDraft.getRotate());
        videoMaterialDraftDto.setErrorMessage(videoMaterialDraft.getErrorMessage());
        return videoMaterialDraftDto;
    }

    public BaseResponse<Boolean> buildFinish(SetBuildFinishRequest request) {
        if (Objects.isNull(request.getId()) || Objects.isNull(request.getMaterialId())){
            return BaseResponse.success(false);
        }

        return BaseResponse.success(
                videoMaterialDraftManager.setBuildFinish(request.getId(),request.getMaterialId())
        );
    }
}
