package com.inngke.ai.crm.service;

import cn.hutool.core.lang.Validator;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Splitter;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.inngke.ai.crm.db.crm.entity.Department;
import com.inngke.ai.crm.db.crm.entity.Staff;
import com.inngke.ai.crm.db.crm.manager.DepartmentManager;
import com.inngke.ai.crm.db.crm.manager.OrganizeManager;
import com.inngke.ai.crm.db.crm.manager.StaffManager;
import com.inngke.ai.crm.dto.StaffImportDto;
import com.inngke.ai.crm.dto.enums.StaffStateEnum;
import com.inngke.ai.crm.service.es.StaffEsService;
import com.inngke.ai.crm.service.impl.DepartmentCacheFactory;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.SnowflakeIdService;
import com.inngke.common.utils.ValidateUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.MalformedURLException;
import java.net.URL;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
public class StaffImportService {

    private static final Map<String, String> STAFF_IMPORT_HEADER_ALIAS;

    static {
        STAFF_IMPORT_HEADER_ALIAS = MapUtil.newHashMap(true);
        STAFF_IMPORT_HEADER_ALIAS.put("员工姓名", "name");
        STAFF_IMPORT_HEADER_ALIAS.put("手机号", "mobile");
        STAFF_IMPORT_HEADER_ALIAS.put("部门", "departmentName");
        STAFF_IMPORT_HEADER_ALIAS.put("备注", "remark");
    }

    @Autowired
    private StaffManager staffManager;

    @Autowired
    private DepartmentManager departmentManager;

    @Autowired
    private OrganizeManager organizeManager;

    @Autowired
    private SnowflakeIdService snowflakeIdService;

    @Autowired
    private StaffEsService staffEsService;

    @Autowired
    private DepartmentCacheFactory departmentCacheFactory;

    public List<StaffImportDto> analyzingStaffExcel(String url) {
        List<StaffImportDto> staffImportList = readStaffFromUrl(url);
        Set<String> mobileSet = Sets.newHashSet();
        for (StaffImportDto staffImportDto : staffImportList) {
            String mobile = StringUtils.trimToNull(staffImportDto.getMobile());
            staffImportDto.setMobile(mobile);
            staffImportDto.setDepartmentName(StringUtils.trimToNull(staffImportDto.getDepartmentName()));
            staffImportDto.setRemark(StringUtils.trimToNull(staffImportDto.getRemark()));
            staffImportDto.setName(StringUtils.trimToNull(staffImportDto.getName()));

            if (StringUtils.isBlank(staffImportDto.getName())) {
                throw new InngkeServiceException("导入失败，员工姓名没有填写");
            }

            if (StringUtils.isBlank(mobile)) {
                throw new InngkeServiceException("导入失败，员工手机号没有填写：" + staffImportDto.getName());
            }
            if (!Validator.isMobile(mobile)) {
                throw new InngkeServiceException("导入失败，员工手机号格式错误：" + mobile);
            }
            if (mobileSet.contains(mobile)) {
                throw new InngkeServiceException("导入失败，重复手机号：" + mobile);
            }
            mobileSet.add(mobile);
        }

        return staffImportList;
    }

    protected List<StaffImportDto> readStaffFromUrl(String urlStr) {
        try {
            URL url = new URL(urlStr);

            ExcelReader reader = ExcelUtil.getReader(URLUtil.getStream(url), 0);
            reader.setHeaderAlias(STAFF_IMPORT_HEADER_ALIAS);
            return reader.read(1, 2, Integer.MAX_VALUE, StaffImportDto.class);
        } catch (MalformedURLException e) {
            throw new InngkeServiceException(e);
        }
    }

    public List<String> staffImport(int organizeId, String csvData) {
        List<CsvItem> items = Lists.newArrayList();
        List<String> errors = Lists.newArrayList();
        if (organizeManager.getById(organizeId) == null) {
            errors.add("企业不存在：id=" + organizeId);
            return errors;
        }
        Map<String, Department> deptNameMaps = Maps.newHashMap();
        Map<Long, Department> deptIdMaps = Maps.newHashMap();
        List<Department> depts = departmentManager.list(
                Wrappers.<Department>query()
                        .eq(Department.ORGANIZE_ID, organizeId)
                        .eq(Department.DELETED, false)
                        .orderByAsc(Department.PARENT_ID)
        );
        depts.forEach(dept -> {
            deptIdMaps.put(dept.getId(), dept);
        });
        depts.forEach(dept -> {
            if (dept.getParentId().equals(0L)) {
                deptNameMaps.put(dept.getName(), dept);
                return;
            }
            String fullName = getDeptFullName(dept, deptIdMaps);
            deptNameMaps.put(fullName, dept);
        });

        int index = csvData.indexOf(InngkeAppConst.TURN_LINE);
        if (index != -1) {
            //去掉标题行
            csvData = csvData.substring(index + 1);
        }

        Splitter.on(InngkeAppConst.TURN_LINE)
                .omitEmptyStrings()
                .trimResults()
                .split(csvData)
                .forEach(line -> {
                    String[] is = line.split(InngkeAppConst.COMMA_STR);
                    if (is.length != 5) {
                        errors.add("数据格式错误：" + line);
                        return;
                    }
                    CsvItem item = new CsvItem();
                    item.deptName1 = is[0];
                    item.deptName2 = is[1];
                    item.deptName3 = is[2];
                    item.name = is[3];
                    item.mobile = is[4];
                    items.add(item);
                });

        List<Long> staffIds = Lists.newArrayList();
        if (!items.isEmpty()) {
            items.forEach(item -> {
                String error = importItem(organizeId, deptNameMaps, item, staffIds);
                if (!StringUtils.isEmpty(error)) {
                    errors.add(error);
                }
            });
        }
        if (!staffIds.isEmpty()) {
            departmentCacheFactory.incCacheVersion(organizeId);
            staffEsService.updateEsDocByIds(staffIds);
        }

        return errors;
    }

    private String getDeptFullName(Department dept, Map<Long, Department> deptIdMaps) {
        Long parentId = dept.getParentId();
        if (parentId == 0L) {
            return dept.getName();
        }
        Department parentDept = deptIdMaps.get(parentId);
        if (parentDept == null) {
            return dept.getName();
        }
        return getDeptFullName(parentDept, deptIdMaps) + "." + dept.getName();
    }

    private String importItem(int organizeId, Map<String, Department> deptNameMaps, CsvItem item, List<Long> staffIds) {
        if (!ValidateUtils.isMobile(item.mobile)) {
            return "手机号码不合法：" + item.mobile;
        }
        if (StringUtils.isEmpty(item.name)) {
            return "mobile=" + item.mobile + "的员工姓名不能为空";
        }
        Department staffDepartment;
        Department dept = deptNameMaps.get(item.deptName1);
        if (dept == null) {
            //一级部门不存在
            dept = createDept(organizeId, item.deptName1, 0L);
            deptNameMaps.put(item.deptName1, dept);
        }
        staffDepartment = dept;

        if (!StringUtils.isEmpty(item.deptName2)) {
            Department dept2 = deptNameMaps.get(item.deptName1 + "." + item.deptName2);
            if (dept2 == null) {
                //二级部门不存在
                dept2 = createDept(organizeId, item.deptName2, dept.getId());
                deptNameMaps.put(item.deptName1 + "." + item.deptName2, dept2);
            }
            staffDepartment = dept2;

            if (!StringUtils.isEmpty(item.deptName3)) {
                Department dept3 = deptNameMaps.get(item.deptName1 + "." + item.deptName2 + "." + item.deptName3);
                if (dept3 == null) {
                    //三级部门不存在
                    dept3 = createDept(organizeId, item.deptName3, dept2.getId());
                    deptNameMaps.put(item.deptName1 + "." + item.deptName2 + "." + item.deptName3, dept3);
                }
                staffDepartment = dept3;
            }
        }

        //创建员工
        Staff staff = staffManager.getOne(
                Wrappers.<Staff>query()
                        .eq(Staff.ORGANIZE_ID, organizeId)
                        .eq(Staff.DELETED, false)
                        .eq(Staff.MOBILE, item.mobile)
        );
        if (staff != null) {
            //更新
            staff.setName(item.name);
            staff.setDeleted(false);
            staff.setDepartmentId(staffDepartment.getId());
            staffManager.updateById(staff);
        } else {
            staff = new Staff();
            staff.setId(snowflakeIdService.getId());
            staff.setUserId(0L);
            staff.setOrganizeId((long) organizeId);
            staff.setDepartmentId(staffDepartment.getId());
            staff.setName(item.name);
            staff.setMobile(item.mobile);
            staff.setState(StaffStateEnum.UNREGISTERED.getState());
            staff.setRemark("");
            staff.setDeleted(false);
            LocalDateTime now = LocalDateTime.now();
            staff.setCreateTime(now);
            staff.setUpdateTime(now);
            staff.setTester(false);
            staffManager.save(staff);
        }
        staffIds.add(staff.getId());
        return null;
    }

    private Department createDept(int organizeId, String deptName, long parentId) {
        Department dept = new Department();
        dept.setId(snowflakeIdService.getId());
        dept.setOrganizeId((long) organizeId);
        dept.setName(deptName);
        dept.setParentId(parentId);
        dept.setDeleted(false);
        LocalDateTime now = LocalDateTime.now();
        dept.setCreateTime(now);
        dept.setUpdateTime(now);
        departmentManager.save(dept);
        return dept;
    }

    static class CsvItem {
        String deptName1;
        String deptName2;
        String deptName3;
        String mobile;
        String name;
    }
}
