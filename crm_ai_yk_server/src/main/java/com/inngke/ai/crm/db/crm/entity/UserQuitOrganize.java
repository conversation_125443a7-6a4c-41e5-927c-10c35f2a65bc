/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 退出企业记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class UserQuitOrganize implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 企业id
     */
    private Long organizeId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 员工姓名
     */
    private String realName;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 部门id
     */
    private String departmentName;

    /**
     * 部门id
     */
    private Long departmentId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    public static final String ID = "id";

    public static final String ORGANIZE_ID = "organize_id";

    public static final String USER_ID = "user_id";

    public static final String REAL_NAME = "real_name";

    public static final String MOBILE = "mobile";

    public static final String DEPARTMENT = "department_name";

    public static final String DEPARTMENT_ID = "department_id";

    public static final String REMARK = "remark";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

}
