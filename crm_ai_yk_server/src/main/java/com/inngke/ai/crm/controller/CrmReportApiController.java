package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.dto.request.bi.BiUrlRequest;
import com.inngke.ai.crm.service.QuickBiService;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @chapter 企业模块
 * @section BI
 */
@RestController
@RequestMapping("/api/ai/bi")
public class CrmReportApiController {
    @Autowired
    private QuickBiService quickBiService;

    /**
     * 获取报表链接
     * 获取到的链接只可打开2次，10分钟内有效
     */
    @PostMapping("/url")
    public BaseResponse<String> getReportUrl(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody BiUrlRequest request
    ) {
        return BaseResponse.success(quickBiService.createBiUrl(jwtPayload, request));
    }
}
