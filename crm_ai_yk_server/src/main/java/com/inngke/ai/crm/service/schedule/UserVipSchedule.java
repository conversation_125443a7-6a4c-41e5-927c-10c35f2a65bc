package com.inngke.ai.crm.service.schedule;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inngke.ai.crm.db.crm.entity.*;
import com.inngke.ai.crm.db.crm.manager.*;
import com.inngke.ai.crm.dto.enums.CoinDispatchTypeEnum;
import com.inngke.ai.crm.dto.enums.CoinLogEventTypeEnum;
import com.inngke.ai.crm.service.AiLockService;
import com.inngke.common.dto.Lock;
import com.inngke.common.service.LockService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-10-20 17:07
 **/
@Component
@Slf4j
public class UserVipSchedule {

    @Autowired
    private UserManager userManager;

    @Autowired
    private UserVipManager userVipManager;

    @Autowired
    private CoinProductManager coinProductManager;

    @Autowired
    private CoinManager coinManager;

    @Autowired
    private CoinLogManager coinLogManager;

    @Autowired
    private LockService lockService;


    /**
     * 每天凌晨，退还过期的会员
     */
    @Scheduled(cron = "59 00 00 * * *")
    public void returnOrgFee() {

        Lock lock = lockService.getLock("crm_ai_yk:lock:returnOrgFee", 600);
        if (Objects.isNull(lock)) {
            return;
        }

        log.info("退还过期的会员卡给企业定时任务开始");

        List<UserVip> userVipList = userVipManager.list(new QueryWrapper<UserVip>()
                .eq(UserVip.USER_ID, 0L)
                .eq(UserVip.ENABLE, 1)
                .ne(UserVip.REMAIN_COUNT, 0)
                .ne(UserVip.ORGANIZE_ID, 0)
                .le(UserVip.CREATE_TIME, userVipManager.nowTimeMinusVipExpireTime()));
        if (CollectionUtils.isEmpty(userVipList)) {
            return;
        }
        Map<Long, List<UserVip>> organizeIdMap = userVipList.stream().collect(Collectors.groupingBy(UserVip::getOrganizeId));

        organizeIdMap.forEach(this::returnOrgFee);
    }

    private void returnOrgFee(Long organizeId, List<UserVip> userVipList) {
        if (CollectionUtils.isEmpty(userVipList)) {
            return;
        }
        Set<Long> productIdList = userVipList.stream().map(UserVip::getCoinProductId).collect(Collectors.toSet());

        Map<Long, CoinProduct> coinProductIdMap = coinProductManager.list(new QueryWrapper<CoinProduct>()
                        .in(CoinProduct.ID, productIdList))
                .stream().collect(Collectors.toMap(CoinProduct::getId, Function.identity()));

        List<UserVip> userVipUpdateList = new ArrayList<>(userVipList.size());

        int fee = 0;

        for (UserVip item : userVipList) {
            CoinProduct coinProduct = coinProductIdMap.get(item.getCoinProductId());
            if (Objects.isNull(coinProduct)) {
                return;
            }
            fee = fee + (coinProduct.getAmount() * item.getRemainCount());

            UserVip userVip = new UserVip();
            userVip.setId(item.getId());
            userVip.setRemainCount(0);
            userVipUpdateList.add(userVip);
        }
        List<Long> list = userVipUpdateList.stream().map(UserVip::getId).collect(Collectors.toList());
        log.info("退还过期的会员卡定时任务，organizeId:{},金额：{},userVipIds：{}", organizeId, fee, list);

        userVipManager.returnOrgFee(userVipUpdateList, organizeId, fee);
    }


    /**
     * 每天凌晨给用户发放积分或者过期会员
     */
    @Scheduled(cron = "59 59 01 * * *")
    public void userVipExpire() {
        Lock lock = lockService.getLock("crm_ai_yk:lock:userVipExpire", 600);
        if (Objects.isNull(lock)) {
            return;
        }

        log.info("每天凌晨给用户发放积分或者过期会员定时任务开始");

        List<User> list = userManager.list(
                new QueryWrapper<User>()
                        .le(User.CURRENT_VIP_EXPIRED_TIME, LocalDateTime.now())
                        .ge(User.CURRENT_VIP_EXPIRED_TIME, LocalDateTime.now().minusDays(3))
                        .isNotNull(User.CURRENT_VIP_EXPIRED_TIME)
        );
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<Long> userIdList = list.stream().map(User::getId).collect(Collectors.toList());

        List<UserVip> userVipList = userVipManager.list(new QueryWrapper<UserVip>()
                .in(UserVip.USER_ID, userIdList)
                .eq(UserVip.ENABLE, 1)
                .ne(UserVip.REMAIN_COUNT, 0));

        Map<Long, CoinProduct> coinProductMap = coinProductManager.list(new QueryWrapper<CoinProduct>()
                        .ne(CoinProduct.VIP_TYPE, 0))
                .stream().collect(Collectors.toMap(CoinProduct::getId, Function.identity()));

        Map<Long, List<UserVip>> userVipMap = new HashMap<>();

        userVipList.forEach(item -> userVipMap.computeIfAbsent(item.getUserId(), l -> new ArrayList<>()).add(item));

        List<User> userUpdateList = new ArrayList<>();
        List<UserVip> userVipUpdateList = new ArrayList<>();
        List<Coin> insertCoinList = new ArrayList<>();
        List<CoinLog> insertCoinLogList = new ArrayList<>();

        list.forEach(item ->
                userVipExpireDo(item, userVipMap.get(item.getId()), coinProductMap, userUpdateList, userVipUpdateList, insertCoinList, insertCoinLogList));

        userVipManager.userVipExpireSchedule(userUpdateList, userVipUpdateList, insertCoinList, insertCoinLogList);

        log.info("每天凌晨给用户发放积分或者过期会员定时任务结束");
    }

    private void userVipExpireDo(User user, List<UserVip> userVipList, Map<Long, CoinProduct> coinProductMap,
                                 List<User> userUpdateList, List<UserVip> userVipUpdateList,
                                 List<Coin> insertCoinList, List<CoinLog> insertCoinLogList) {
        if (CollectionUtils.isEmpty(userVipList) && !user.getCurrentVipId().equals(0L)) {
//            User userUpdate = new User();
//            userUpdate.setId(user.getId());
//            userUpdate.setCurrentVipId(0L);
//            userUpdateList.add(userUpdate);
//            log.info("用户会员卡过期,userId:{}",user.getId());
            return;
        }
        if (CollectionUtils.isEmpty(userVipList)) {
            return;
        }

        userVipList.sort((u1, u2) -> {
            int vipType = u2.getVipType().compareTo(u1.getVipType());
            if (vipType != 0) {
                return vipType;
            }
            return u2.getOrganizeId().compareTo(u1.getOrganizeId());
        });

        UserVip userVip = userVipList.get(0);
        CoinProduct coinProduct = coinProductMap.get(userVip.getCoinProductId());
        if (Objects.isNull(coinProduct)) {
            return;
        }

        User userUpdate = new User();
        userUpdate.setId(user.getId());
        userUpdate.setCurrentVipId(userVip.getId());
        userUpdate.setCurrentVipExpiredTime(LocalDateTime.now().plusMonths(1));
        userUpdate.setCurrentVipType(userVip.getVipType());

        userUpdateList.add(userUpdate);

        UserVip userVipUpdate = new UserVip();
        userVipUpdate.setId(userVip.getId());
        userVipUpdate.setRemainCount(userVip.getRemainCount() - 1);

        userVipUpdateList.add(userVipUpdate);

        Coin coin = coinManager.createCoin(coinProduct.getCoin(), user.getId(), null, coinManager.getTypeByVipType(coinProduct.getVipType()), userManager.getVipExpireTimeAfterDistribute(null, userVip));
        CoinLog coinLog = coinLogManager.createCoinLog(coin, null, coinLogManager.getTypeByVipType(coinProduct.getVipType()));

        insertCoinList.add(coin);
        insertCoinLogList.add(coinLog);

        log.info("用户会员卡续期,userId:{},续期会员卡userVipId:{}", user.getId(), userVip.getId());
    }


}
