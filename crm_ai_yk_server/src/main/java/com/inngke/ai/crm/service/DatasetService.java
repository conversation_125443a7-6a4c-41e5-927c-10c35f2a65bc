package com.inngke.ai.crm.service;

import com.inngke.ai.crm.dto.request.video.VideoDatasetCreateRequest;
import com.inngke.ai.crm.dto.response.VideoIndexTaskDto;
import com.inngke.common.dto.JwtPayload;

public interface DatasetService {
    /**
     * 向数据集添加视频任务
     * 注意：本接口仅创建任务，并不是完成任务！
     *
     * @param jwtPayload 当前用户
     * @param request    添加请求
     * @return 任务
     */
    VideoIndexTaskDto index(JwtPayload jwtPayload, VideoDatasetCreateRequest request);

    /**
     * 获取视频索引任务状态
     *
     * @param jwtPayload 当前用户
     * @param taskId     任务ID
     * @return 任务状态
     */
    VideoIndexTaskDto getVideoIndexTask(JwtPayload jwtPayload, long taskId);

    /**
     * 删除数据集中的某个文件
     *
     * @param jwtPayload 当前用户
     * @param datasetId  数据集ID
     * @param fileId     文件ID
     * @return 是否删除成功
     */
    Boolean deleteDataset(JwtPayload jwtPayload, String datasetId, String fileId);
}
