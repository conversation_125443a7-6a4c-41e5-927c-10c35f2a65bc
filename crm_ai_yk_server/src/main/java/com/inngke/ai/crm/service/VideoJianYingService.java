package com.inngke.ai.crm.service;

import com.inngke.ai.crm.db.crm.entity.VideoCreateTask;
import com.inngke.ai.crm.dto.request.video.VideoJianyingTaskFetchRequest;
import com.inngke.ai.dto.request.JianYingCreatedRequest;
import com.inngke.ai.dto.request.JianYingTaskUpdateRequest;
import com.inngke.ai.dto.response.JianYingTask;

public interface VideoJianYingService {
    void jianyingCreate(JianYingCreatedRequest request);

    void jianYingTaskUpdate(JianYingTaskUpdateRequest request);

    boolean createRetryTask(VideoCreateTask videoCreateTask);

    JianYingTask getJianYingTask(VideoJianyingTaskFetchRequest request);
}
