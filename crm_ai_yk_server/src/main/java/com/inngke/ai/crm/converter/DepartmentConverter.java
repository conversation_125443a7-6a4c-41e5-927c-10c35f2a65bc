package com.inngke.ai.crm.converter;

import com.google.common.collect.Lists;
import com.inngke.ai.crm.db.crm.entity.Department;
import com.inngke.ai.crm.db.crm.entity.Staff;
import com.inngke.ai.crm.db.crm.entity.User;
import com.inngke.ai.crm.dto.request.org.department.CreateDepartmentRequest;
import com.inngke.ai.crm.dto.response.org.department.DepartmentDto;
import com.inngke.ai.crm.dto.response.org.department.DepartmentStaffTreeDto;

public class DepartmentConverter {

    public static Department toDepartment(CreateDepartmentRequest request) {
        Department department = new Department();
        department.setName(request.getName());
        department.setParentId(request.getParentId());
        return department;
    }

    public static DepartmentDto toDepartmentDto(Department department) {
        DepartmentDto departmentDto = new DepartmentDto();
        departmentDto.setId(department.getId());
        departmentDto.setName(department.getName());
        departmentDto.setParentId(department.getParentId());
        return departmentDto;
    }

    public static DepartmentStaffTreeDto toDepartmentStaffTreeDto(Department department) {
        DepartmentStaffTreeDto departmentStaffTreeDto = new DepartmentStaffTreeDto();
        departmentStaffTreeDto.setType(0);
        departmentStaffTreeDto.setChildren(Lists.newArrayList());
        departmentStaffTreeDto.setId(department.getId());
        departmentStaffTreeDto.setName(department.getName());
        departmentStaffTreeDto.setParentId(department.getParentId());
        return departmentStaffTreeDto;
    }

    public static DepartmentStaffTreeDto toDepartmentStaffTreeDto(Staff staff) {
        DepartmentStaffTreeDto departmentStaffTreeDto = new DepartmentStaffTreeDto();
        departmentStaffTreeDto.setType(1);
        departmentStaffTreeDto.setId(staff.getId());
        departmentStaffTreeDto.setName(staff.getName());
        return departmentStaffTreeDto;
    }
}
