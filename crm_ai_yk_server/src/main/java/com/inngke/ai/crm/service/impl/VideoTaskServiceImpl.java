package com.inngke.ai.crm.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.inngke.ai.crm.converter.CategoryConverter;
import com.inngke.ai.crm.converter.VideoBgmMaterialConverter;
import com.inngke.ai.crm.db.crm.entity.*;
import com.inngke.ai.crm.db.crm.manager.*;
import com.inngke.ai.crm.dto.enums.VideoDraftTypeEnum;
import com.inngke.ai.crm.dto.form.SelectOption;
import com.inngke.ai.crm.dto.request.video.SaveVideoTaskInfoRequest;
import com.inngke.ai.crm.dto.request.video.VideoCreateWithMaterialRequest;
import com.inngke.ai.crm.dto.response.common.SubtitlesStyleDto;
import com.inngke.ai.crm.dto.response.video.OeDiagnosisDto;
import com.inngke.ai.crm.dto.response.video.VideoTaskInfoDto;
import com.inngke.ai.crm.service.StaffService;
import com.inngke.ai.crm.service.VideoTaskService;
import com.inngke.ai.dto.enums.VideoTypeEnum;
import com.inngke.ai.dto.request.VideoDigitalHumanConfig;
import com.inngke.ai.dto.utils.FormDataUtils;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.JsonService;
import com.inngke.common.utils.DateTimeUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class VideoTaskServiceImpl implements VideoTaskService {

    @Autowired
    private AiGenerateTaskManager aiGenerateTaskManager;
    @Autowired
    private JianyingResourceManager jianyingResourceManager;
    @Autowired
    private CategoryManager categoryManager;
    @Autowired
    private AiGenerateTaskIoManager aiGenerateTaskIoManager;
    @Autowired
    private AiGenerateVideoOutputManager aiGenerateVideoOutputManager;
    @Autowired
    private VideoCreateTaskManager videoCreateTaskManager;
    @Autowired
    private StaffService staffService;
    @Autowired
    private JsonService jsonService;

    @Autowired
    private VideoOceanengineDiagnosisManager videoOceanengineDiagnosisManager;
    @Autowired
    private VideoBgmMaterialManager videoBgmMaterialManager;

    @Override
    public VideoTaskInfoDto getInfo(JwtPayload jwtPayload, Long id) {
        Staff staff = staffService.getStaffByUserIdNotExistThrow(jwtPayload);

        AiGenerateTask aiGenerateTask = aiGenerateTaskManager.getById(id);
        if (Objects.isNull(aiGenerateTask)) {
            throw new InngkeServiceException("视频任务不存在");
        }

        AiGenerateTaskIo taskIo = aiGenerateTaskIoManager.getById(id);

        AiGenerateVideoOutput taskVideo = aiGenerateVideoOutputManager.getByTaskIds(Lists.newArrayList(id)).stream()
                .collect(Collectors.toMap(AiGenerateVideoOutput::getTaskId, Function.identity())).get(id);

        VideoCreateTask videoCreateTask = videoCreateTaskManager.getOne(Wrappers.<VideoCreateTask>query().eq(VideoCreateTask.TASK_ID, id)
                .orderByDesc(VideoCreateTask.ID).last(InngkeAppConst.STR_LIMIT_1));

        VideoOceanengineDiagnosis diagnosis = videoOceanengineDiagnosisManager.getOne(
                Wrappers.<VideoOceanengineDiagnosis>query()
                        .eq(VideoOceanengineDiagnosis.VIDEO_ID, id)
        );

        VideoTaskInfoDto videoTaskInfoDto = new VideoTaskInfoDto();
        //视频标签(话题),封面，创建时间
        videoTaskInfoDto.setId(aiGenerateTask.getId());
        videoTaskInfoDto.setTags(aiGenerateTask.getTags());
        videoTaskInfoDto.setCoverImage(aiGenerateTask.getCoverImage());
        videoTaskInfoDto.setCreateTime(DateTimeUtils.getMilli(aiGenerateTask.getCreateTime()));

        if (diagnosis != null) {
            videoTaskInfoDto.setOeDiagnosis(
                    new OeDiagnosisDto()
                            .setIsAdHighQualityMaterial(OeDiagnosisDto.getValue(diagnosis.getIsAdHighQualityMaterial()))
                            .setIsFirstPublishMaterial(OeDiagnosisDto.getValue(diagnosis.getIsFirstPublishMaterial()))
                            .setIsEcpHighQualityMaterial(OeDiagnosisDto.getValue(diagnosis.getIsEcpHighQualityMaterial()))
                            .setIsInefficientMaterial(OeDiagnosisDto.getValue(diagnosis.getIsInefficientMaterial()))
                            .setNotEcpHighQualityReason(diagnosis.getNotEcpHighQualityReason())
                            .setNotAdHighQualityReason(diagnosis.getNotAdHighQualityReason())
                            .setAudioStatus(diagnosis.getAudioStatus())
                            .setAudioRejectReason(diagnosis.getAudioRejectReason())
            );
        } else {
            videoTaskInfoDto.setOeDiagnosis(new OeDiagnosisDto());
        }

        //视频地址，标题，简介,推荐封面
        Optional.ofNullable(taskVideo).ifPresent(ignore -> {
            videoTaskInfoDto.setTitle(taskVideo.getVideoTitle());
            videoTaskInfoDto.setDesc(taskVideo.getVideoContent());
            videoTaskInfoDto.setVideoUrl(taskVideo.getVideoUrl());
            videoTaskInfoDto.setVideo1080Url(taskVideo.getVideo1080Url());
            videoTaskInfoDto.setWidth(1080);
            videoTaskInfoDto.setHeight(1920);
            videoTaskInfoDto.setRotate(0);

            if (StringUtils.isNotBlank(taskVideo.getKeyFrame())) {
                List<String> coverImageList = Splitter.on(InngkeAppConst.COMMA_STR).splitToList(taskVideo.getKeyFrame()).stream().map(keyFrameTime ->
                        taskVideo.getVideoUrl() + "?x-oss-process=video/snapshot,t_" + keyFrameTime + ",f_jpg"
                ).collect(Collectors.toList());

                videoTaskInfoDto.setCoverImageList(coverImageList);
            }
        });
        if (StringUtils.isBlank(videoTaskInfoDto.getTitle())) {
            videoTaskInfoDto.setTitle(aiGenerateTask.getTitle());
        }

        //剪映工程文件
        Optional.ofNullable(videoCreateTask).map(VideoCreateTask::getProjectZipUrl).ifPresent(videoTaskInfoDto::setProjectZipUrl);


        //视频字幕，素材分类
        VideoCreateWithMaterialRequest videoMaterialCreateRequest = jsonService.toObject(taskIo.getInputs(), VideoCreateWithMaterialRequest.class);

        Optional.ofNullable(videoMaterialCreateRequest).ifPresent(createRequest -> {
            Map<String, Object> promptMap = createRequest.getPromptMap();
            videoTaskInfoDto.setPromptMap(promptMap);

            //fixme 删除
            if (Objects.nonNull(promptMap)) {
                //视频字幕
                Optional.ofNullable(promptMap.get(FormDataUtils.FORM_KEY_ASIDES)).map(Object::toString).ifPresent(videoTaskInfoDto::setAside);

                //素材分类
                Optional.ofNullable(promptMap.get(FormDataUtils.FORM_KEY_CATEGORY_IDS)).ifPresent(categoryIdsObj -> {
                    String json = jsonService.toJson((Serializable) categoryIdsObj);
                    if (StringUtils.isBlank(json)) {
                        return;
                    }

                    videoTaskInfoDto.setVideoMaterialCategoryList(Lists.newArrayList());
                    Optional.ofNullable(jsonService.toObjectList(json, SelectOption.class)).ifPresent(selectOptionList -> {
                        videoTaskInfoDto.setVideoMaterialCategoryList(selectOptionList.stream().map(SelectOption::getTitle).collect(Collectors.toList()));
                    });
                });

                //创作类型
                int videoType = FormDataUtils.getInt(promptMap, FormDataUtils.FORM_KEY_VIDEO_TYPE, 0);
                videoTaskInfoDto.setDraftType(videoType == VideoTypeEnum.MUSIC_BEAT_VIDEO.getVideoType() ?
                        VideoDraftTypeEnum.MUSIC_BEAT.getCode() : VideoDraftTypeEnum.STORYBOARD.getCode());
                long bgmId = FormDataUtils.getLong(promptMap, FormDataUtils.FORM_KEY_CHOOSE_MUSIC, 0);
                Optional.ofNullable(videoBgmMaterialManager.getById(bgmId)).map(VideoBgmMaterialConverter::toVideoBgmMaterialDto)
                        .ifPresent(videoTaskInfoDto::setBgmMaterial);
            }

            List<VideoDigitalHumanConfig> digitalHumanConfigs = createRequest.getDigitalHumanConfigs();
            if (!CollectionUtils.isEmpty(digitalHumanConfigs)) {
                videoTaskInfoDto.setDigitalHumanConfigs(digitalHumanConfigs);
            }
        });

        int subtitleTextStyleId = FormDataUtils.getInt(videoTaskInfoDto.getPromptMap(), FormDataUtils.FORM_KEY_SUBTITLE_TEXT_STYLE, 0);
        Optional.ofNullable(jianyingResourceManager.getById(subtitleTextStyleId)).ifPresent(jianyingResource -> {
            SubtitlesStyleDto subtitlesStyleDto = new SubtitlesStyleDto();
            subtitlesStyleDto.setId(jianyingResource.getId());
            subtitlesStyleDto.setIcon(jianyingResource.getDemoUrl());
            subtitlesStyleDto.setName(jianyingResource.getName());
            videoTaskInfoDto.setSubtitlesStyle(subtitlesStyleDto);
        });

        int subtitleTagStyleCode = FormDataUtils.getInt(videoTaskInfoDto.getPromptMap(), FormDataUtils.FORM_KEY_SUBTITLE_TAG_STYLE, 0);
        Optional.ofNullable(
                categoryManager.getOne(Wrappers.<Category>query()
                        .eq(Category.TYPE, FormConfigServiceImpl.CATEGORY_TYPE_SUBTITLE_STYLE)
                        .eq(Category.DELETED, false)
                        .eq(Category.CODE, subtitleTagStyleCode))
        ).map(CategoryConverter::toCategorySelectOption).ifPresent(videoTaskInfoDto::setSubtitleTagStyle);

        //创作人
        Optional.ofNullable(aiGenerateTask.getStaffId()).map(staffService::getStaffById)
                .ifPresentOrElse(creatorStaff -> videoTaskInfoDto.setCreatorName(creatorStaff.getName()),
                        () -> videoTaskInfoDto.setCreatorName(InngkeAppConst.MIDDLE_LINE_STR)
                );

        return videoTaskInfoDto;
    }

    @Override
    public VideoTaskInfoDto saveTaskInfo(JwtPayload jwtPayload, Long id, SaveVideoTaskInfoRequest request) {
        aiGenerateTaskManager.saveVideoTaskInfo(id, request);

        return getInfo(jwtPayload, id);
    }
}
