package com.inngke.ai.crm.service.devops.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.ai.crm.db.crm.entity.Organize;
import com.inngke.ai.crm.db.crm.entity.VideoWord;
import com.inngke.ai.crm.db.crm.manager.OrganizeManager;
import com.inngke.ai.crm.db.crm.manager.VideoWordManager;
import com.inngke.ai.crm.dto.request.devops.*;
import com.inngke.ai.crm.dto.response.devops.VideoWordListDto;
import com.inngke.ai.crm.service.devops.DevOpsVideoWordService;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * DevOps视频词条管理服务实现
 */
@Service
public class DevOpsVideoWordServiceImpl implements DevOpsVideoWordService {

    @Autowired
    private VideoWordManager videoWordManager;

    @Autowired
    private OrganizeManager organizeManager;

    @Override
    public BaseResponse<BasePaginationResponse<VideoWordListDto>> getVideoWordList(GetVideoWordListRequest request) {
        // 构建查询条件
        QueryWrapper<VideoWord> wrapper = new QueryWrapper<>();
        
        // 关键字搜索
        if (StringUtils.isNotBlank(request.getKeyword())) {
            wrapper.and(w -> w
                    .like(VideoWord.WORD, request.getKeyword())
                    .or()
                    .like(VideoWord.TTS_REPLACE_WORD, request.getKeyword())
            );
        }
        
        // 类型筛选
        if (request.getType() != null) {
            wrapper.eq(VideoWord.TYPE, request.getType());
        }
        
        // 企业ID筛选
        if (request.getOrganizeId() != null) {
            wrapper.eq(VideoWord.ORGANIZE_ID, request.getOrganizeId());
        }
        
        // 排序
        wrapper.orderByDesc(VideoWord.ID);
        
        // 查询总数
        int count = videoWordManager.count(wrapper);
        
        // 分页查询
        int offset = (request.getPageNo() - 1) * request.getPageSize();
        wrapper.last("limit " + offset + "," + request.getPageSize());
        
        List<VideoWord> records = videoWordManager.list(wrapper);
        
        // 转换为DTO
        List<VideoWordListDto> dtoList = records.stream()
                .map(this::convertToListDto)
                .collect(Collectors.toList());
        
        // 批量查询企业信息
        enrichOrganizeInfo(dtoList);
        
        BasePaginationResponse<VideoWordListDto> paginationResponse = new BasePaginationResponse<>();
        paginationResponse.setList(dtoList);
        paginationResponse.setTotal(count);

        return BaseResponse.success(paginationResponse);
    }

    @Override
    public BaseResponse<Boolean> addVideoWord(AddVideoWordRequest request) {
        // 检查是否存在相同的词条
        int count = videoWordManager.count(
                Wrappers.<VideoWord>query()
                        .eq(VideoWord.WORD, request.getWord())
                        .eq(VideoWord.ORGANIZE_ID, request.getOrganizeId())
                        .eq(VideoWord.TYPE, request.getType())
        );
        
        if (count > 0) {
            throw new InngkeServiceException("该企业下已存在相同的词条");
        }
        
        // 创建实体
        VideoWord videoWord = new VideoWord();
        BeanUtils.copyProperties(request, videoWord);
        videoWord.setCreateTime(LocalDateTime.now());
        videoWord.setUpdateTime(LocalDateTime.now());
        
        // 保存
        boolean success = videoWordManager.save(videoWord);
        
        return BaseResponse.success(success);
    }

    @Override
    public BaseResponse<Boolean> updateVideoWord(UpdateVideoWordRequest request) {
        // 检查记录是否存在
        VideoWord existingWord = videoWordManager.getById(request.getId());
        if (existingWord == null) {
            throw new InngkeServiceException("词条不存在");
        }
        
        // 检查是否存在相同的词条（排除当前记录）
        int count = videoWordManager.count(
                Wrappers.<VideoWord>query()
                        .eq(VideoWord.WORD, request.getWord())
                        .eq(VideoWord.ORGANIZE_ID, request.getOrganizeId())
                        .eq(VideoWord.TYPE, request.getType())
                        .ne(VideoWord.ID, request.getId())
        );
        
        if (count > 0) {
            throw new InngkeServiceException("该企业下已存在相同的词条");
        }
        
        // 更新实体
        VideoWord videoWord = new VideoWord();
        BeanUtils.copyProperties(request, videoWord);
        videoWord.setUpdateTime(LocalDateTime.now());
        
        // 保存
        boolean success = videoWordManager.updateById(videoWord);
        
        return BaseResponse.success(success);
    }

    @Override
    public BaseResponse<Boolean> deleteVideoWord(Integer id) {
        // 检查记录是否存在
        VideoWord existingWord = videoWordManager.getById(id);
        if (existingWord == null) {
            throw new InngkeServiceException("词条不存在");
        }
        
        // 删除
        boolean success = videoWordManager.removeById(id);
        
        return BaseResponse.success(success);
    }

    @Override
    public BaseResponse<Boolean> batchDeleteVideoWord(BatchDeleteVideoWordRequest request) {
        // 批量删除
        boolean success = videoWordManager.removeByIds(request.getIds());
        
        return BaseResponse.success(success);
    }

    /**
     * 转换为列表DTO
     */
    private VideoWordListDto convertToListDto(VideoWord videoWord) {
        VideoWordListDto dto = new VideoWordListDto();
        BeanUtils.copyProperties(videoWord, dto);
        
        // 设置类型名称
        if (videoWord.getType() != null) {
            switch (videoWord.getType()) {
                case 1:
                    dto.setTypeName("TTS多音替换");
                    break;
                case 2:
                    dto.setTypeName("行业词（字幕不拆分）");
                    break;
                default:
                    dto.setTypeName("未知");
            }
        }
        
        return dto;
    }

    /**
     * 批量查询企业信息并填充
     */
    private void enrichOrganizeInfo(List<VideoWordListDto> dtoList) {
        if (dtoList.isEmpty()) {
            return;
        }
        
        // 收集所有企业ID（排除0）
        List<Long> organizeIds = dtoList.stream()
                .map(VideoWordListDto::getOrganizeId)
                .filter(id -> id != null && id != 0)
                .distinct()
                .collect(Collectors.toList());
        
        if (organizeIds.isEmpty()) {
            // 只有通用词条，设置企业名称为"通用"
            dtoList.forEach(dto -> {
                if (dto.getOrganizeId() != null && dto.getOrganizeId() == 0) {
                    dto.setOrganizeName("通用");
                }
            });
            return;
        }
        
        // 批量查询企业信息
        Collection<Organize> organizes = organizeManager.listByIds(organizeIds);
        Map<Long, String> organizeMap = organizes.stream()
                .collect(Collectors.toMap(Organize::getId, Organize::getName));
        
        // 填充企业名称
        dtoList.forEach(dto -> {
            if (dto.getOrganizeId() != null) {
                if (dto.getOrganizeId() == 0) {
                    dto.setOrganizeName("通用");
                } else {
                    dto.setOrganizeName(organizeMap.get(dto.getOrganizeId()));
                }
            }
        });
    }
}
