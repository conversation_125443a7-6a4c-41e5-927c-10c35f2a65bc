package com.inngke.ai.crm.api.xhs.dto;

import com.inngke.ai.crm.api.browser.dto.BrowserTaskRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-12-25 09:54
 **/
public class XiaoHongShuPublishRequest extends BrowserTaskRequest {

    private Long ykId;

    private String title;

    private String content;

    private List<String> tags;

    private List<String> images;

    private List<XiaoHongShuCookiesDto> cookies;

    /**
     * 手机号
     */
    private String mobile;

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Long getYkId() {
        return ykId;
    }

    public void setYkId(Long ykId) {
        this.ykId = ykId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }


    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public List<String> getImages() {
        return images;
    }

    public void setImages(List<String> images) {
        this.images = images;
    }

    public List<XiaoHongShuCookiesDto> getCookies() {
        return cookies;
    }

    public void setCookies(List<XiaoHongShuCookiesDto> cookies) {
        this.cookies = cookies;
    }
}
