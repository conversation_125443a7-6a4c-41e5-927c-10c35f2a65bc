package com.inngke.ai.crm.db.crm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.inngke.ai.crm.dto.enums.CoinProductPlatformEnum;
import com.inngke.ai.crm.dto.enums.CoinProductType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2023-09-17 20:22
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class CoinProduct implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 雪花ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 是否生效0=未生效，1=生效
     */
    private Integer enable;



    private Integer sort;

    /**
     * 1=首单专享
     * @see CoinProductType
     */
    private Integer type;

    /**
     * 标题
     */
    private String title;


    /**
     * 商品描述
     */
    private String description;

    /**
     * 虚拟币/周期
     */
    private Integer coin;

    /**
     * 周期类型0=一次性,1=周期,3=季卡，12=年卡
     *
     */
    private Integer periodType;

    /**
     * 价格，单位分
     */
    private Integer amount;

    /**
     * 原始价格，单位分
     */
    private Integer orgAmount;

    /**
     * VIP类型 0=非VIP 1=VIP 2=SVIP
     */
    private Integer vipType;

    /**
     * 平台类型
     *
     * @see CoinProductPlatformEnum
     */
    private String platform;


    /**
     * 企业专享，0表示公共
     */
    private Long organizeId;

    /**
     * 领取积分后是否强制成功企业员工，默认：否
     */
    private Integer beOrganizeStaff;


    /**
     * 创建时间
     */
    private LocalDateTime createTime;


    public static final String ID = "id";

    public static final String ENABLE = "enable";

    public static final String TYPE = "type";

    public static final String TITLE = "title";

    public static final String DESCRIPTION = "description";

    public static final String COIN = "coin";

    public static final String PERIOD_TYPE = "period_type";

    public static final String AMOUNT = "amount";

    public static final String ORG_AMOUNT = "org_amount";

    public static final String VIP_TYPE = "vip_type";

    public static final String PLATFORM = "platform";

    public static final String ORGANIZE_ID = "organize_id";

    public static final String BE_ORGANIZE_STAFF = "be_organize_staff";

    public static final String CREATE_TIME = "create_time";

}
