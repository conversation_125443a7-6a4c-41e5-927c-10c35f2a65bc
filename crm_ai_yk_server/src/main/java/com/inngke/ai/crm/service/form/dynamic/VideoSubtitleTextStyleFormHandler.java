package com.inngke.ai.crm.service.form.dynamic;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.inngke.ai.crm.db.crm.entity.DifyAppConf;
import com.inngke.ai.crm.db.crm.entity.JianyingResource;
import com.inngke.ai.crm.db.crm.manager.JianyingResourceManager;
import com.inngke.ai.crm.dto.form.SelectFormConfig;
import com.inngke.ai.crm.dto.form.SelectOption;
import com.inngke.ai.crm.dto.response.pro.ProAiArticleTemplateDto;
import com.inngke.ai.crm.service.impl.FormConfigServiceImpl;
import com.inngke.ai.dto.utils.FormDataUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class VideoSubtitleTextStyleFormHandler extends BaseSelectOptionHandler {
    @Autowired
    private JianyingResourceManager jianyingResourceManager;

    /**
     * 需要处理的表单key
     */
    @Override
    public String getFormKey() {
        return FormDataUtils.FORM_KEY_SUBTITLE_TEXT_STYLE;
    }

    @Override
    protected List<SelectOption> getSelectOptions(long organizeId, DifyAppConf difyAppConf, ProAiArticleTemplateDto formConfigs, SelectFormConfig currentFormConfig, String preFormValue) {
        if (!CollectionUtils.isEmpty(currentFormConfig.getSelectOptions())) {
            //如果已指定，则返回空，则不会替换当前值
            return Lists.newArrayList();
        }
        return jianyingResourceManager.list(
                        Wrappers.<JianyingResource>query()
                                .eq(JianyingResource.STATUS, 1)
                                .eq(JianyingResource.MATERIAL_TYPE, FormConfigServiceImpl.TEXT_MATERIAL_TYPE)
                                .select(JianyingResource.ID, JianyingResource.DEMO_URL, JianyingResource.NAME)
                ).stream().map(resource -> new SelectOption()
                        .setValue(String.valueOf(resource.getId()))
                        .setIcon(resource.getDemoUrl())
                        .setTitle(resource.getName())
                )
                .collect(Collectors.toList());
    }
}
