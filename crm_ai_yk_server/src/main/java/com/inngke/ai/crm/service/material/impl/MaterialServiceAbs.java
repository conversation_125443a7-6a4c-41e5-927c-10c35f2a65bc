package com.inngke.ai.crm.service.material.impl;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.inngke.ai.crm.converter.MaterialConverter;
import com.inngke.ai.crm.db.crm.entity.Material;
import com.inngke.ai.crm.db.crm.entity.MaterialCategory;
import com.inngke.ai.crm.db.crm.entity.VideoMaterial;
import com.inngke.ai.crm.db.crm.manager.MaterialCategoryManager;
import com.inngke.ai.crm.db.crm.manager.MaterialManager;
import com.inngke.ai.crm.db.crm.manager.UserManager;
import com.inngke.ai.crm.dto.request.material.*;
import com.inngke.ai.crm.dto.response.material.MaterialCategoryDto;
import com.inngke.ai.crm.dto.response.material.MaterialDto;
import com.inngke.ai.crm.service.VideoMaterialService;
import com.inngke.ai.crm.service.material.MaterialService;
import com.inngke.ai.crm.service.material.VideoMaterialProcessLogService;
import com.inngke.ai.crm.service.material.VideoMaterialTaskService;
import com.inngke.ai.dto.enums.MaterialTypeEnum;
import com.inngke.common.core.utils.SnowflakeHelper;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.utils.JsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public abstract class MaterialServiceAbs implements MaterialService {

    public static final String VIDEO_TYPE = "video";
    @Autowired
    private MaterialCategoryManager materialCategoryManager;
    @Autowired
    private UserManager userManager;
    @Autowired
    protected RedisTemplate redisTemplate;
    @Autowired
    protected VideoMaterialTaskService videoMaterialRotateService;
    @Autowired
    private VideoMaterialProcessLogService videoMaterialProcessLogService;

    @Autowired
    private VideoMaterialService videoMaterialService;

    @Override
    public BasePaginationResponse<MaterialDto> pagingMaterials(JwtPayload jwtPayload, PagingMaterialRequest request) {
        BasePaginationResponse<MaterialDto> response = new BasePaginationResponse<>();
        response.setList(Lists.newArrayList());
        response.setTotal(0);
        Long userOrganizeId = userManager.getUserOrganizeId(jwtPayload.getCid());
        if (Objects.isNull(userOrganizeId)) {
            return response;
        }

        request.setOrganizeId(userOrganizeId);

        List<Long> sonCategoryIds = materialCategoryManager.getByParentIds(request.getCategoryIds()).stream().map(MaterialCategory::getId).collect(Collectors.toList());
        request.setSonCategoryIds(sonCategoryIds);

        String categoryDirQuery = buildCategoryDirQuery(request, sonCategoryIds);

        List<? extends Material> videoMaterials = getManager().pagingMaterials(request, categoryDirQuery);

        Map<Long, MaterialCategoryDto> materialCategoryMap = getCategoryMapByMaterialList(videoMaterials);

        Set<Long> processingMaterialIds = videoMaterialProcessLogService.getProcessingIds();

        List<MaterialDto> materialList = videoMaterials.stream().map(videoMaterial -> toMaterialDto(videoMaterial, materialCategoryMap, processingMaterialIds)).collect(Collectors.toList());
        response.setList(materialList);
        Integer total = getManager().pagingCountMaterials(request, categoryDirQuery);
        response.setTotal(total);

        return response;
    }

    private String buildCategoryDirQuery() {
        return null;
    }

    @Override
    public List<MaterialDto> listMaterials(JwtPayload jwtPayload, ListMaterialRequest request) {
        List<Long> sonCategoryIds = materialCategoryManager.getByParentIds(request.getCategoryIds()).stream().map(MaterialCategory::getId).collect(Collectors.toList());
        List<? extends Material> videoMaterials = getManager().pagingMaterials(
                userManager.getUserOrganizeId(jwtPayload.getCid()), request.getLastId(), request.getCategoryIds(), sonCategoryIds, 1, request.getPageSize()
        );

        Map<Long, MaterialCategoryDto> materialCategoryMap = getCategoryMapByMaterialList(videoMaterials);

        Set<Long> processingMaterialIds = videoMaterialProcessLogService.getProcessingIds();
        //取出还没有完成处理的旋转角度
        return videoMaterials.stream().map(videoMaterial -> {
            MaterialDto materialDto = toMaterialDto(videoMaterial, materialCategoryMap, processingMaterialIds);
            if (videoMaterial instanceof VideoMaterial) {
                VideoMaterial videoMaterialObj = (VideoMaterial) videoMaterial;
                materialDto.setDuration(videoMaterialObj.getVideoDuration());
                materialDto.setWidth(videoMaterialObj.getWidth());
                materialDto.setHeight(videoMaterialObj.getHeight());
                materialDto.setRotate(videoMaterialObj.getRotate());
                materialDto.setMaterialType(MaterialTypeEnum.CROP_MATERIAL.getType());
            }

            return materialDto;
        }).collect(Collectors.toList());
    }

    @Override
    public Integer countMaterials(JwtPayload jwtPayload, ListMaterialRequest request) {
        return getManager().pagingCountMaterials(userManager.getUserOrganizeId(jwtPayload.getCid()), request.getCategoryIds());
    }

    @Override
    public Boolean rotate(JwtPayload jwtPayload, RotateMaterialRequest request) {
        return true;
    }

    public Boolean rotate2(JwtPayload jwtPayload, RotateMaterialRequest request) {
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addMaterials(JwtPayload jwtPayload, AddMaterialRequest request) {
        return !CollectionUtils.isEmpty(addMaterialsReturnIds(jwtPayload, request));
    }

    protected List<Long> addMaterialsReturnIds(JwtPayload jwtPayload, AddMaterialRequest request) {
        List<Material> materialList = request.getUrls().stream().map(url -> {
            Material material = MaterialConverter.toMaterial(url, request);
            material.setOrganizeId(userManager.getUserOrganizeId(jwtPayload.getCid()));
            material.setId(SnowflakeHelper.getId());
            return material;
        }).collect(Collectors.toList());
        List<? extends Material> newMaterialList = saveExistMaterial(materialList, request.getCategoryIds());
        boolean added = getManager().addMaterials(newMaterialList);
        if (added) {
            return newMaterialList.stream().map(Material::getId).collect(Collectors.toList());
        }

        return Lists.newArrayList();
    }

    protected List<? extends Material> saveExistMaterial(List<Material> materialList, List<Long> categoryIds) {
        return getManager().saveExistMaterial(materialList, categoryIds);
    }

    @Override
    public boolean deleteMaterials(JwtPayload jwtPayload, DeleteMaterialRequest request) {
        return getManager().deleteImageMaterials(userManager.getUserOrganizeId(jwtPayload.getCid()), request.getIds());
    }

    @Override
    public boolean batchSetMaterialCategory(JwtPayload jwtPayload, BatchSetMaterialCategoryRequest request) {
        return getManager().batchSetMaterialCategory(userManager.getUserOrganizeId(jwtPayload.getCid()), request.getMaterialIds(), request.getCategoryIds());
    }

    protected Map<Long, MaterialCategoryDto> getCategoryMapByMaterialList(List<? extends Material> materials) {
        List<String> categoryIds = materials.stream().map(Material::getCategoryIds).filter(StringUtils::isNotBlank).map(
                categoryIdStr -> JsonUtil.jsonToList(categoryIdStr, String.class)
        ).flatMap(Collection::stream).collect(Collectors.toList());

        return materialCategoryManager.getByIds(categoryIds).stream().map(MaterialConverter::toMaterialCategoryDto)
                .collect(Collectors.toMap(MaterialCategoryDto::getId, Function.identity()));
    }

    protected MaterialDto toMaterialDto(Material material, Map<Long, MaterialCategoryDto> materialCategoryMap, Set<Long> processingMaterialIds) {
        return MaterialConverter.toMaterialDto(material, materialCategoryMap);
    }

    private String buildCategoryDirQuery(PagingMaterialRequest request, List<Long> sonCategoryIds) {
        List<Long> categoryIds = request.getCategoryIds();
        Long dirId = request.getDirId();
        String subQuery = "";
        if (!CollectionUtils.isEmpty(categoryIds) && CollectionUtils.isEmpty(sonCategoryIds)) {
            subQuery = " and category_ids != '' and json_contains(category_ids, '" + JsonUtil.toJsonString(categoryIds) + "') ";
        }

        if (!CollectionUtils.isEmpty(sonCategoryIds)) {
            subQuery = " and category_ids != '' and (" + Joiner.on("or").join(
                    sonCategoryIds.stream().map(
                            sonCategoryId -> " json_contains(category_ids,'" + sonCategoryId + "') "
                    ).collect(Collectors.toList())
            ) + ")";
        }

        if (Objects.nonNull(dirId) && VIDEO_TYPE.equals(request.getType())) {
            subQuery += " and dir_ids != '' and json_contains(dir_ids,'" + dirId + "')";
        }

        if (subQuery.startsWith(" and")) {
            subQuery = subQuery.substring(5);
        }
        return subQuery;
    }

    abstract protected MaterialManager<? extends Material> getManager();

}
