package com.inngke.ai.crm.converter;

import com.google.common.collect.Lists;
import com.inngke.ai.crm.db.crm.entity.DigitalPersonTag;
import com.inngke.ai.crm.db.crm.entity.DigitalPersonTemplate;
import com.inngke.ai.crm.dto.digital.person.DigitalPersonConfigDto;
import com.inngke.ai.crm.dto.request.digital.person.AddDpTemplateRequest;
import com.inngke.ai.crm.dto.response.DpTagDto;
import com.inngke.ai.crm.dto.response.DpTemplateDto;
import com.inngke.ai.crm.dto.response.digital.person.DigitalPersonTagDto;
import com.inngke.ai.crm.dto.response.digital.person.DigitalPersonTemplateDto;
import com.inngke.ai.crm.dto.response.video.TtsConfigDto;
import com.inngke.ai.dto.response.JianYingFloatConfigDto;
import com.inngke.common.core.utils.SnowflakeHelper;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.JsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

public class DigitalPersonConverter {

    public static DigitalPersonTemplate toDigitalPersonTemplate(AddDpTemplateRequest request) {
        DigitalPersonTemplate digitalPersonTemplate = new DigitalPersonTemplate();
        digitalPersonTemplate.setId(SnowflakeHelper.getId());
        digitalPersonTemplate.setOrganizeId(request.getOrganizeId());
        digitalPersonTemplate.setType(request.getType());
        digitalPersonTemplate.setName(request.getName());
        digitalPersonTemplate.setSourceName(request.getSourceName());
        digitalPersonTemplate.setGif(request.getGif());
        digitalPersonTemplate.setFullScreenPreview(request.getFullScreenPreview());
        digitalPersonTemplate.setFloatScreenPreview(request.getFloatScreenPreview());
        digitalPersonTemplate.setBackground(request.getBackground());
        digitalPersonTemplate.setPreviewVideoUrl(request.getPreviewVideoUrl());
        digitalPersonTemplate.setTtsId(request.getTtsId());
        digitalPersonTemplate.setGender(request.getGender());
        if (!CollectionUtils.isEmpty(request.getTags())) {
            digitalPersonTemplate.setTags(JsonUtil.toJsonString(request.getTags()));
        }
        digitalPersonTemplate.setDpId(request.getDpId());
        digitalPersonTemplate.setSort(request.getSort());
        Optional.ofNullable(request.getDpConfig()).map(JsonUtil::toJsonString)
                .ifPresent(digitalPersonTemplate::setDpConfig);
        Optional.ofNullable(request.getJianYingConfig()).map(JsonUtil::toJsonString)
                .ifPresent(digitalPersonTemplate::setJianYingConfig);
        digitalPersonTemplate.setCreateTime(LocalDateTime.now());
        return digitalPersonTemplate;
    }

    public static DpTemplateDto toDpTemplateDto(DigitalPersonTemplate digitalPersonTemplate) {
        DpTemplateDto dpTemplateDto = new DpTemplateDto();
        dpTemplateDto.setId(digitalPersonTemplate.getId());
        dpTemplateDto.setOrganizeId(digitalPersonTemplate.getOrganizeId());
        dpTemplateDto.setName(digitalPersonTemplate.getName());
        dpTemplateDto.setSourceName(digitalPersonTemplate.getSourceName());
        dpTemplateDto.setGif(digitalPersonTemplate.getGif());
        dpTemplateDto.setFullScreenPreview(digitalPersonTemplate.getFullScreenPreview());
        dpTemplateDto.setFloatScreenPreview(digitalPersonTemplate.getFloatScreenPreview());
        dpTemplateDto.setBackground(digitalPersonTemplate.getBackground());
        dpTemplateDto.setTags(JsonUtil.jsonToList(digitalPersonTemplate.getTags(),Long.class));
        dpTemplateDto.setDpId(digitalPersonTemplate.getDpId());
        dpTemplateDto.setSort(digitalPersonTemplate.getSort());
        dpTemplateDto.setPreviewVideoUrl(digitalPersonTemplate.getPreviewVideoUrl());
        dpTemplateDto.setGender(digitalPersonTemplate.getGender());
        dpTemplateDto.setTtsId(digitalPersonTemplate.getTtsId());
        dpTemplateDto.setType(digitalPersonTemplate.getType());
        if (StringUtils.isNotBlank(digitalPersonTemplate.getDpConfig())) {
            dpTemplateDto.setDpConfig(JsonUtil.jsonToObject(digitalPersonTemplate.getDpConfig(), DigitalPersonConfigDto.class));
        }
        if (StringUtils.isNotBlank(digitalPersonTemplate.getJianYingConfig())){
            dpTemplateDto.setJianYingConfig(JsonUtil.jsonToObject(digitalPersonTemplate.getJianYingConfig(), JianYingFloatConfigDto.class));
        }
        dpTemplateDto.setCreateTime(DateTimeUtils.getMilli(digitalPersonTemplate.getCreateTime()));
        return dpTemplateDto;
    }

    public static DpTagDto toDpTagDto(DigitalPersonTag digitalPersonTag) {
        DpTagDto dpTagDto = new DpTagDto();
        dpTagDto.setId(digitalPersonTag.getId());
        dpTagDto.setName(digitalPersonTag.getName());
        dpTagDto.setSort(digitalPersonTag.getSort());
        return dpTagDto;
    }

    public static DigitalPersonTemplateDto toDigitalPersonTemplateDto(
            DigitalPersonTemplate digitalPersonTemplate,
            Map<Long, Integer> useCountMap,
            Map<Integer, TtsConfigDto> videoAudioConfigMap,
            Map<Long, DigitalPersonTagDto> digitalPersonTagMap) {
        DigitalPersonTemplateDto digitalPersonTemplateDto = new DigitalPersonTemplateDto();
        digitalPersonTemplateDto.setId(digitalPersonTemplate.getId());
        digitalPersonTemplateDto.setName(digitalPersonTemplate.getName());
        digitalPersonTemplateDto.setType(digitalPersonTemplate.getType());
        digitalPersonTemplateDto.setUrl(digitalPersonTemplate.getFullScreenPreview());
        digitalPersonTemplateDto.setFloatScreenPreviewUrl(digitalPersonTemplate.getFloatScreenPreview());
        digitalPersonTemplateDto.setFullScreenPreviewUrl(digitalPersonTemplate.getFullScreenPreview());
        digitalPersonTemplateDto.setPreviewVideoUrl(digitalPersonTemplate.getPreviewVideoUrl());
        digitalPersonTemplateDto.setUsedCount(
                Optional.ofNullable(useCountMap.get(digitalPersonTemplate.getId())).orElse(0)
        );

        Optional.ofNullable(digitalPersonTemplate.getTags()).map(tagStr ->
                JsonUtil.jsonToList(tagStr, Long.class)
        ).ifPresentOrElse(tagList -> digitalPersonTemplateDto.setDigitalPersonTagList(
                tagList.stream().map(digitalPersonTagMap::get).collect(Collectors.toList())
        ), () -> digitalPersonTemplateDto.setDigitalPersonTagList(Lists.newArrayList()));

        digitalPersonTemplateDto.setGender(digitalPersonTemplate.getGender());
        digitalPersonTemplateDto.setVideoAudioConfig(Optional.ofNullable(videoAudioConfigMap.get(digitalPersonTemplate.getTtsId())).orElse(new TtsConfigDto()));
        return digitalPersonTemplateDto;
    }

    public static DigitalPersonTemplateDto toDigitalPersonTemplateDto(
            DigitalPersonTemplate digitalPersonTemplate, Integer useCount,
            TtsConfigDto videoAudioConfig,Map<Long, DigitalPersonTagDto> digitalPersonTagMap) {
        DigitalPersonTemplateDto digitalPersonTemplateDto = new DigitalPersonTemplateDto();
        digitalPersonTemplateDto.setId(digitalPersonTemplate.getId());
        digitalPersonTemplateDto.setName(digitalPersonTemplate.getName());
        digitalPersonTemplateDto.setUrl(digitalPersonTemplate.getFullScreenPreview());
        digitalPersonTemplateDto.setFullScreenPreviewUrl(digitalPersonTemplate.getFullScreenPreview());
        digitalPersonTemplateDto.setFloatScreenPreviewUrl(digitalPersonTemplate.getFloatScreenPreview());
        digitalPersonTemplateDto.setPreviewVideoUrl(digitalPersonTemplate.getPreviewVideoUrl());
        digitalPersonTemplateDto.setUsedCount(useCount);
        digitalPersonTemplateDto.setGender(digitalPersonTemplate.getGender());
        digitalPersonTemplateDto.setVideoAudioConfig(videoAudioConfig);
        digitalPersonTemplateDto.setType(digitalPersonTemplate.getType());
        digitalPersonTemplateDto.setDeleted(digitalPersonTemplate.getDeleted());
        Optional.ofNullable(digitalPersonTemplate.getTags()).map(tagStr ->
                JsonUtil.jsonToList(tagStr, Long.class)
        ).ifPresentOrElse(tagList -> digitalPersonTemplateDto.setDigitalPersonTagList(
                tagList.stream().map(digitalPersonTagMap::get).collect(Collectors.toList())
        ), () -> digitalPersonTemplateDto.setDigitalPersonTagList(Lists.newArrayList()));
        return digitalPersonTemplateDto;
    }
}
