package com.inngke.ai.crm.service;

import com.inngke.ai.crm.db.crm.entity.Staff;
import com.inngke.ai.crm.db.crm.entity.User;
import com.inngke.ai.crm.db.crm.entity.UserVip;
import com.inngke.ai.crm.db.crm.manager.CoinManager;
import com.inngke.ai.crm.db.crm.manager.UserVipManager;
import com.inngke.common.core.utils.AsyncUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class UserVipCoinRelationService {

    @Resource
    private UserVipManager userVipManager;
    @Resource
    private CoinManager coinManager;
    private static final Executor pool = Executors.newFixedThreadPool(16);

    public UserVipCoinRelation initFromStaffUserRelation(StaffUserRelationService.StaffUserRelation staffUserRelation){
        List<Long> staffIds = staffUserRelation.getStaffList().stream().map(Staff::getId).collect(Collectors.toList());
        List<Long> userIds = staffUserRelation.getUserList().stream().map(User::getId).collect(Collectors.toList());

        CompletableFuture<Map<Long, UserVip>> staffLastVipMapFuture = AsyncUtils.supplyTraceAsync(() -> userVipManager.getStaffLastMap(staffIds), pool);
        CompletableFuture<Map<Long, UserVip>> staffEarliestVipMapFuture = AsyncUtils.supplyTraceAsync(() -> userVipManager.getStaffEarliest(staffIds), pool);
        CompletableFuture<Map<Long, UserVip>> userEarliestVipMapFuture = AsyncUtils.supplyTraceAsync(() -> userVipManager.getUserEarliest(staffIds), pool);
        CompletableFuture<Map<Long, Integer>> userCoinMapFuture = AsyncUtils.supplyTraceAsync(() -> coinManager.getUserCoinMap(userIds), pool);

        //员工最后分配vip
        Map<Long, UserVip> staffLastVipMap = AsyncUtils.getFutureData(staffLastVipMapFuture);
        //员工最早分配vip
        Map<Long, UserVip> staffEarliestVipMap = AsyncUtils.getFutureData(staffEarliestVipMapFuture);
        //获取用户最早分配vip
        Map<Long, UserVip> userEarliestVipMap = AsyncUtils.getFutureData(userEarliestVipMapFuture);
        //用户积分余额
        Map<Long, Integer> userCoinMap = AsyncUtils.getFutureData(userCoinMapFuture);

        return new UserVipCoinRelation(staffUserRelation,staffLastVipMap,staffEarliestVipMap,userEarliestVipMap,userCoinMap);
    }

    public static class UserVipCoinRelation{

        private final StaffUserRelationService.StaffUserRelation staffUserRelation;
        /**
         * 员工最后分配vip
         */
        private final Map<Long, UserVip> staffLastVipMap;
        /**
         * 员工最早分配vip
         */
        private final Map<Long, UserVip> staffEarliestVipMap;
        /**
         * 获取用户最早分配vip
         */
        private final Map<Long, UserVip> userEarliestVipMap;
        /**
         * 用户积分余额
         */
        private final Map<Long, Integer> userCoinMap;

        public UserVipCoinRelation(StaffUserRelationService.StaffUserRelation staffUserRelation, Map<Long, UserVip> staffLastVipMap, Map<Long, UserVip> staffEarliestVipMap, Map<Long, UserVip> userEarliestVipMap, Map<Long, Integer> userCoinMap) {
            this.staffUserRelation = staffUserRelation;
            this.staffLastVipMap = staffLastVipMap;
            this.staffEarliestVipMap = staffEarliestVipMap;
            this.userEarliestVipMap = userEarliestVipMap;
            this.userCoinMap = userCoinMap;
        }

        public UserVip getStaffLastVip(Long staffId) {
            return staffLastVipMap.get(staffId);
        }

        public Map<Long, UserVip> getStaffLastVipMap() {
            return staffLastVipMap;
        }

        public Map<Long, UserVip> getStaffEarliestVipMap() {
            return staffEarliestVipMap;
        }

        public Map<Long, UserVip> getUserEarliestVipMap() {
            return userEarliestVipMap;
        }

        public Map<Long, Integer> getUserCoinMap() {
            return userCoinMap;
        }

        public Integer getUserCoinByStaffId(Long id) {
            return Optional.ofNullable(staffUserRelation.getStaffUserProperties(id,User::getId)).map(userCoinMap::get).orElse(0);
        }

        public UserVip getStaffEarliestVip(Long id) {
            return Optional.ofNullable(staffUserRelation.getStaffUserProperties(id,User::getId)).map(userEarliestVipMap::get).orElse(staffEarliestVipMap.get(id));
        }

        public <U> U getStaffLastVipProperties(Long staffId, Function<? super UserVip, ? extends U> mapper) {
            return Optional.ofNullable(staffLastVipMap.get(staffId)).map(mapper).orElse(null);
        }
    }
}
