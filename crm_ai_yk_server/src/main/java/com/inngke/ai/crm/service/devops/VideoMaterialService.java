package com.inngke.ai.crm.service.devops;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.inngke.ai.crm.api.video.VideoApi;
import com.inngke.ai.crm.api.video.VideoMaterialApi;
import com.inngke.ai.crm.api.video.dto.QueryVideoMaterialDto;
import com.inngke.ai.crm.api.video.dto.QueryVideoMaterialRequest;
import com.inngke.ai.crm.api.video.dto.RotateMaterialRequest;
import com.inngke.ai.crm.converter.MaterialConverter;
import com.inngke.ai.crm.db.crm.entity.Material;
import com.inngke.ai.crm.db.crm.entity.MaterialCategory;
import com.inngke.ai.crm.db.crm.entity.VideoMaterial;
import com.inngke.ai.crm.db.crm.entity.VideoMaterialDir;
import com.inngke.ai.crm.db.crm.manager.*;
import com.inngke.ai.crm.dto.IdPageDto;
import com.inngke.ai.crm.dto.request.IdPageRequest;
import com.inngke.ai.crm.dto.request.devops.*;
import com.inngke.ai.crm.dto.request.material.DeleteMaterialRequest;
import com.inngke.ai.crm.dto.request.material.PagingMaterialRequest;
import com.inngke.ai.crm.dto.response.devops.VideoInfoDto;
import com.inngke.ai.crm.dto.response.material.MaterialCategoryDto;
import com.inngke.ai.crm.dto.response.material.MaterialDirTreeDto;
import com.inngke.ai.crm.dto.response.material.MaterialDto;
import com.inngke.ai.crm.dto.response.video.VideoMaterialDto;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.SnowflakeHelper;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.JsonService;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.JsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class VideoMaterialService {


    private static final Logger logger = LoggerFactory.getLogger(VideoMaterialService.class);

    @Autowired
    private VideoMaterialManager videoMaterialManager;
    @Autowired
    private VideoApi videoApi;
    @Autowired
    private VideoMaterialApi videoMaterialApi;
    @Autowired
    @Qualifier("videoMaterialManagerImpl")
    private MaterialManager<VideoMaterial> materialManager;
    @Autowired
    private MaterialCategoryManager materialCategoryManager;
    @Autowired
    private JsonService jsonService;
    @Autowired
    private VideoMaterialDirManager videoMaterialDirManager;

    /**
     * 删除素材
     */
    public Boolean deleteMaterial(DeleteVideoMaterialRequest request) {
        boolean deleted = videoMaterialManager.update(Wrappers.<VideoMaterial>update()
                .eq(VideoMaterial.ID, request.getMaterialId())
                .set(VideoMaterial.STATUS, -1)
        );

        if (!deleted) {
            return false;
        }

        DeleteMaterialRequest deleteRequest = new DeleteMaterialRequest();
        deleteRequest.setIds(Lists.newArrayList(request.getMaterialId()));
        return BaseResponse.responseSuccess(videoMaterialApi.batchDeleteMaterials(deleteRequest));
    }

    /**
     * 旋转视频素材
     */
    public Boolean rotateVideoMaterial(RotateMaterialRequest request) {
        BaseResponse<Boolean> rotateResp = videoMaterialApi.rotate(request);
        return Optional.ofNullable(rotateResp).map(BaseResponse::getData).orElse(null);
    }

    /**
     * 更新视频素材信息
     */
    public VideoMaterialDto updateVideoMaterial(UpdateVideoMaterialRequest request) {
        VideoMaterial videoMaterial = getVideoMaterialAndThrowIfNotExist(request.getId());
        videoMaterial.setUpdateTime(null);

        Optional.ofNullable(request.getUrl()).ifPresent(videoMaterial::setUrl);
        Optional.ofNullable(request.getType()).ifPresent(videoMaterial::setType);
        Optional.ofNullable(request.getContent()).ifPresent(videoMaterial::setContent);
        Optional.ofNullable(request.getWidth()).ifPresent(videoMaterial::setWidth);
        Optional.ofNullable(request.getHeight()).ifPresent(videoMaterial::setHeight);
        Optional.ofNullable(request.getTags()).ifPresent(videoMaterial::setTags);
        Optional.ofNullable(request.getVideoDuration()).ifPresent(videoMaterial::setVideoDuration);
        Optional.ofNullable(request.getStatus()).ifPresent(videoMaterial::setStatus);
        Optional.ofNullable(request.getCutFrames()).ifPresent(videoMaterial::setCutFrames);
        if ( request.getWidth() != null && request.getHeight() != null){
            videoMaterial.setVertical(request.getWidth() < request.getHeight());
        }

        videoMaterialManager.updateById(videoMaterial);

        return toVideoMaterialDto(videoMaterial);
    }


    public VideoMaterialDto updateVideoClassify(UpdateVideoClassifyRequest request) {
        VideoMaterial videoMaterial = getVideoMaterialAndThrowIfNotExist(request.getId());

        if (StringUtils.isNotBlank(request.getClassify())) {
            videoMaterial.setClassify(request.getClassify());
            videoMaterial.setUpdateTime(null);
            videoMaterialManager.updateById(videoMaterial);
        }

        return toVideoMaterialDto(videoMaterial);
    }


    public VideoMaterialDto getVideoMaterialInfo(VideoMaterialIdRequest request) {
        return toVideoMaterialDto(getVideoMaterialAndThrowIfNotExist(request.getMaterialId()));
    }

    /**
     * 获取素材列表
     */
    public IdPageDto<MaterialDto> getVideoMaterialList(GetVideoMaterialRequest request) {
        if (StringUtils.isBlank(request.getVectorQuery())) {
            return getVideoMaterialListDb(request);
        }

        return getVideoMaterialListMilvus(request);
    }

    private IdPageDto<MaterialDto> getVideoMaterialListMilvus(GetVideoMaterialRequest request) {
        IdPageDto<MaterialDto> materialResponse = new IdPageDto<>();
        materialResponse.setTotal(0);
        materialResponse.setList(Lists.newArrayList());

        QueryVideoMaterialRequest queryVideoMaterialRequest = new QueryVideoMaterialRequest();
        if (CollectionUtils.isEmpty(request.getCategoryIds())) {
            request.setCategoryIds(materialCategoryManager.getByOrganizeIdType(
                    request.getOrganizeId(), MaterialCategory.TYPE_VIDEO
            ).stream().map(MaterialCategory::getId).collect(Collectors.toList()));
        }

        queryVideoMaterialRequest.setTags(Joiner.on(InngkeAppConst.COMMA_STR).join(request.getCategoryIds()));
        queryVideoMaterialRequest.setPage(request.getPageNo());
        queryVideoMaterialRequest.setPageSize(request.getPageSize());

        BaseResponse<List<QueryVideoMaterialDto>> response = videoMaterialApi.query(jsonService.toObject(jsonService.toJson(queryVideoMaterialRequest), Map.class));
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            return materialResponse;
        }
        List<MaterialDto> materialList = materialResponse.getList();
        List<Long> materialIds = materialList.stream().map(MaterialDto::getId).collect(Collectors.toList());

        List<VideoMaterial> videoMaterials = videoMaterialManager.list(Wrappers.<VideoMaterial>query().in(VideoMaterial.MATERIAL_GROUP_ID, materialIds));

        Map<Long, MaterialCategoryDto> materialCategoryMap = getCategoryMapByMaterialList(videoMaterials);

        materialResponse.setList(
                videoMaterials.stream().map(videoMaterial -> MaterialConverter.toMaterialDto(videoMaterial, materialCategoryMap)).collect(Collectors.toList())
        );
        materialResponse.setTotal(
                materialManager.pagingCountMaterials(request.getOrganizeId(), request.getCategoryIds())
        );

        return materialResponse;
    }

    private IdPageDto<MaterialDto> getVideoMaterialListDb(GetVideoMaterialRequest request) {
        IdPageDto<MaterialDto> listResponse = new IdPageDto<>();

        List<VideoMaterial> videoMaterials = materialManager.pagingMaterials(request.getOrganizeId(), null,
                request.getCategoryIds(), null, request.getPageNo(), request.getPageSize()
        );

        Map<Long, MaterialCategoryDto> materialCategoryMap = getCategoryMapByMaterialList(videoMaterials);

        listResponse.setList(
                videoMaterials.stream().map(videoMaterial -> MaterialConverter.toMaterialDto(videoMaterial, materialCategoryMap)).collect(Collectors.toList())
        );
        listResponse.setTotal(
                materialManager.pagingCountMaterials(request.getOrganizeId(), request.getCategoryIds())
        );

        return listResponse;
    }

    private Map<Long, MaterialCategoryDto> getCategoryMapByMaterialList(List<VideoMaterial> materials) {
        List<String> categoryIds = materials.stream().map(Material::getCategoryIds).filter(StringUtils::isNotBlank).map(
                categoryIdStr -> JsonUtil.jsonToList(categoryIdStr, String.class)
        ).flatMap(Collection::stream).collect(Collectors.toList());

        return materialCategoryManager.getByIds(categoryIds).stream().map(MaterialConverter::toMaterialCategoryDto)
                .collect(Collectors.toMap(MaterialCategoryDto::getId, Function.identity()));
    }

    /**
     * 获取视频信息
     */
    public VideoInfoDto getVideoMaterialFileInfo(VideoMaterialIdRequest request) {
        VideoMaterial videoMaterial = getVideoMaterialAndThrowIfNotExist(request.getMaterialId());

        if (StringUtils.isBlank(videoMaterial.getUrl())) {
            throw new InngkeServiceException("视频素材未上传");
        }

        VideoMaterialUrlRequest videoMaterialIdRequest = new VideoMaterialUrlRequest();
        videoMaterialIdRequest.setUrl(videoMaterial.getUrl());

        BaseResponse<VideoInfoDto> response = videoApi.getVideoMaterialFileInfo(videoMaterialIdRequest);
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            throw new InngkeServiceException("获取视频信息失败:" + Optional.ofNullable(response)
                    .map(BaseResponse::getMsg).orElse(InngkeAppConst.EMPTY_STR));
        }

        return response.getData();
    }


    public VideoMaterialDto saveErrorContent(SaveErrorContentRequest request) {
        videoMaterialManager.update(Wrappers.<VideoMaterial>update()
                .eq(VideoMaterial.ID, request.getId())
                .set(VideoMaterial.ERROR_CONTENT, request.getErrorContent())
        );

        return toVideoMaterialDto(videoMaterialManager.getById(request.getId()));
    }

    private VideoMaterialDto toVideoMaterialDto(VideoMaterial videoMaterial) {
        VideoMaterialDto videoMaterialDto = new VideoMaterialDto();
        videoMaterialDto.setOrganizeId(videoMaterial.getOrganizeId());
        videoMaterialDto.setId(videoMaterial.getId());
        videoMaterialDto.setUserId(videoMaterial.getUserId());
        videoMaterialDto.setUrl(videoMaterial.getUrl());
        videoMaterialDto.setContent(videoMaterial.getContent());
        videoMaterialDto.setFileSize(videoMaterial.getFileSize());
        videoMaterialDto.setWidth(videoMaterial.getWidth());
        videoMaterialDto.setHeight(videoMaterial.getHeight());
        videoMaterialDto.setVideoDuration(videoMaterial.getVideoDuration());
        videoMaterialDto.setMaterialGroupId(videoMaterial.getMaterialGroupId());
        videoMaterialDto.setStatus(videoMaterial.getStatus());
        videoMaterialDto.setRetryCount(videoMaterial.getRetryCount());
        videoMaterialDto.setContentCreateTime(DateTimeUtils.getMilli(videoMaterial.getContentCreateTime()));
        videoMaterialDto.setCreateTime(DateTimeUtils.getMilli(videoMaterial.getCreateTime()));
        videoMaterialDto.setUpdateTime(DateTimeUtils.getMilli(videoMaterial.getUpdateTime()));
        videoMaterialDto.setErrorMsg(videoMaterial.getErrorMsg());
        videoMaterialDto.setErrorContent(videoMaterial.getErrorContent());
        videoMaterialDto.setTags(videoMaterial.getTags());
        videoMaterialDto.setDatasetDocumentId(videoMaterial.getDatasetDocumentId());
        videoMaterialDto.setSourceMd5(videoMaterial.getSourceMd5());
        videoMaterialDto.setSrcPath(videoMaterial.getSrcPath());
        videoMaterialDto.setClassify(videoMaterial.getClassify());
        videoMaterialDto.setCategoryIds(jsonService.toObjectList(videoMaterial.getCategoryIds(), Long.class));
        videoMaterialDto.setDirIds(jsonService.toObjectList(videoMaterial.getDirIds(),Long.class));
        return videoMaterialDto;
    }

    private VideoMaterial toVideoMaterial(UpdateVideoMaterialRequest request) {
        VideoMaterial videoMaterial = new VideoMaterial();
        videoMaterial.setId(request.getId());
        videoMaterial.setUrl(request.getUrl());
        videoMaterial.setContent(request.getContent());
        videoMaterial.setWidth(request.getWidth());
        videoMaterial.setHeight(request.getHeight());
        videoMaterial.setTags(request.getTags());
        return videoMaterial;
    }

    private VideoMaterial getVideoMaterialAndThrowIfNotExist(Long materialId) {
        VideoMaterial videoMaterial = videoMaterialManager.getById(materialId);
        if (Objects.isNull(videoMaterial)) {
            throw new InngkeServiceException("视频素材不存在");
        }
        return videoMaterial;
    }

    public List<MaterialCategoryDto> getVideoMaterialTopCategoryList(PagingMaterialRequest request) {
        List<MaterialCategory> materialCategoryList = materialCategoryManager.getTopByOrganizeIdType(request.getOrganizeId(), MaterialCategory.TYPE_VIDEO);

        return materialCategoryList.stream().map(MaterialConverter::toMaterialCategoryDto).collect(Collectors.toList());
    }

    public VideoMaterialDto addVideoMaterial(AddVideoMaterialRequest request) {
        VideoMaterial exist = videoMaterialManager.getBySourceMd5(request.getSourceMd5());
        if (Objects.nonNull(exist)) {
            throw new InngkeServiceException("素材已存在");
        }
        VideoMaterial videoMaterial = new VideoMaterial();
        videoMaterial.setId(SnowflakeHelper.getId());
        videoMaterial.setUrl(request.getUrl());
        videoMaterial.setSrcPath(request.getSrcPath());
        videoMaterial.setWidth(request.getWidth());
        videoMaterial.setHeight(request.getHeight());
        videoMaterial.setVideoDuration(request.getVideoDuration());
        videoMaterial.setDirIds(jsonService.toJson((Serializable) request.getDirIds()));
        videoMaterial.setStatus(1);
        videoMaterial.setContentCreateTime(LocalDateTime.now());
        videoMaterial.setVertical(request.getWidth() < request.getHeight());
        videoMaterial.setUserId(0L);
        videoMaterial.setCreateTime(LocalDateTime.now());
        videoMaterial.setSourceMd5(request.getSourceMd5());
        videoMaterial.setCutFrames(request.getCutFrames());

        setVideoMaterialOrganizeIdAndCategoryIds(videoMaterial, request.getDirIds());

        videoMaterialManager.save(videoMaterial);

        return toVideoMaterialDto(videoMaterialManager.getById(videoMaterial.getId()));
    }

    private void setVideoMaterialOrganizeIdAndCategoryIds(VideoMaterial videoMaterial, List<Long> dirIds) {
        List<MaterialDirTreeDto> materialDirTreeList = videoMaterialDirManager.getByIds(dirIds).stream().map(this::toMaterialDirTreeDto).collect(Collectors.toList());

        Map<Long, MaterialDirTreeDto> dirMap = materialDirTreeList.stream().collect(Collectors.toMap(MaterialDirTreeDto::getId, Function.identity()));

        MaterialDirTreeDto current = null;

        for (MaterialDirTreeDto videoMaterialDir : materialDirTreeList) {
            MaterialDirTreeDto parent = dirMap.get(videoMaterialDir.getParentId());
            if (Objects.isNull(parent)) {
                current = videoMaterialDir;
            } else {
                parent.getChildren().add(videoMaterialDir);
            }
        }

        while (Objects.nonNull(current)) {
            if (current.getOrganizeId() != null && current.getOrganizeId() != 0L) {
                videoMaterial.setOrganizeId(current.getOrganizeId());
            }
            if (CollectionUtils.isNotEmpty(current.getCategoryIds())) {
                videoMaterial.setCategoryIds(jsonService.toJson((Serializable) current.getCategoryIds().stream().map(Long::valueOf).collect(Collectors.toList())));
            }
            if (CollectionUtils.isNotEmpty(current.getChildren())) {
                current = current.getChildren().stream().findFirst().orElse(null);
            } else {
                current = null;
            }
        }
    }

    private MaterialDirTreeDto toMaterialDirTreeDto(VideoMaterialDir materialDir) {
        MaterialDirTreeDto materialDirTreeDto = new MaterialDirTreeDto();
        materialDirTreeDto.setId(materialDir.getId());
        materialDirTreeDto.setCategoryIds(
                StringUtils.isNotBlank(materialDir.getCategoryIds()) ?
                        Splitter.on(InngkeAppConst.COMMA_STR).splitToList(materialDir.getCategoryIds()) : Lists.newArrayList()
        );
        materialDirTreeDto.setFullPath(materialDir.getFullPath());
        materialDirTreeDto.setName(materialDir.getName());
        materialDirTreeDto.setEnv(materialDir.getEnv());
        materialDirTreeDto.setOrganizeId(materialDir.getOrganizeId());
        materialDirTreeDto.setParentId(materialDir.getPid());
        materialDirTreeDto.setChildren(Lists.newArrayList());
        return materialDirTreeDto;
    }

    public List<VideoMaterialDto> getVideoMaterialListByLastId(IdPageRequest request) {
        List<VideoMaterial> videoMaterials = videoMaterialManager.list(
                Wrappers.<VideoMaterial>query()
                        .gt(VideoMaterial.ID, Optional.ofNullable(request.getLastId()).orElse(0L))
                        .ne(VideoMaterial.STATUS, -1)
                        .orderByAsc(VideoMaterial.ID)
                        .last("limit " + request.getPageSize()));

        return videoMaterials.stream().map(this::toVideoMaterialDto).collect(Collectors.toList());
    }

    public Boolean isExist(String md5) {
        return videoMaterialManager.count(Wrappers.<VideoMaterial>query().eq(VideoMaterial.SOURCE_MD5, md5).ne(VideoMaterial.STATUS, -1)) > 0;
    }
}
