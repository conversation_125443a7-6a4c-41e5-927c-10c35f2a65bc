package com.inngke.ai.crm.service;

import com.inngke.ai.crm.controller.StaffInfoDto;
import com.inngke.ai.crm.dto.request.BaseImportRequest;
import com.inngke.ai.crm.dto.request.org.DeleteStaffRequest;
import com.inngke.ai.crm.dto.request.org.EditStaffRequest;
import com.inngke.ai.crm.dto.request.org.GetOrgStaffPagingRequest;
import com.inngke.ai.crm.dto.request.org.GetStaffInfoRequest;
import com.inngke.ai.crm.dto.request.org.staff.CreateStaffRequest;
import com.inngke.ai.crm.dto.response.org.StaffItemDto;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;

public interface EmployeeService {

    BaseResponse<BasePaginationResponse<StaffItemDto>> importStaff(BaseImportRequest request);

    BaseResponse<BasePaginationResponse<StaffItemDto>> getQuiteStaffPaging(GetOrgStaffPagingRequest request);
}
