package com.inngke.ai.crm.service;

import com.inngke.ai.crm.dto.IdPageDto;
import com.inngke.ai.crm.dto.request.CrmCoinOrderListRequest;
import com.inngke.ai.crm.dto.response.CrmCoinOrderListDto;
import com.inngke.common.dto.response.BaseResponse;

/**
 * <AUTHOR>
 * @since 2023-09-19 17:51
 **/
public interface CrmCoinOrderService {
    BaseResponse<IdPageDto<CrmCoinOrderListDto>> list(CrmCoinOrderListRequest request);
}
