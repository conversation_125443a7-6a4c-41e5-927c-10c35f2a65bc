package com.inngke.ai.crm.dto.response.script;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/4/8 15:52
 */
@Data
public class ScriptTypeTreeDto implements Serializable {

    /**
     * 标志code
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     *  ID
     */
    private Long id;

    /**
     * 自定义排序
     */
    private Integer customSort;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long updateTime;

    /**
     * 子树结构
     */
    private List<ScriptTypeTreeDto> children;
}

