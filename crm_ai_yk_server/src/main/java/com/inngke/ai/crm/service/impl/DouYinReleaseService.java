package com.inngke.ai.crm.service.impl;

import cn.hutool.core.net.URLEncodeUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.inngke.ai.crm.core.CrmServiceConsts;
import com.inngke.ai.crm.core.util.QRCodeGenerator;
import com.inngke.ai.crm.db.crm.entity.AiGenerateTask;
import com.inngke.ai.crm.db.crm.entity.AiGenerateVideoOutput;
import com.inngke.ai.crm.db.crm.entity.Staff;
import com.inngke.ai.crm.db.crm.manager.*;
import com.inngke.ai.crm.dto.enums.AppConfigCodeEnum;
import com.inngke.ai.crm.dto.response.video.ReleaseSchemaDto;
import com.inngke.ai.crm.service.StaffService;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.JsonService;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.Md5Utils;
import com.inngke.ip.ai.douyin.api.DouYinCommonApi;
import com.inngke.ip.ai.douyin.api.DouYinOauthApi;
import com.inngke.ip.ai.douyin.api.DouYinOpenApi;
import com.inngke.ip.ai.douyin.dto.DouYinClientTokenDto;
import com.inngke.ip.ai.douyin.dto.DouYinTicketDto;
import com.inngke.ip.ai.douyin.dto.DouYinTicketResponse;
import com.inngke.ip.ai.douyin.dto.ReleaseSchemaParamsDto;
import com.inngke.ip.ai.douyin.dto.reqeust.GetAccessTokenRequest;
import com.inngke.ip.ai.douyin.dto.response.DouYinBaseDto;
import com.inngke.ip.ai.douyin.dto.response.DouYinBaseResponse;
import com.inngke.ip.ai.douyin.dto.response.DouYinShareIdDto;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

@Service
public class DouYinReleaseService {
    private static final String RELEASE_LOCK_KEY = CrmServiceConsts.CACHE_LOCK_KEY_PRE + "dy-release:";
    private static final Logger logger = LoggerFactory.getLogger(DouYinReleaseService.class);
    private static final String CLIENT_CREDENTIAL = "client_credential";
    private static final String SCHEME_PREFIX = "snssdk1128://openplatform/share";
    private static final Integer MAX_QR_MARK_LENGTH = 20;

    public static final List<String> APP_CONFIG_CODE_LIST = Lists.newArrayList(
            AppConfigCodeEnum.DOU_YIN_WEB_APP_KEY.getCode(),
            AppConfigCodeEnum.DOU_YIN_WEB_APP_SECRET.getCode()
    );

    @Autowired
    private DouYinOauthApi douYinOauthApi;
    @Autowired
    private DouYinOpenApi douYinOpenApi;
    @Autowired
    private DouYinCommonApi douYinCommonApi;
    @Autowired
    private AppConfigManager appConfigManager;
    @Autowired
    private JsonService jsonService;
    @Autowired
    private AiGenerateVideoOutputManager aiGenerateVideoOutputManager;
    @Autowired
    private AiGenerateTaskIoManager aiGenerateTaskIoManager;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private CommonService commonService;
    @Autowired
    private DouYinReleaseLogManager douYinReleaseLogManager;
    @Autowired
    private DouYinAccountManager douYinAccountManager;
    @Autowired
    private StaffService staffService;
    @Autowired
    private AiGenerateTaskManager aiGenerateTaskManager;

    public String getClientTicket(String accessToken) {
        ValueOperations cache = redisTemplate.opsForValue();
        String ticketCacheKey = CrmServiceConsts.CACHE_KEY_PRE + "dou-yin:ticket";

        String ticket = (String) cache.get(ticketCacheKey);

        if (StringUtils.isBlank(ticket)) {
            DouYinTicketResponse response = douYinOpenApi.getTicket(accessToken);

            ticket = Optional.ofNullable(response).map(DouYinBaseResponse::getData).map(DouYinTicketDto::getTicket).orElse(null);
            if (StringUtils.isBlank(ticket)) {
                logger.info("请求抖音失败:{}", jsonService.toJson(response));
                throw new InngkeServiceException("请求抖音失败");
            }

            cache.set(ticketCacheKey, ticket, response.getData().getExpiresIn() - 600, TimeUnit.SECONDS);

        }

        return ticket;
    }

    public String getClientToken(Map<String, String> configMap) {
        ValueOperations cache = redisTemplate.opsForValue();
        String ticketCacheKey = CrmServiceConsts.CACHE_KEY_PRE + "dou-yin:token";

        String accessToken = (String) cache.get(ticketCacheKey);
        if (StringUtils.isBlank(accessToken)) {
            GetAccessTokenRequest getClientTokenRequest = new GetAccessTokenRequest();
            getClientTokenRequest.setClientSecret(configMap.get(AppConfigCodeEnum.DOU_YIN_WEB_APP_SECRET.getCode()));
            getClientTokenRequest.setGrantType(CLIENT_CREDENTIAL);
            getClientTokenRequest.setClientKey(configMap.get(AppConfigCodeEnum.DOU_YIN_WEB_APP_KEY.getCode()));

            DouYinBaseResponse<DouYinClientTokenDto> getClientTokenResponse = douYinOauthApi.getClientToken(getClientTokenRequest);
            if (!DouYinBaseResponse.isSuccess(getClientTokenResponse)) {
                throw new InngkeServiceException(
                        "请求抖音失败" + Optional.of(getClientTokenResponse).map(DouYinBaseResponse::getData).map(DouYinBaseDto::getDescription).orElse(InngkeAppConst.EMPTY_STR)
                );
            }

            DouYinClientTokenDto data = getClientTokenResponse.getData();
            accessToken = data.getAccessToken();
            if (StringUtils.isBlank(accessToken)) {
                logger.info("请求抖音失败:{}", jsonService.toJson(getClientTokenResponse));
                throw new InngkeServiceException("请求抖音失败");
            }

            cache.set(ticketCacheKey, accessToken, 30, TimeUnit.MINUTES);
        }

        return accessToken;
    }

    public ReleaseSchemaDto getReleaseSchema(JwtPayload jwtPayload, Long videoId, Long publishTaskId) {
        //正常状态的帐号数量至少需要一个
        if (douYinAccountManager.getNormalAccountCount(jwtPayload.getCid()) <= 0) {
            throw new InngkeServiceException(40000, "未授权抖音帐号");
        }

        String cacheKey = CrmServiceConsts.CACHE_KEY_PRE + "release:schema:dto:" + videoId;

        ValueOperations cache = redisTemplate.opsForValue();

        ReleaseSchemaDto releaseSchemaDto = Optional.ofNullable(cache.get(cacheKey)).map(String::valueOf)
                .map(schemaDtoJson -> jsonService.toObject(schemaDtoJson, ReleaseSchemaDto.class)).orElse(null);

        if (Objects.isNull(releaseSchemaDto)) {
            //获取视频信息,配置信息
            CompletableFuture<AiGenerateVideoOutput> videoFuture = AsyncUtils.supplyTraceAsync(() -> aiGenerateVideoOutputManager.getById(videoId));
            CompletableFuture<Map<String, String>> configMapFuture = AsyncUtils.supplyTraceAsync(() -> appConfigManager.getValueByCodeList(APP_CONFIG_CODE_LIST));

            AiGenerateVideoOutput video = AsyncUtils.getFutureData(videoFuture);
            Map<String, String> configMap = AsyncUtils.getFutureData(configMapFuture);
            if (Objects.isNull(video)) {
                throw new InngkeServiceException("发送失败：视频不存在");
            }
            AiGenerateTask task = aiGenerateTaskManager.getOne(
                    Wrappers.<AiGenerateTask>query()
                            .eq(AiGenerateTask.ID, video.getTaskId())
                            .select(AiGenerateTask.TAGS)
            );

            //获取抖音clientToken
            String accessToken = getClientToken(configMap);

            //获取抖音ticket，分享id
            CompletableFuture<String> shareIdFuture = AsyncUtils.supplyTraceAsync(() -> getShareId(accessToken));
            CompletableFuture<String> clientTicketFuture = AsyncUtils.supplyTraceAsync(() -> getClientTicket(accessToken));

            String shareId = AsyncUtils.getFutureData(shareIdFuture);

            String schemaUrl = genSchema(configMap, task, video, AsyncUtils.getFutureData(clientTicketFuture), shareId);

            //生成schema链接并转短链
            String url = commonService.toShortLink(schemaUrl, video.getCreatorId());

            //保存分享日志
            douYinReleaseLogManager.addLog(videoId, video.getTaskId(), shareId, jwtPayload.getCid(), publishTaskId);

            releaseSchemaDto = toReleaseSchemaDto(video, schemaUrl, url);

            cache.set(cacheKey, jsonService.toJson(releaseSchemaDto), 60, TimeUnit.SECONDS);
        }

        return releaseSchemaDto;
    }

    private ReleaseSchemaDto toReleaseSchemaDto(AiGenerateVideoOutput video, String schemaUrl, String url) {
        String videoContent = video.getVideoContent();
        if (StringUtils.isNotBlank(videoContent)) {
            if (StringUtils.length(videoContent) > MAX_QR_MARK_LENGTH) {
                videoContent = StringUtils.substring(videoContent, 0, MAX_QR_MARK_LENGTH - 3) + "...";
            }
        } else {
            videoContent = "发布视频二维码";
        }

        ReleaseSchemaDto releaseSchemaDto = new ReleaseSchemaDto();
        releaseSchemaDto.setUrl(url);
        releaseSchemaDto.setQrCode(QRCodeGenerator.generateQRCodeBase64(schemaUrl, 512, 512, 25, videoContent));

        return releaseSchemaDto;
    }

    private String genSchema(Map<String, String> configMap, AiGenerateTask task, AiGenerateVideoOutput video, String ticket, String shareId) {
        ReleaseSchemaParamsDto releaseSchemaParamsDto = initReleaseParams(configMap, task, video, shareId);

        //签名
        releaseSchemaParamsDto.setNonceStr(CommonService.randomChar(16));
        releaseSchemaParamsDto.setTimestamp(String.valueOf(DateTimeUtils.getMilli(LocalDateTime.now()) / 1000));

        String signatureStr = releaseSchemaParamsDto.getSignatureStr(ticket);
        releaseSchemaParamsDto.setSignature(Md5Utils.md5(signatureStr));

        return SCHEME_PREFIX + InngkeAppConst.ASK_STR + Joiner.on(InngkeAppConst.AND_STR)
                .useForNull(InngkeAppConst.EMPTY_STR).withKeyValueSeparator(InngkeAppConst.EQUAL_STR)
                .join(jsonService.toObject(jsonService.toJson(releaseSchemaParamsDto), Map.class));
    }

    private ReleaseSchemaParamsDto initReleaseParams(
            Map<String, String> configMap, AiGenerateTask task, AiGenerateVideoOutput video, String shareId) {
        ReleaseSchemaParamsDto releaseSchemaParamsDto = new ReleaseSchemaParamsDto();
        releaseSchemaParamsDto.setShareType("h5");
        releaseSchemaParamsDto.setClientKey(configMap.get(AppConfigCodeEnum.DOU_YIN_WEB_APP_KEY.getCode()));
        releaseSchemaParamsDto.setState(shareId);
        releaseSchemaParamsDto.setVideoPath(URLEncodeUtil.encode(Optional.ofNullable(video.getVideo1080Url()).orElse(video.getVideoUrl())));
        releaseSchemaParamsDto.setShareToPublish(1);
        String videoContent = video.getVideoContent();
        if (task != null && StringUtils.isNotBlank(task.getTags())) {
            videoContent += InngkeAppConst.WHITE_SPACE_STR + task.getTags();
        }
        releaseSchemaParamsDto.setTitle(URLEncodeUtil.encode(videoContent));
        return releaseSchemaParamsDto;

    }

    private String getShareId(String accessToken) {
        DouYinBaseResponse<DouYinShareIdDto> response = douYinCommonApi.getShareId(accessToken);

        return Optional.ofNullable(response).map(DouYinBaseResponse::getData).map(DouYinShareIdDto::getShareId).orElse(null);
    }
}
