package com.inngke.ai.crm.service.message.sender;

import com.inngke.ai.crm.client.common.ShortLinkServiceForAi;
import com.inngke.ai.crm.core.config.AiGcConfig;
import com.inngke.ai.crm.db.crm.entity.CreationTask;
import com.inngke.ai.crm.db.crm.entity.PublishTask;
import com.inngke.ai.crm.service.message.CrmMessageTypeEnum;
import com.inngke.ai.crm.service.message.content.CrmCreationTaskSmsContext;
import com.inngke.ai.crm.service.message.content.CrmMessageContext;
import com.inngke.ai.crm.service.message.content.CrmPublishTaskSmsContext;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.ip.common.dto.request.ShortLinkGenerateRequest;
import com.inngke.ip.common.dto.response.ShortLinkGenerateDto;
import com.inngke.ip.reach.dto.request.TemplateMessageSendRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class CrmAiPublishTaskMsgSenderService extends CrmMessageSenderServiceAbs {

    @Autowired
    private ShortLinkServiceForAi shortLinkServiceForAi;

    @Autowired
    private AiGcConfig aiGcConfig;

    @Override
    public CrmMessageTypeEnum getMessageType() {
        return CrmMessageTypeEnum.PUBLISH_TASK_MSG;
    }

    @Override
    public void init(CrmMessageContext ctx) {

    }

    @Override
    public TemplateMessageSendRequest sendMessage(CrmMessageContext ctx) {
        CrmPublishTaskSmsContext context = (CrmPublishTaskSmsContext) ctx;

        PublishTask publishTask = context.getPublishTask();

        if (Objects.isNull(publishTask)) {
            throw new InngkeServiceException("发送短信，任务详情为空");
        }

        ShortLinkGenerateRequest shortLinkGenerateRequest = new ShortLinkGenerateRequest();
        shortLinkGenerateRequest.setBid(aiGcConfig.getBid());
        shortLinkGenerateRequest.setPagePath("subpackages/generator/video/detail");
        shortLinkGenerateRequest.setPageRequest("publishTaskId=" + context.getPublishTask().getId());

        ShortLinkGenerateDto generate = shortLinkServiceForAi.generate(shortLinkGenerateRequest);

        return getTemplateRequestBuilder()
                .setVar("code", generate.getCode())
                .setMobile(context.getMobile())
                .build();
    }
}
