package com.inngke.ai.crm.core;

/**
 * <AUTHOR>
 * @since 2023/7/25 10:03
 */
public class CrmServiceConsts {

    private CrmServiceConsts(){}
    public static final String VIDEO_SNAPSHOT_PARAMS = "?x-oss-process=video/snapshot,t_0,f_jpg,m_fast";

    public static final String APP_ID = "crm_ai_yk";
    public static final String DS_APP_ID_SELECTOR = "#BID: " + APP_ID;
    public static final String CACHE_KEY_PRE = APP_ID + ":";
    public static final String CACHE_LOCK_KEY_PRE = CACHE_KEY_PRE + "lock:";

    public static final Integer USE_EXTERNAL_MODEL = 1;

    public static final Integer WAIT_EXTERNAL_MODEL_RESULT_TIMES = 20;

    public static final String APP_CODE = "ai-pc";

    public static final String ROLE_CODE = "manager";

    public static final Long INNER_ORGANIZE_ID = 1L;
}