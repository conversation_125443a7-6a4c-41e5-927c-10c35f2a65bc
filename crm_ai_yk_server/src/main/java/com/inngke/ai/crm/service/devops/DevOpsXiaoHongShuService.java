package com.inngke.ai.crm.service.devops;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.inngke.ai.crm.db.crm.entity.DifyAppConf;
import com.inngke.ai.crm.db.crm.manager.DifyAppConfManager;
import com.inngke.ai.crm.dto.enums.AiProductIdEnum;
import com.inngke.ai.crm.dto.request.devops.AddXhsAppConfigRequest;
import com.inngke.ai.crm.dto.request.devops.GetAppConfigRequest;
import com.inngke.ai.crm.dto.response.devops.DifyAppConfDto;
import com.inngke.common.core.InngkeAppConst;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class DevOpsXiaoHongShuService {

    @Autowired
    private DifyAppConfManager difyAppConfManager;

    /**
     * 获取小红书应用配置
     */
    public List<DifyAppConfDto> getAppConfig(GetAppConfigRequest request) {
        List<DifyAppConf> list = difyAppConfManager.list(Wrappers.<DifyAppConf>query()
                .eq(DifyAppConf.ORGANIZE_ID, request.getOrganizeId())
                .eq(Objects.nonNull(request.getAiProductId()), DifyAppConf.AI_PRODUCT_ID, request.getAiProductId())
                .eq(DifyAppConf.STATUS, 1)
                .orderByAsc(DifyAppConf.SORT)
        );

        return list.stream().map(this::toDifyAppConfDto).collect(Collectors.toList());
    }


    public DifyAppConfDto saveAppConfig(AddXhsAppConfigRequest request) {
        DifyAppConf difyAppConf = toDifyAppConf(request);

        difyAppConfManager.saveOrUpdate(difyAppConf);

        return toDifyAppConfDto(difyAppConfManager.getById(difyAppConf.getId()));
    }

    private DifyAppConfDto toDifyAppConfDto(DifyAppConf difyAppConf) {
        DifyAppConfDto difyAppConfDto = new DifyAppConfDto();
        difyAppConfDto.setId(difyAppConf.getId());
        difyAppConfDto.setOrganizeId(difyAppConf.getOrganizeId());
        difyAppConfDto.setAiProductId(difyAppConf.getAiProductId());
        difyAppConfDto.setSort(difyAppConf.getSort());
        difyAppConfDto.setAppKey(difyAppConf.getAppKey());
        difyAppConfDto.setName(difyAppConf.getName());
        difyAppConfDto.setFormColumnConfig(difyAppConf.getFormColumnConfig());
        difyAppConfDto.setFormColumnConfig2(difyAppConf.getFormColumnConfig2());
        difyAppConfDto.setDescription(difyAppConf.getDescription());
        difyAppConfDto.setImageUrl(difyAppConf.getImageUrl());
        difyAppConfDto.setVipProduct(difyAppConf.getVipProduct());
        difyAppConfDto.setBgColor(difyAppConf.getBgColor());
        difyAppConfDto.setImageMark(difyAppConf.getImageMark());
        difyAppConfDto.setHasProduct(difyAppConf.getHasProduct());
        difyAppConfDto.setCoverId(difyAppConf.getCoverId());
        difyAppConfDto.setCategoryId(difyAppConf.getCategoryId());
        difyAppConfDto.setWorkflow(difyAppConf.getWorkflow());
        if (StringUtils.isNotBlank(difyAppConf.getVideoMaterialGroupIds())){
            difyAppConfDto.setVideoMaterialGroupIds(Sets.newHashSet(
                    Splitter.on(InngkeAppConst.COMMA_STR).split(difyAppConf.getVideoMaterialGroupIds())
            ).stream().map(Long::valueOf).collect(Collectors.toSet()));
        }else {
            difyAppConfDto.setVideoMaterialGroupIds(Sets.newHashSet());
        }
        return difyAppConfDto;
    }

    private DifyAppConf toDifyAppConf(AddXhsAppConfigRequest request) {
        DifyAppConf difyAppConf = new DifyAppConf();
        difyAppConf.setOrganizeId(request.getOrganizeId());
        difyAppConf.setAiProductId(request.getAiProductId());
        difyAppConf.setSort(request.getSort());
        difyAppConf.setAppKey(request.getAppKey());
        difyAppConf.setName(request.getName());
        difyAppConf.setFormColumnConfig(request.getFormColumnConfig());
        difyAppConf.setFormColumnConfig2(request.getFormColumnConfig2());
        difyAppConf.setDescription(request.getDescription());
        difyAppConf.setImageUrl(request.getImageUrl());
        difyAppConf.setId(request.getId());
        difyAppConf.setVipProduct(true);
        difyAppConf.setBgColor(request.getBgColor());
        difyAppConf.setImageMark(request.getImageMark());
        difyAppConf.setHasProduct(request.getHasProduct());
        difyAppConf.setCoverId(request.getCoverId());
        difyAppConf.setCategoryId(request.getCategoryId());
        difyAppConf.setWorkflow(request.getWorkflow());
        if (!Objects.isNull(request.getVideoMaterialGroupIds())){
            difyAppConf.setVideoMaterialGroupIds(Joiner.on(InngkeAppConst.COMMA_STR).join(request.getVideoMaterialGroupIds()));
        }
        if (Objects.isNull(request.getId())) {
            difyAppConf.setCreateTime(LocalDateTime.now());
        }
        return difyAppConf;
    }

    public Boolean deleteConfig(Integer id) {
        return difyAppConfManager.update(Wrappers.<DifyAppConf>update().eq(DifyAppConf.ID, id).set(DifyAppConf.STATUS, -1));
    }
}
