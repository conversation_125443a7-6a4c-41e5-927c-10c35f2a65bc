package com.inngke.ai.crm.service;

import com.inngke.ai.crm.db.crm.entity.VideoBgmMaterial;
import com.inngke.ai.crm.dto.request.video.*;
import com.inngke.ai.crm.dto.response.VideoDetailDto;
import com.inngke.ai.crm.dto.response.VideoExampleDto;
import com.inngke.ai.crm.dto.response.video.VideoMaterialDto;
import com.inngke.ai.dto.config.VideoConfig;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.request.BaseIdRequest;
import com.inngke.common.dto.response.BaseResponse;

import java.util.List;

public interface VideoService {

    /**
     * 查询多媒体素材列表
     *
     * @param jwtPayload 当前用户
     * @param request    查询请求
     * @return 多媒体素材列表
     */
    List<VideoMaterialDto> listVideoMaterial(JwtPayload jwtPayload, VideoMaterialQuery request);

    /**
     * 查询多媒体素材详情
     *
     * @param jwtPayload 当前用户
     * @param id         素材ID
     * @return 多媒体素材详情
     */
    VideoMaterialDto getVideoMaterial(JwtPayload jwtPayload, long id);

    List<VideoExampleDto> videoExample();

    BaseResponse<VideoDetailDto> detail(JwtPayload jwtPayload, Long id);

    BaseResponse<VideoDetailDto> detail(JwtPayload jwtPayload, GetVideoDetailRequest request);

    /**
     * 创建多媒体素材（批量）
     */
    VideoMaterialDto createMaterialBatch(VideoMaterialSyncRequest request);

    List<VideoBgmMaterial> listBgmMaterial(VideoBgmMaterialQuery request);

    String getVideoMaterialIds(VideoMaterialIdsGetRequest request);

    VideoConfig getVideoConfig(VideoConfigRequest request);

    Boolean categorySet(JwtPayload jwtPayload, VideoCategorySetRequest request);
}
