package com.inngke.ai.crm.service.material;

import com.inngke.ai.crm.dto.request.material.AddUserMaterialRequest;
import com.inngke.ai.crm.dto.request.material.ListMaterialRequest;
import com.inngke.ai.crm.dto.request.material.RotateMaterialRequest;
import com.inngke.ai.crm.dto.request.video.VideoCompressData;
import com.inngke.ai.crm.dto.response.material.UserMaterialDto;
import com.inngke.common.dto.JwtPayload;

import java.util.List;

public interface UserMaterialService {
    List<UserMaterialDto> addMaterial(JwtPayload jwtPayload, AddUserMaterialRequest request);

    List<UserMaterialDto> listMaterials(JwtPayload jwtPayload, ListMaterialRequest request);

    Boolean rotate(JwtPayload jwtPayload, RotateMaterialRequest request);
    
    Boolean rotate2(JwtPayload jwtPayload, RotateMaterialRequest request);

    Boolean compressed(Long id, VideoCompressData data);
}
