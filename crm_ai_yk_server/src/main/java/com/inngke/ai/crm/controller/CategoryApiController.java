package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.dto.request.base.CategoryQuery;
import com.inngke.ai.crm.dto.request.base.CategorySaveRequest;
import com.inngke.ai.crm.dto.response.CategoryNode;
import com.inngke.ai.crm.dto.response.common.CategoryDto;
import com.inngke.ai.crm.service.CategoryService;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @chapter 公共接口
 * @section 分类
 */
@RestController
@RequestMapping("/api/ai/category")
public class CategoryApiController {

    @Autowired
    private CategoryService categoryService;

    /**
     * 分类树接口
     */
    @GetMapping("/{type}/tree")
    public BaseResponse<List<CategoryNode>> getTree(
            @RequestAttribute JwtPayload jwtPayload,
            @PathVariable String type,
            CategoryQuery request
    ) {
        return BaseResponse.success(categoryService.getTree(jwtPayload, request));
    }

    /**
     * 分类列表接口
     */
    @GetMapping("/{type}/list")
    public BaseResponse<List<CategoryDto>> getList(
            @RequestAttribute JwtPayload jwtPayload,
            @PathVariable String type) {
        return BaseResponse.success(categoryService.getList(jwtPayload,type));
    }

    /**
     * 分类创建/修改
     */
    @PostMapping("/{type}")
    public BaseResponse<Long> save(
            @RequestAttribute JwtPayload jwtPayload,
            @PathVariable String type,
            @RequestBody CategorySaveRequest request
    ) {
        request.setType(type);
        return BaseResponse.success(categoryService.save(jwtPayload, request));
    }

    /**
     * 删除分类
     */
    @DeleteMapping("/{type}/{id:\\d+}")
    public BaseResponse<Boolean> delete(
            @RequestAttribute JwtPayload jwtPayload,
            @PathVariable String type,
            @PathVariable long id
    ) {
        return BaseResponse.success(categoryService.delete(jwtPayload, type, id));
    }
}
