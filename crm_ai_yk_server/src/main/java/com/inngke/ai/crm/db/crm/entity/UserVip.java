/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.util.Objects;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class UserVip implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 雪花ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 企业ID， 0表示个人
     */
    private Long organizeId;

    /**
     * 姓名
     */
    private String realName;

    /**
     * 用户ID，即user.id，0表示未关联上用户
     */
    private Long userId;

    /**
     * 员工id
     */
    private Long staffId;

    /**
     * 员工手机号码
     */
    private String mobile;

    /**
     * 所属部门
     */
    private String departmentName;

    /**
     * 部门id
     */
    private Long departmentId;

    /**
     * 对应的商品ID，即coin_product.id
     */
    private Long coinProductId;

    /**
     * 对应的订单ID，即coin_order.id
     */
    private Long coinOrderId;

    /**
     * VIP类型 0=非VIP 1=VIP 2=SVIP
     */
    private Integer vipType;

    /**
     * 周期类型0=一次性 1=月（周期性） 2=季度 3=年
     */
    private Integer periodType;

    /**
     * 发放的积分/月
     */
    private Integer coin;

    /**
     * 剩余待发放次数
     */
    private Integer remainCount;

    /**
     * 总共次数
     */
    private Integer totalCount;

    /**
     * 状态： 0=无效 1=有效
     */
    private Boolean enable;

    /**
     * 激活码（使用当前ID）
     */
    private String code;

    /**
     * 备注
     */
    private String remark;

    /**
     * 购买会员卡时的价格
     */
    private Integer amount;


    /**
     * 激活时间
     */
    private LocalDateTime activationTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    public static final String ID = "id";

    public static final String ORGANIZE_ID = "organize_id";

    public static final String REAL_NAME = "real_name";

    public static final String USER_ID = "user_id";

    public static final String MOBILE = "mobile";

    public static final String DEPARTMENT_ID = "department_id";

    public static final String STAFF_ID = "staff_id";

    public static final String DEPARTMENT_NAME = "department_name";

    public static final String COIN_PRODUCT_ID = "coin_product_id";

    public static final String COIN_ORDER_ID = "coin_order_id";

    public static final String VIP_TYPE = "vip_type";

    public static final String PERIOD_TYPE = "period_type";

    public static final String COIN = "coin";

    public  static final String AMOUNT = "amount";

    public static final String REMAIN_COUNT = "remain_count";

    public static final String TOTAL_COUNT = "total_count";

    public static final String ENABLE = "enable";

    public static final String CODE = "code";

    public static final String REMARK = "remark";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String ACTIVATION_TIME = "activation_time";

    public boolean isOverdue(){
        if (Objects.isNull(this.getActivationTime())) {
            return false;
        }
        //月
        if (getPeriodType() == 1){
            return this.getCreateTime().plusMonths(1).isBefore(LocalDateTime.now());
        }
        //季度
        if (getPeriodType() == 2){
            return this.getCreateTime().plusMonths(3).isBefore(LocalDateTime.now());
        }
        //年
        if (getPeriodType() == 3){
            return this.getCreateTime().plusMonths(12).isBefore(LocalDateTime.now());
        }
        //15天
        if (getPeriodType() == 4){
            return this.getCreateTime().plusDays(15).isBefore(LocalDateTime.now());
        }
        return true;
    }
}
