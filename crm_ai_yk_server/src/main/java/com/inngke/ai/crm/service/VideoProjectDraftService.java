package com.inngke.ai.crm.service;

import com.inngke.ai.crm.dto.request.devops.VideoBgmListRequest;
import com.inngke.ai.crm.dto.request.video.*;
import com.inngke.ai.crm.dto.response.video.*;
import com.inngke.ai.dto.request.VideoDigitalHumanConfig;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.ip.ai.dify.app.dto.VideoScriptsDto;

import java.util.List;
import java.util.Map;

public interface VideoProjectDraftService {
    BasePaginationResponse<VideoProjectDraftDto> getDraftList(JwtPayload jwtPayload, VideoProjectDraftRequest request);

    List<VideoProjectDraftDto> getDraftDemos(JwtPayload jwtPayload);

    VideoProjectDraftDetail cloneFromDraft(JwtPayload jwtPayload, VideoProjectDraftCreateRequest request);

    boolean setTitle(JwtPayload jwtPayload, VideoProjectDraftTitleSetRequest request);

    VideoProjectDraftDetail saveDraft(JwtPayload jwtPayload, VideoCreateWithMaterialRequest request, String draftTitle);

    String screenshot(String videoUr, int time, int rotate);

    VideoProjectDraftDetail getDraftDetail(JwtPayload jwtPayload, Long draftId);

    BasePaginationResponse<VideoBgmMaterialDto> getBgmList(JwtPayload jwtPayload, VideoBgmListRequest request);

    List<VideoDigitalHumanConfig> getDigitalHumanConfig(JwtPayload jwtPayload, VideoDigitalHumanRequest request);

    VideoSubtitleBaseInfoResponse getVideoSubtitleBaseInfo(JwtPayload jwtPayload, VideoDigitalHumanRequest request);

    VideoScriptsDto createScript(JwtPayload jwtPayload, VideoCreateWithMaterialRequest request);

    Boolean draftDelete(JwtPayload jwtPayload, Long draftId);

    void cacheDigitalHumanConfig(Long cid, List<VideoDigitalHumanConfig> configs);

    VideoSubtitleGetResponse videoSubtitleGet(JwtPayload jwtPayload, VideoSubtitleGetRequest request);

    VideoSubtitleGetResponse videoSubtitleRepair(JwtPayload jwtPayload, VideoSubtitleRepairRequest request);

    VideoProjectDraftDetail getDefaultDraft(long userId);

    boolean checkVideoSubtitle(String url);

    void saveUserVideoConfig(Map<String, Object> promptMap, Long userId, Long widgetId);

    VideoProjectVideoMashupDetail saveVideoMashupDraft(JwtPayload jwtPayload, VideoCreateWithVideoMashupRequest request);

    VideoProjectVideoMashupDetail draftVideoMashupScript(JwtPayload jwtPayload, VideoCreateWithVideoMashupRequest request);

    VideoProjectVideoMashupDetail getVideoMashupDraftDetail(JwtPayload jwtPayload, Long draftId);

    VideoProjectVideoMashupDetail draftVideoMashupPreview(JwtPayload jwtPayload, Long draftId);

    Map<Long, String> getDraftVideoMashupPreview(JwtPayload jwtPayload, Long draftId);

    VideoProjectDraftDto getDraftBasicInfo(JwtPayload jwtPayload, Long draftId);
}
