package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.dto.request.GetStaffEsDocsRequest;
import com.inngke.ai.crm.dto.response.StaffEsDto;
import com.inngke.ai.crm.service.es.StaffEsService;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.ip.common.dto.response.EsDocsResponse;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @chapter 企业模块
 * @section 员工ES
 */
@RestController
@RequestMapping("/api/ai-staff")
public class StaffEsController {

    @Resource
    private StaffEsService staffEsService;


    @GetMapping("/docs")
    public BaseResponse<EsDocsResponse<StaffEsDto>> getDocs(GetStaffEsDocsRequest request){
        return staffEsService.getDocList(request.getLastAiStaffId(),request.getPageSize());
    }
}
