/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.dao;

import com.inngke.ai.crm.db.crm.entity.TextStyleConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * 文本样式配置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-23
 */
public interface TextStyleConfigDao extends BaseMapper<TextStyleConfig> {

    @Select("select * from text_style_config where type = #{type} order by rand() limit 1")
    TextStyleConfig random(@Param("type") int type);
}
