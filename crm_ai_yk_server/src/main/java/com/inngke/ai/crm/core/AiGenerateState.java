package com.inngke.ai.crm.core;

import com.inngke.ai.crm.core.util.CrmUtils;
import com.inngke.ai.crm.db.crm.entity.AiGenerateTask;
import com.inngke.ai.crm.dto.response.ai.AiGenerateStateConfig;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.ip.ai.dify.dto.response.DifyResponse;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.Consumer;

public class AiGenerateState {
    private final List<AiGenerateStateConfig> configs = new ArrayList<>();

    private final SseEmitter sseEmitter;

    private AiGenerateTask task;

    /**
     * 会话ID
     */
    private String conversationId;

    /**
     * 可以用于存放额外的任意数据
     */
    private Object extraObj;

    /**
     * 没有发送给前端的数据缓冲区
     */
    private final StringBuilder buffer = new StringBuilder();

    /**
     * 阶段：0=未开始 1=...
     */
    private int stage = -1;

    /**
     * 是否忽略当前阶段，仅当 stageBack=0时会产生
     */
    private boolean ignoreStage = false;

    private int maxBufferLength = 0;

    private boolean firstMessageInit = false;

    /**
     * 是否在完成时直接关闭SSE
     */
    private boolean closeSseOnFinish = true;

    private final List<String> checkWords = new ArrayList<>();

    /**
     * 内容
     */
    private final List<List<StringBuilder>> contents = new ArrayList<>();

    private AiGenerateState(SseEmitter sseEmitter) {
        this.sseEmitter = sseEmitter;
    }

    private BiConsumer<AiGenerateState, DifyResponse> firstMessageFunc;

    private BiConsumer<AiGenerateState, DifyResponse> messageFunc;

    private Consumer<AiGenerateState> onFinishFunc;

    private BiConsumer<AiGenerateState, Throwable> onErrorHandlerFunc;

    private BiFunction<AiGenerateState, String, Serializable> contentFormatFunc = null;

    public static AiGenerateStateBuilder getBuilder(SseEmitter sseEmitter) {
        AiGenerateStateBuilder builder = new AiGenerateStateBuilder();
        builder.state = new AiGenerateState(sseEmitter);
        return builder;
    }

    private void createNewContent(int index) {
        List<StringBuilder> cs = contents.get(index);
        StringBuilder sb = new StringBuilder();
        cs.add(sb);
    }

    public StringBuilder getContentOrCreate(int index) {
        List<StringBuilder> cs = contents.get(index);
        if (cs.isEmpty()) {
            StringBuilder sb = new StringBuilder();
            cs.add(sb);
            return sb;
        }
        return cs.get(cs.size() - 1);
    }

    public String getContentString(int index) {
        StringBuilder content = getContent(index);
        return content == null ? null : content.toString();
    }

    public List<StringBuilder> getContents(int index) {
        return contents.get(index);
    }

    public StringBuilder getContent(int index) {
        List<StringBuilder> cs = contents.get(index);
        if (cs.isEmpty()) {
            return null;
        }
        return cs.get(cs.size() - 1);
    }

    public SseEmitter getSseEmitter() {
        return sseEmitter;
    }

    public int getStage() {
        return stage;
    }

    public AiGenerateTask getTask() {
        return task;
    }

    public void onMessage(DifyResponse response) {
        if (messageFunc != null) {
            messageFunc.accept(this, response);
        }

        if (firstMessageInit) {
            return;
        }
        this.conversationId = response.getConversationId();
        if (firstMessageFunc != null) {
            firstMessageFunc.accept(this, response);
        }
        firstMessageInit = true;
    }

    private void init() {
        configs.forEach(config -> {
            contents.add(new ArrayList<>());
            if (config.getStartWords() == null || config.getStartWords().length == 0) {
                return;
            }
            for (int i = 0; i < config.getStartWords().length; i++) {
                String startWord = config.getStartWords()[i];
                if (StringUtils.isEmpty(startWord)) {
                    continue;
                }
                maxBufferLength = Math.max(maxBufferLength, startWord.length());

                for (int j = 0; j < startWord.length() - 1; j++) {
                    String word = startWord.substring(0, j + 1);
                    if (!checkWords.contains(word)) {
                        checkWords.add(word);
                    }
                }
            }
        });
    }

    private boolean endWithWords(StringBuilder word) {
        for (String w : checkWords) {
            if (endWithWords(word, w)) {
                return true;
            }
        }
        return false;
    }

    private boolean endWithWords(StringBuilder word, String keywords) {
        if (word.length() < keywords.length()) {
            return false;
        }
        // 检查 word 是否以 keywords 结尾
        for (int i = 0; i < keywords.length(); i++) {
            if (word.charAt(word.length() - keywords.length() + i) != keywords.charAt(i)) {
                return false;
            }
        }
        return true;
    }

    public void newWords(String word) {
        if (word == null || word.length() == 0) {
            return;
        }
        // 用于测试
//        System.out.println(">>" + word);
        buffer.append(word);

        if (endWithWords(buffer)) {
            return;
        }

        MatchResult result = matchWords(buffer);
        if (result != null) {
            //命中了起始词
            String preContent = buffer.substring(0, result.index);
            String content = buffer.substring(result.index + result.length);
            AiGenerateStateConfig config = stage == -1 ? null : configs.get(stage);
            if (config != null && !ignoreStage) {
                StringBuilder sb = getContentOrCreate(stage);
                if (preContent.length() > 0) {
                    sb.append(preContent);

                    if (config.getSseEvent() == 2) {
                        //实时发送
                        sendSseEvent(config.getSseTypeName(), preContent);
                    } else if (config.getSseEvent() == 1) {
                        //阶段结束时发送
                        sendSseEvent(config.getSseTypeName(), sb.toString());
                    }
                }
                //当前状态结束
                if (config.getSendWordOnFinish() != null && !StringUtils.isEmpty(result.word)) {
                    sendSseEvent(config.getSseTypeName(), config.getSendWordOnFinish());
                }
            }
            //进入下一个状态
            config = configs.get(result.stage);
            if (config != null && stage >= result.stage) {
                //阶段回退
                switch (config.getStageBack()) {
                    case 0:
                        //忽略当前阶段
                        ignoreStage = true;
                        createNewContent(stage);
                        break;
                    case 1:
                        //创建阶段新的输入
                        createNewContent(stage);
                        ignoreStage = false;
                        break;
                    case 2:
                        //追加到本阶段上一个输入
                        ignoreStage = false;
                        break;
                    default:
                        throw new InngkeServiceException("不支持的 stageBack 类型：" + config.getStageBack());
                }
            } else {
                ignoreStage = false;
                if (stage > -1) {
                    createNewContent(stage);
                }
            }
            stage = result.stage;

            if (!ignoreStage && content.length() > 0) {
                getContentOrCreate(stage).append(content);
                if (config != null && config.getSseEvent() == 2) {
                    sendSseEvent(config.getSseTypeName(), content);
                }
            }
            buffer.delete(0, buffer.length());
            return;
        }

        //没有命中起始词
        if (!ignoreStage && buffer.length() > maxBufferLength) {
            if (stage != -1) {
                getContentOrCreate(stage).append(buffer);
                AiGenerateStateConfig config = configs.get(stage);
                if (config.getSseEvent() == 2) {
                    sendSseEvent(config.getSseTypeName(), buffer.toString());
                }
            }
            buffer.delete(0, buffer.length());
        }
    }

    private void sendSseEvent(String sseTypeName, String content) {
        if (contentFormatFunc != null) {
            Serializable body = contentFormatFunc.apply(this, content);
            CrmUtils.sendSseEvent(sseEmitter, sseTypeName, body);
        } else {
            CrmUtils.sendSseEvent(sseEmitter, sseTypeName, content);
        }
    }

    public void finish() {
        if (stage > -1) {
            AiGenerateStateConfig config = configs.get(stage);
            if (buffer.length() > 0) {
                getContentOrCreate(stage).append(buffer);
            }
            if (!ignoreStage) {
                if (config.getSseEvent() == 1) {
                    sendSseEvent(config.getSseTypeName(), getContent(stage).toString());
                } else if (config.getSseEvent() == 2) {
                    sendSseEvent(config.getSseTypeName(), buffer.toString());
                }
                //当前状态结束
                if (config.getSendWordOnFinish() != null) {
                    sendSseEvent(config.getSseTypeName(), config.getSendWordOnFinish());
                }
            }
        }
        for (List<StringBuilder> contentList : contents) {
            if (CollectionUtils.isEmpty(contentList)) {
                continue;
            }
            for (int i = contentList.size() - 1; i >= 0; i--) {
                if (contentList.get(i).length() == 0) {
                    contentList.remove(i);
                }
            }
        }
        if (closeSseOnFinish) {
            closeSseEmitter();
        }
    }

    public void closeSseEmitter() {
        CrmUtils.complete(sseEmitter);
    }

    private MatchResult matchWords(StringBuilder sb) {
        String bufferString = sb.toString();
        for (int i = 0; i < configs.size(); i++) {
            AiGenerateStateConfig config = configs.get(i);
            for (String keyword : config.getStartWords()) {
                int index = bufferString.indexOf(keyword);
                if (index > -1) {
                    return new MatchResult(i, index, keyword.length(), keyword);
                }
            }
        }
        return null;
    }

    static class MatchResult {
        int stage;
        int index;
        int length;
        String word;

        public MatchResult(int stage, int index, int length, String word) {
            this.stage = stage;
            this.index = index;
            this.length = length;
            this.word = word;
        }
    }

    public Consumer<AiGenerateState> getOnFinishFunc() {
        return onFinishFunc;
    }

    public BiConsumer<AiGenerateState, Throwable> getOnErrorHandlerFunc() {
        return onErrorHandlerFunc;
    }

    public <T> T getExtraObj() {
        return (T) extraObj;
    }

    public void setExtraObj(Object extraObj) {
        this.extraObj = extraObj;
    }

    public String getConversationId() {
        return conversationId;
    }

    public void setContentFormatFunc(BiFunction<AiGenerateState, String, Serializable> contentFormatFunc) {
        this.contentFormatFunc = contentFormatFunc;
    }

    public static class AiGenerateStateBuilder {
        private AiGenerateState state;

        /**
         * 添加状态
         *
         * @param sseEvent         SSE事件 0=不发送 1=完结后发送 2=实时发送
         * @param sseTypeName      SSE类型
         * @param sendWordOnFinish 完结后发送的词
         * @param stageBack        阶段回退 0=忽略当前阶段 1=创建阶段新的输入 2=追加到本阶段上一个输入
         * @param startWords       起始词，支持多个
         */
        public AiGenerateStateBuilder addState(int sseEvent, String sseTypeName, String sendWordOnFinish, int stageBack, String... startWords) {
            state.configs.add(new AiGenerateStateConfig(sseEvent, sseTypeName, sendWordOnFinish, stageBack, startWords));
            return this;
        }

        public AiGenerateState setDefaultStage(int stage) {
            state.stage = stage;
            return state;
        }

        public AiGenerateStateBuilder setOnFinish(Consumer<AiGenerateState> finishFunc) {
            state.onFinishFunc = finishFunc;
            return this;
        }

        public AiGenerateStateBuilder setOnErrorHandlerFunc(BiConsumer<AiGenerateState, Throwable> errorHandlerFunc) {
            state.onErrorHandlerFunc = errorHandlerFunc;
            return this;
        }

        public AiGenerateStateBuilder setTask(AiGenerateTask task) {
            state.task = task;
            return this;
        }

        public AiGenerateStateBuilder setFirstMessageFunc(BiConsumer<AiGenerateState, DifyResponse> firstMessageFunc) {
            state.firstMessageFunc = firstMessageFunc;
            return this;
        }

        public AiGenerateStateBuilder setMessageFunc(BiConsumer<AiGenerateState, DifyResponse> messageFunc) {
            state.messageFunc = messageFunc;
            return this;
        }

        public AiGenerateStateBuilder setContentFormatFunc(BiFunction<AiGenerateState, String, Serializable> contentFormatFunc) {
            state.contentFormatFunc = contentFormatFunc;
            return this;
        }

        public AiGenerateStateBuilder closeSseOnFinish(boolean closeSseOnFinish) {
            state.closeSseOnFinish = closeSseOnFinish;
            return this;
        }

        public AiGenerateState build() {
            state.init();
            return state;
        }
    }
}
