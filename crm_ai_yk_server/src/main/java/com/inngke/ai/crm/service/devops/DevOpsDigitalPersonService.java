package com.inngke.ai.crm.service.devops;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.ai.crm.converter.DigitalPersonConverter;
import com.inngke.ai.crm.db.crm.entity.DigitalPersonTag;
import com.inngke.ai.crm.db.crm.entity.DigitalPersonTemplate;
import com.inngke.ai.crm.db.crm.manager.DigitalPersonTagManager;
import com.inngke.ai.crm.db.crm.manager.DigitalPersonTemplateManager;
import com.inngke.ai.crm.db.crm.manager.TtsConfigManager;
import com.inngke.ai.crm.dto.request.devops.GetTemplateRequest;
import com.inngke.ai.crm.dto.request.digital.person.AddDistalPersonTagRequest;
import com.inngke.ai.crm.dto.request.digital.person.AddDpTemplateRequest;
import com.inngke.ai.crm.dto.request.digital.person.UpdateDistalPersonTagRequest;
import com.inngke.ai.crm.dto.request.digital.person.UpdateDpTemplateRequest;
import com.inngke.ai.crm.dto.response.DpTagDto;
import com.inngke.ai.crm.dto.response.DpTemplateDto;
import com.inngke.common.core.utils.SnowflakeHelper;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class DevOpsDigitalPersonService {

    private static final String OSS_PREFIX = "yk-ai-video";
    private static final String DOWNLOAD_PATH_PREFIX = "tmp/";

    @Autowired
    private DigitalPersonTemplateManager digitalPersonTemplateManager;
    @Autowired
    private TtsConfigManager ttsConfigManager;
    @Autowired
    private DigitalPersonTagManager digitalPersonTagManager;

    public BaseResponse<Boolean> addTemplate(AddDpTemplateRequest request) {
        DigitalPersonTemplate digitalPersonTemplate = DigitalPersonConverter.toDigitalPersonTemplate(request);

        return BaseResponse.success(digitalPersonTemplateManager.save(digitalPersonTemplate));
    }

    public BaseResponse<Boolean> updateTemplate(UpdateDpTemplateRequest request) {
        DigitalPersonTemplate digitalPersonTemplate = DigitalPersonConverter.toDigitalPersonTemplate(request);
        digitalPersonTemplate.setId(request.getId());
        digitalPersonTemplate.setCreateTime(null);

        return BaseResponse.success(digitalPersonTemplateManager.updateById(digitalPersonTemplate));
    }

    public BaseResponse<List<DpTemplateDto>> getTemplateList(GetTemplateRequest request) {
        return BaseResponse.success(
                digitalPersonTemplateManager.list(Wrappers.<DigitalPersonTemplate>query()
                        .eq(Objects.nonNull(request.getOrganizeId()), DigitalPersonTemplate.ORGANIZE_ID, request.getOrganizeId())
                        .apply(Objects.nonNull(request.getTag()), "json_contains(tags,{0})", Optional.ofNullable(request.getTag()).map(Object::toString).orElse(""))
                        .eq(Objects.nonNull(request.getType()), DigitalPersonTemplate.TYPE, request.getType())
                        .orderByDesc(DigitalPersonTemplate.SORT, DigitalPersonTemplate.ID)
                ).stream().map(DigitalPersonConverter::toDpTemplateDto).collect(Collectors.toList())
        );
    }

    public BaseResponse<List<DpTagDto>> getDpTagList() {
        return BaseResponse.success(
                digitalPersonTagManager.list(Wrappers.<DigitalPersonTag>query().orderByDesc(DigitalPersonTag.SORT)).stream()
                        .map(DigitalPersonConverter::toDpTagDto).collect(Collectors.toList())
        );
    }

    public BaseResponse<Boolean> deleteTemplate(Long id) {
        return BaseResponse.success(digitalPersonTemplateManager.removeById(id));
    }

    public BaseResponse<Boolean> addDpTag(AddDistalPersonTagRequest request) {
        DigitalPersonTag digitalPersonTag = new DigitalPersonTag();
        digitalPersonTag.setName(request.getName());
        digitalPersonTag.setSort(request.getSort());
        digitalPersonTag.setCreateTime(LocalDateTime.now());
        digitalPersonTag.setId(SnowflakeHelper.getId());
        return BaseResponse.success(
                digitalPersonTagManager.save(digitalPersonTag)
        );
    }

    public BaseResponse<Boolean> updateDpTag(UpdateDistalPersonTagRequest request) {
        DigitalPersonTag digitalPersonTag = new DigitalPersonTag();
        digitalPersonTag.setId(request.getId());
        digitalPersonTag.setName(request.getName());
        digitalPersonTag.setSort(request.getSort());

        return BaseResponse.success(
                digitalPersonTagManager.updateById(digitalPersonTag)
        );
    }

    public BaseResponse<Boolean> deleteDpTag(Long id) {
        return BaseResponse.success(
                digitalPersonTagManager.removeById(id)
        );
    }
}
