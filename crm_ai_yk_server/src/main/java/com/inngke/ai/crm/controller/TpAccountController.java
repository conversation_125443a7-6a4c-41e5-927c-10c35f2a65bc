package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.service.TpAccountService;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @chapter 三方帐号
 * @since 2023-08-29 19:47
 **/
@RestController
@RequestMapping("/api/ai/tp-account")
public class TpAccountController {

    @Resource
    private TpAccountService tpAccountService;

    /**
     * 刷新抖音账户授权
     */
    @PostMapping("/dou-yin/{openId}/refresh")
    public BaseResponse<Boolean> refreshDouYinAccount(@PathVariable String openId){
        return BaseResponse.success(tpAccountService.refreshDouYinAccount(openId));
    }
}
