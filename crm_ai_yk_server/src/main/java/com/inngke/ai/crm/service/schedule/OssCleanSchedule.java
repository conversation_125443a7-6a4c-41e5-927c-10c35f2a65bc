package com.inngke.ai.crm.service.schedule;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.OSSObjectSummary;
import com.aliyun.oss.model.ObjectListing;
import com.inngke.ai.crm.core.CrmServiceConsts;
import com.inngke.ai.crm.core.config.AliAccountConfig;
import com.inngke.common.core.utils.EnvUtils;
import com.inngke.common.dto.Lock;
import com.inngke.common.enums.EnvEnum;
import com.inngke.common.service.LockService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.Map;

@Component
public class OssCleanSchedule {
    private static final int A_DAY = 24 * 60 * 60 * 1000;
    private static final Logger logger = LoggerFactory.getLogger(OssCleanSchedule.class);
    @Autowired
    private AliAccountConfig aliAccountConfig;

    @Autowired
    private LockService lockService;

    private static final String BUCKET_NAME = "yk-ai-video";

    private static final Map<String, Integer> DIRECTORY_MAP = Map.of(
            "tmp/", 1,
            "video/jianying/", 10
    );

    /**
     * 每天凌晨0点31分清理oss上的临时文件
     */
    @Scheduled(cron = "31 0 0 * * *")
    public void clean() {
        if (EnvUtils.getEnv() != EnvEnum.PROD) {
            return;
        }
        Lock lock = lockService.getLock(CrmServiceConsts.CACHE_LOCK_KEY_PRE + "oss_clean", 60, false);
        if (lock == null) {
            return;
        }

        OSS ossClient = ossClient();
        DIRECTORY_MAP.forEach((directory, days) -> {
            ObjectListing objectListing = ossClient.listObjects(BUCKET_NAME, directory);
            Date now = new Date();

            for (OSSObjectSummary objectSummary : objectListing.getObjectSummaries()) {
                Date lastModified = objectSummary.getLastModified();
                if (now.getTime() - lastModified.getTime() > days * A_DAY) {
                    ossClient.deleteObject(BUCKET_NAME, objectSummary.getKey());
                    logger.info("删除: {}, 最后修改时间:{}", objectSummary.getKey(), lastModified);
                }
            }
        });
    }

    private OSS ossClient() {
        String endpoint = "oss-cn-heyuan.aliyuncs.com";
        String accessKeyId = aliAccountConfig.getAccessKey();
        String accessKeySecret = aliAccountConfig.getAccessKeySecret();
        return new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
    }
}
