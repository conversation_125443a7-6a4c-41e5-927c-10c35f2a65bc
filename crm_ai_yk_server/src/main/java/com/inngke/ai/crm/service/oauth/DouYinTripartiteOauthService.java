package com.inngke.ai.crm.service.oauth;

import com.inngke.ai.crm.core.config.AiGcConfig;
import com.inngke.ai.crm.core.util.DouYinUtil;
import com.inngke.ai.crm.db.crm.manager.AppConfigManager;
import com.inngke.ai.crm.dto.enums.AppConfigCodeEnum;
import com.inngke.ai.crm.dto.enums.TripartiteEnum;
import com.inngke.ai.crm.service.CrmUserService;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.JsonService;
import com.inngke.common.wx.rpc.dto.response.mp.SessionResponse;
import com.inngke.ip.ai.douyin.api.DouYinAppsApi;
import com.inngke.ip.ai.douyin.dto.reqeust.Code2SessionRequest;
import com.inngke.ip.ai.douyin.dto.response.DouYinBaseResponse;
import com.inngke.ip.ai.douyin.dto.response.SessionDto;
import com.inngke.ip.ai.douyin.event.DouYinPhoneInfoDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class DouYinTripartiteOauthService implements TripartiteOauthService {

    @Autowired
    private DouYinAppsApi douYinAppsApi;
    @Autowired
    private AppConfigManager appConfigManager;
    @Autowired
    private AiGcConfig aiGcConfig;
    @Autowired
    CrmUserService crmUserService;
    @Autowired
    JsonService jsonService;


    @Override
    public TripartiteEnum getTripartite() {
        return TripartiteEnum.DOU_YIN;
    }

    @Override
    public SessionResponse code2Session(String code) {

        Map<String, String> appConfig = appConfigManager.getValueByCodeList(
                AppConfigCodeEnum.DOU_YIN_MP_APP_ID.getCode(), AppConfigCodeEnum.DOU_YIN_MP_APP_SECRET.getCode()
        );
        Code2SessionRequest request = new Code2SessionRequest();
        request.setAppId(appConfig.get(AppConfigCodeEnum.DOU_YIN_MP_APP_ID.getCode()));
        request.setSecret(appConfig.get(AppConfigCodeEnum.DOU_YIN_MP_APP_SECRET.getCode()));
        request.setCode(code);

        DouYinBaseResponse<SessionDto> response = douYinAppsApi.code2Session(request);

        if (DouYinBaseResponse.dataIsNull(response)){
            return null;
        }

        SessionDto data = response.getData();

        SessionResponse sessionResponse = new SessionResponse();
        sessionResponse.setSessionKey(data.getSessionKey());
        sessionResponse.setOpenId(data.getOpenid());
        sessionResponse.setUnionId(data.getUnionid());

        return sessionResponse;
    }

    @Override
    public String getPhoneNumber(String code, Long userId, String iv, String encryptedData) {
        return getMobileByEncryptedData(userId, iv, encryptedData);
    }

    private String getMobileByEncryptedData(Long userId, String iv, String encryptedData) {

        if (org.springframework.util.StringUtils.isEmpty(encryptedData) && org.springframework.util.StringUtils.isEmpty(iv)) {
            throw new InngkeServiceException("获取手机号参数不正确");
        }

        Integer bid = aiGcConfig.getBid();
        String sessionKey = crmUserService.getSessionKey(bid, userId, TripartiteEnum.DOU_YIN.getCode());
        String result = DouYinUtil.decrypt(encryptedData, sessionKey, iv);

        if (org.springframework.util.StringUtils.isEmpty(result)) {
            throw new InngkeServiceException("获取手机号失败");
        }

        DouYinPhoneInfoDto douYinPhoneInfoDto = jsonService.toObject(result, DouYinPhoneInfoDto.class);
        return douYinPhoneInfoDto.getPhoneNumber();
    }

}
