package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.dto.request.CrmQueryOrderRequest;
import com.inngke.ai.crm.dto.request.WxPrepayRequest;
import com.inngke.ai.crm.dto.request.WxRefundRequest;
import com.inngke.ai.crm.dto.response.CrmQueryOrderDto;
import com.inngke.ai.crm.dto.response.WxPayNotifyDto;
import com.inngke.ai.crm.dto.response.WxPrePayDto;
import com.inngke.ai.crm.service.CrmWxPayService;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;
import com.wechat.pay.java.core.notification.RequestParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @chapter AI
 * @section 微信支付
 * @since 2023-09-12 09:29
 **/
@RestController
@RequestMapping("/api/ai/wx-pay")
public class CrmWxPayController {

    @Autowired
    private CrmWxPayService crmWxPayService;

    /**
     * 微信JSAPI下单
     */
    @PostMapping("/prepay")
    public BaseResponse<WxPrePayDto> prepay(@RequestAttribute JwtPayload jwtPayload,
                                            @RequestBody WxPrepayRequest request) {
        request.setUserId(jwtPayload.getCid());
        return crmWxPayService.prepay(request);
    }

//    @PostMapping("/refund")
    public BaseResponse<Boolean> refund(@RequestAttribute JwtPayload jwtPayload,
                                        @RequestBody WxRefundRequest request) {
        request.setUserId(jwtPayload.getCid());
        return crmWxPayService.refund(request);
    }

    /**
     * 查询订单状态
     */
    @GetMapping
    public BaseResponse<CrmQueryOrderDto> queryOrder(CrmQueryOrderRequest request) {
        return crmWxPayService.queryOrder(request);
    }

    /**
     * 微信支付回调通知
     */
    @PostMapping("pay-notify")
    public WxPayNotifyDto payNotify(HttpServletRequest request) throws IOException {
        // 构造 RequestParam
        RequestParam requestParam = requestParam(request);
        // 处理逻辑
        return crmWxPayService.payNotify(requestParam);
    }

    /**
     * 微信退款回调通知
     */
//    @PostMapping("refund-notify")
    public WxPayNotifyDto refundNotify(HttpServletRequest request) throws IOException {
        // 构造 RequestParam
        RequestParam requestParam = requestParam(request);
        // 处理逻辑
        return crmWxPayService.refundNotify(requestParam);
    }

    private RequestParam requestParam(HttpServletRequest request) throws IOException {
        String wechatPaySerial = request.getHeader("Wechatpay-Serial");
        String nonce = request.getHeader("Wechatpay-Nonce");
        String wechatSignature = request.getHeader("Wechatpay-Signature");
        String wechatTimestamp = request.getHeader("Wechatpay-Timestamp");
        // HTTP 请求体 body
        String requestBody = getStreamAsString(request.getInputStream());
        // 构造 RequestParam
        return new RequestParam.Builder()
                .serialNumber(wechatPaySerial)
                .nonce(nonce)
                .signature(wechatSignature)
                .timestamp(wechatTimestamp)
                .body(requestBody)
                .build();
    }

    /**
     * 获取http输入流
     */
    public static String getStreamAsString(InputStream stream) throws IOException {
        try {
            Reader reader = new InputStreamReader(stream, StandardCharsets.UTF_8);
            StringBuilder response = new StringBuilder();
            final char[] buff = new char[1024];
            int read = 0;
            while ((read = reader.read(buff)) > 0) {
                response.append(buff, 0, read);
            }
            return response.toString();
        } finally {
            if (stream != null) {
                stream.close();
            }
        }
    }


}
