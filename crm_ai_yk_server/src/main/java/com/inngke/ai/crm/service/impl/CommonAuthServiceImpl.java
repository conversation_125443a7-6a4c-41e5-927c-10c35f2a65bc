package com.inngke.ai.crm.service.impl;

import com.inngke.ai.crm.client.WxMpQrServiceForAi;
import com.inngke.ai.crm.core.config.AiGcConfig;
import com.inngke.ai.crm.dto.response.CrmPcConfigDto;
import com.inngke.ai.crm.dto.response.CrmPcConfigStorageDto;
import com.inngke.ai.crm.service.CommonAuthService;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.service.JsonService;
import com.inngke.ip.common.dto.request.GenMpQrRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Objects;
import java.util.Optional;

import static com.inngke.ip.common.consts.GenQrCodeHandleTypeConsts.BASE64_HANDLE_TYPE;
import static com.inngke.ip.common.consts.GenQrCodeHandleTypeConsts.DEFAULT_HANDLE_TYPE;

@Service
@Slf4j
public class CommonAuthServiceImpl implements CommonAuthService {
    @Autowired
    private JsonService jsonService;

    @Autowired
    private WxMpQrServiceForAi wxMpQrServiceForAi;

    @Autowired
    private AiGcConfig aiGcConfig;

    @Autowired
    private RedisTemplate redisTemplate;

    @Override
    public BaseResponse<String> genQrCode(GenMpQrRequest request) {
        request.setBid(aiGcConfig.getBid());
        request.setHandleType((Objects.isNull(request.getHandleType()) ? DEFAULT_HANDLE_TYPE : request.getHandleType()));
        String gen = wxMpQrServiceForAi.gen(request);
        if (DEFAULT_HANDLE_TYPE.equals(request.getHandleType())) {
            return BaseResponse.success(gen.replace("https://static.inngke.com/", "https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/"));
        }
        return BaseResponse.success(gen);
    }

    @Override
    public BaseResponse<CrmPcConfigDto> pcConfig() {
        CrmPcConfigDto result = new CrmPcConfigDto();

        CrmPcConfigStorageDto storageDto = new CrmPcConfigStorageDto();
        storageDto.setDomain(aiGcConfig.getDomain());
        storageDto.setPlatform(aiGcConfig.getPlatform());
        result.setStorage(storageDto);

        String version = (String) redisTemplate.opsForHash().get(InngkeAppConst.PUBLIC_CACHE_KEY + "appVersion", "cloudShopAI-PC");
        result.setWebVersion(Optional.ofNullable(version).orElse("1.0.0"));
        String apiVersion = Optional.ofNullable(System.getenv("CRM_VERSION")).orElse("1.0.0");
        result.setApiVersion(apiVersion);
        return BaseResponse.success(result);
    }

}
