package com.inngke.ai.crm.converter;

import com.inngke.ai.crm.db.crm.entity.ScriptContent;
import com.inngke.ai.crm.db.crm.entity.ScriptType;
import com.inngke.ai.crm.dto.request.script.ScriptContentRequest;
import com.inngke.ai.crm.dto.request.script.ScriptTypeSaveRequest;
import com.inngke.ai.crm.dto.response.script.*;
import com.inngke.common.utils.DateTimeUtils;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/4/3 10:26
 */
public class ScriptConverter {

    public static ScriptTypeListResponse toScriptTypeListResponse(ScriptType scriptType) {
        ScriptTypeListResponse scriptTypeListResponse = new ScriptTypeListResponse();
        scriptTypeListResponse.setId(scriptType.getId());
        scriptTypeListResponse.setName(scriptType.getName());
        return scriptTypeListResponse;
    }

    public static ScriptContentTitleDto toScriptContentTitleDto(ScriptContent scriptContent) {
        ScriptContentTitleDto scriptContentTitleDto = new ScriptContentTitleDto();
        scriptContentTitleDto.setId(scriptContent.getId());
        scriptContentTitleDto.setTitle(scriptContent.getTitle());
        scriptContentTitleDto.setContentLength(scriptContent.getContentLength());
        scriptContentTitleDto.setTypeId(scriptContent.getScriptTypeId());
        return scriptContentTitleDto;
    }

    public static ScriptContentDto toScriptContentDto(ScriptContent scriptContent) {
        ScriptContentDto scriptContentDto = new ScriptContentDto();
        scriptContentDto.setContent(scriptContent.getContent());
        return scriptContentDto;
    }

    public static ScriptContentDetailResponse toScriptContentDetailResponse(ScriptContent scriptContent) {
        ScriptContentDetailResponse scriptContentDetailResponse = new ScriptContentDetailResponse();
        BeanUtils.copyProperties(scriptContent, scriptContentDetailResponse);
        scriptContentDetailResponse.setCreateTime(DateTimeUtils.getMilli(scriptContent.getCreateTime()));
        scriptContentDetailResponse.setUpdateTime(DateTimeUtils.getMilli(scriptContent.getUpdateTime()));
        return scriptContentDetailResponse;
    }

    public static ScriptContent toScriptContent(ScriptContentRequest scriptContentRequest) {
        ScriptContent scriptContent = new ScriptContent();
        scriptContent.setScriptTypeId(scriptContentRequest.getTypeId());
        scriptContent.setTitle(scriptContentRequest.getTitle());
        scriptContent.setContent(scriptContentRequest.getContent());
        scriptContent.setContentLength(scriptContentRequest.getContent().length());
        scriptContent.setCode(scriptContentRequest.getCode());
        scriptContent.setCreateTime(DateTimeUtils.MillisToLocalDateTime(System.currentTimeMillis()));
        scriptContent.setCustomSort(scriptContentRequest.getCustomSort());
        return scriptContent;
    }

    public static ScriptType toScriptType(ScriptTypeSaveRequest scriptTypeSaveRequest) {
        ScriptType scriptType = new ScriptType();
        scriptType.setCode(scriptTypeSaveRequest.getCode());
        scriptType.setName(scriptTypeSaveRequest.getName());
        scriptType.setCreateTime(DateTimeUtils.MillisToLocalDateTime(System.currentTimeMillis()));
        return scriptType;
    }

    public static List<ScriptTypeTreeDto> toScriptTypeTreeDtoList(List<ScriptType> scriptTypeList) {

        return scriptTypeList.stream().map(scriptType -> {
            ScriptTypeTreeDto scriptTypeTreeDto = new ScriptTypeTreeDto();
            scriptTypeTreeDto.setId(scriptType.getId());
            scriptTypeTreeDto.setName(scriptType.getName());
            scriptTypeTreeDto.setCode(scriptType.getCode());
            scriptTypeTreeDto.setCustomSort(scriptType.getCustomSort());
            scriptTypeTreeDto.setUpdateTime(DateTimeUtils.getMilli(scriptType.getUpdateTime()));
            scriptTypeTreeDto.setCreateTime(DateTimeUtils.getMilli(scriptType.getUpdateTime()));
            scriptTypeTreeDto.setChildren(null);
            return scriptTypeTreeDto;
        }).collect(Collectors.toList());

    }

    public static List<ScriptContentDetailResponse> toScriptContentDetailResponseList(List<ScriptContent> scriptContents) {

        return scriptContents.stream().map(scriptContent -> {
            ScriptContentDetailResponse scriptContentDetailResponse = new ScriptContentDetailResponse();
            BeanUtils.copyProperties(scriptContent, scriptContentDetailResponse);
            scriptContentDetailResponse.setTypeId(scriptContent.getScriptTypeId());
            scriptContentDetailResponse.setUpdateTime(DateTimeUtils.getMilli(scriptContent.getUpdateTime()));
            scriptContentDetailResponse.setCreateTime(DateTimeUtils.getMilli(scriptContent.getCreateTime()));
            return scriptContentDetailResponse;
        }).collect(Collectors.toList());

    }

}
