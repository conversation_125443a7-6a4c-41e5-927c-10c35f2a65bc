package com.inngke.ai.crm.service.impl;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.inngke.ai.crm.core.CrmServiceConsts;
import com.inngke.ai.crm.db.crm.entity.VideoMaterialDir;
import com.inngke.ai.crm.db.crm.manager.VideoMaterialDirManager;
import com.inngke.ai.crm.dto.response.material.MaterialDirTreeDto;
import com.inngke.common.cache.service.impl.BaseDupVersionCacheFactory;
import com.inngke.common.core.InngkeAppConst;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

@Service
public class MaterialDirCacheFactory extends BaseDupVersionCacheFactory<MaterialDirTreeDto, MaterialDirCacheFactory.MaterialDirTreeCache> {

    /**
     * 目录树版本号
     */
    private static final String MATERIAL_DIR_TREE_VERSION = CrmServiceConsts.APP_ID + ":materialDirTreeVersion:";
    private static final String MATERIAL_DIR_TREE_DATA = CrmServiceConsts.APP_ID + ":materialDirTree:";
    public static final Integer GLOBAL_ORGANIZE_ID = 0;

    @Autowired
    private VideoMaterialDirManager videoMaterialDirManager;

    @Scheduled(fixedRate = 60000)
    public void refresh() {
        this.incCacheVersion(GLOBAL_ORGANIZE_ID);
    }

    @Override
    public String getCacheVersionKey(int organizeId) {
        return MATERIAL_DIR_TREE_VERSION + GLOBAL_ORGANIZE_ID;
    }

    @Override
    public String getCacheDataKey(int organizeId, Long version) {
        return MATERIAL_DIR_TREE_DATA + GLOBAL_ORGANIZE_ID + InngkeAppConst.UNDERLINE_STR + version;
    }

    @Override
    public MaterialDirCacheFactory.MaterialDirTreeCache newInstance(int bid, Long version) {
        return new MaterialDirTreeCache(GLOBAL_ORGANIZE_ID, version);
    }


    public class MaterialDirTreeCache extends BaseDupVersionCacheFactory.BaseCache {

        private final Map<Long, MaterialDirTreeDto> materialDirMap = Maps.newHashMap();

        private final Map<Long, List<MaterialDirTreeDto>> parentMap = Maps.newHashMap();

        protected MaterialDirTreeCache(int bid, Long version) {
            super(bid, version);
        }

        @Override
        protected void buildFromCacheStr(String materialDirTreeStr) {
            List<MaterialDirTreeDto> root = jsonService.toObjectList(materialDirTreeStr, MaterialDirTreeDto.class);
            if (CollectionUtils.isEmpty(root)) {
                return;
            }
            rebuildFromCache(root);
        }

        private void rebuildFromCache(List<MaterialDirTreeDto> materialDirTreeList) {
            if (CollectionUtils.isEmpty(materialDirTreeList)) {
                return;
            }
            materialDirTreeList.forEach(dept -> {
                parentMap.computeIfAbsent(dept.getParentId(), pid -> Lists.newArrayList()).add(dept);
                materialDirMap.put(dept.getId(), dept);
                rebuildFromCache(dept.getChildren());
            });
        }

        @Override
        protected void build() {
            // 查询所有目录
            List<VideoMaterialDir> list = videoMaterialDirManager.list();

            list.forEach(materialDir -> {
                List<MaterialDirTreeDto> materialDirTreeList = parentMap.computeIfAbsent(materialDir.getPid(), k -> new ArrayList<>());
                MaterialDirTreeDto dept = treeTransDto(materialDir);
                materialDirTreeList.add(dept);
                materialDirMap.put(dept.getId(), dept);
            });

            // 递归调用子类
            List<MaterialDirTreeDto> roots = getRoots();
            buildChildren(roots, parentMap, 0);

            //排序
            sort(roots);
        }

        private void buildChildren(List<MaterialDirTreeDto> list, Map<Long, List<MaterialDirTreeDto>> parentMap, Integer level) {
            if (list == null || list.isEmpty()) {
                return;
            }
            for (MaterialDirTreeDto dto : list) {
                List<MaterialDirTreeDto> materialDirs = parentMap.get(dto.getId());
                dto.setChildren(materialDirs);
                buildChildren(materialDirs, parentMap, level + 1);
            }
        }

        private void sort(List<MaterialDirTreeDto> materialDirList) {
            if (CollectionUtils.isEmpty(materialDirList)) {
                return;
            }
            materialDirList.sort(Comparator.comparingLong(MaterialDirTreeDto::getId));
            materialDirList.forEach(dept -> sort(dept.getChildren()));
        }

        public List<MaterialDirTreeDto> getRoots() {
            return parentMap.get(0L) == null ? new ArrayList<>() : parentMap.get(0L);
        }

        private MaterialDirTreeDto treeTransDto(VideoMaterialDir materialDir) {
            MaterialDirTreeDto materialDirTreeDto = new MaterialDirTreeDto();
            materialDirTreeDto.setId(materialDir.getId());
            materialDirTreeDto.setCategoryIds(
                    StringUtils.isNotBlank(materialDir.getCategoryIds()) ?
                            Splitter.on(InngkeAppConst.COMMA_STR).splitToList(materialDir.getCategoryIds()) : Lists.newArrayList()
            );
            materialDirTreeDto.setFullPath(materialDir.getFullPath());
            materialDirTreeDto.setName(materialDir.getName());
            materialDirTreeDto.setEnv(materialDir.getEnv());
            materialDirTreeDto.setOrganizeId(materialDir.getOrganizeId());
            materialDirTreeDto.setParentId(materialDir.getPid());
            materialDirTreeDto.setChildren(Lists.newArrayList());
            return materialDirTreeDto;
        }

        @Override
        protected String getCacheDataStr() {
            return null;
        }
    }
}
