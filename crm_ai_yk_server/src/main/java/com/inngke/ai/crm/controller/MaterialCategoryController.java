package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.dto.request.material.AddCategoryRequest;
import com.inngke.ai.crm.dto.request.material.EditCategoryRequest;
import com.inngke.ai.crm.dto.response.material.MaterialCategoryItemDto;
import com.inngke.ai.crm.service.MaterialCategoryService;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @chapter 素材模块
 * @section 素材分类
 */
@RestController
@RequestMapping("/api/ai/material-category")
public class MaterialCategoryController {

    @Autowired
    private MaterialCategoryService materialCategoryService;

    /**
     * 获取分类列表
     */
    @GetMapping
    public BaseResponse<List<MaterialCategoryItemDto>> listCategory(
            @RequestAttribute JwtPayload jwtPayload, @RequestParam String type) {
        return BaseResponse.success(materialCategoryService.listCategory(jwtPayload, type));
    }

    /**
     * 添加分类
     *
     * @param request 添加分类请求
     * @return 添加结果
     */
    @PostMapping
    public BaseResponse<Long> addCategory(
            @RequestAttribute JwtPayload jwtPayload,
            @Validated @RequestBody AddCategoryRequest request) {
        return BaseResponse.success(materialCategoryService.addCategory(jwtPayload, request));
    }

    /**
     * 编辑分类
     *
     * @param request 编辑分类请求
     * @return 编辑结果
     */
    @PutMapping("/{id:\\d+}")
    public BaseResponse<Long> addCategory(
            @RequestAttribute JwtPayload jwtPayload,
            @PathVariable Long id,
            @Validated @RequestBody EditCategoryRequest request) {
        request.setId(id);
        return BaseResponse.success(materialCategoryService.editCategory(jwtPayload, request));
    }

    /**
     * 删除分类
     *
     * @param id 分类ID
     * @return 删除结果
     */
    @DeleteMapping("/{id:\\d+}")
    public BaseResponse<Boolean> deleteCategory(
            @RequestAttribute JwtPayload jwtPayload,
            @PathVariable Long id) {
        return BaseResponse.success(materialCategoryService.deleteCategory(jwtPayload, id));
    }


    /**
     * 获取分类全路径
     */
    @GetMapping("/full-path/{id:\\d+}")
    public BaseResponse<List<MaterialCategoryItemDto>> getFullPath( @PathVariable Long id){
        return BaseResponse.success(materialCategoryService.getFullPath(id));
    }
}
