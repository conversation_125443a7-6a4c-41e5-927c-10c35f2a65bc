package com.inngke.ai.crm.service.qunfeng.init;

import com.inngke.ai.crm.db.crm.entity.*;
import com.inngke.ip.ai.qunfeng.dto.QunFengUserDto;
import lombok.Data;

import java.util.List;

@Data
public class InitContext {

    /**
     * 群峰user信息
     */
    private QunFengUserDto qunFengUser;

    /**
     * 群峰订单
     */
    private QunFengOrder qunFengOrder;

    /**
     * 群峰商品
     */
    private QunFengProduct qunFengProduct;

    /**
     * 积分商品
     */
    private CoinProduct initCoinProduct;

    /**
     * 企业
     */
    private Organize organize;

    /**
     * 用户
     */
    private User user;

    /**
     * 员工
     */
    private Staff staff;

    /**
     * 部门
     */
    private Department department;

    /**
     * 订单
     */
    private CoinOrder coinOrder;

    /**
     * vip
     */
    private UserVip userVip;

    private List<UserVip> userVips;

    /**
     * 积分
     */
    private List<Coin> coins;

    private Coin coin;

    /**
     * 积分日志
     */
    private CoinLog coinLog;
}