package com.inngke.ai.crm.api.xhs.dto;

import com.inngke.ai.crm.api.browser.dto.BrowserTaskRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-12-25 14:47
 **/
public class XiaoHongShuUserInfoRequest extends BrowserTaskRequest {

    private List<XiaoHongShuCookiesDto> cookies;

    private String mobile;

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public List<XiaoHongShuCookiesDto> getCookies() {
        return cookies;
    }

    public void setCookies(List<XiaoHongShuCookiesDto> cookies) {
        this.cookies = cookies;
    }
}
