package com.inngke.ai.crm.dto.request.video;

import com.inngke.common.dto.request.BasePageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class VideoMaterialQueryRequest extends BasePageRequest {
    /**
     * 筛选关键字
     *
     * @demo 环保引领
     */
    private String keyword;

    /**
     * 来源 mp,pc
     */
    private String source;

    /**
     * 图片地址
     */
    private String imageUrl;

    /**
     * 任务列表
     */
    private Long taskId;

    /**
     * 对应工程的脚本索引(用于搜索去重)
     */
    private Integer scriptIndex;

    /**
     * 草稿类型：1=裂变混剪 2=分镜脚本
     */
    private Integer draftType;

    /**
     * 分类筛选
     */
    private List<Long> categoryIds;

    /**
     * 镜头时长，单位：毫秒
     *
     * @demo 2000
     */
    private Integer duration;

    private Integer pageNo = 1;

    private Integer pageSize = 50;

    /**
     * 用户ID，如果为空，则尝试从jwt中获取
     */
    private Long userId;

    /**
     * 是否竖屏 1:竖屏 2:横屏 3:不指定
     */
    private Integer vertical;

    /**
     * 创建时间开始，格式：yyyy-MM-dd HH:mm:ss
     *
     * @demo 2020-11-11 11:11:00
     */
    private String createTimeStart;

    /**
     * 创建时间结束，格式：yyyy-MM-dd HH:mm:ss
     *
     * @demo 2020-11-12 11:11:00
     */
    private String createTimeEnd;
}
