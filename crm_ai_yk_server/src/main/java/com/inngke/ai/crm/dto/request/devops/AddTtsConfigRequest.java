package com.inngke.ai.crm.dto.request.devops;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 新增TTS配置请求
 */
@Data
public class AddTtsConfigRequest implements Serializable {

    /**
     * 展示名称
     */
    @NotBlank(message = "展示名称不能为空")
    private String title;

    /**
     * 企业id
     */
    @NotNull(message = "企业ID不能为空")
    private Long organizeId;

    /**
     * 平台：1=Azure 2=火山云
     */
    @NotNull(message = "平台不能为空")
    private Integer platform;

    /**
     * 性别：1=女性 2=男性
     */
    @NotNull(message = "性别不能为空")
    private Integer gender;

    /**
     * 音色/角色
     */
    @NotBlank(message = "音色不能为空")
    private String voiceType;

    /**
     * 语速，默认为1.0
     */
    private Double speedRatio = 1.0;

    /**
     * 音量，默认为1.0
     */
    private Double volumeRatio = 1.0;

    /**
     * 音高，默认为1.0
     */
    private Double pitchRatio = 1.0;

    /**
     * 排序值，越大越前
     */
    private Integer sortOrder = 1000;

    /**
     * 图片URL地址
     */
    private String imageUrl;
}