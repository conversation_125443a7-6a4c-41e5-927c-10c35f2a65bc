package com.inngke.ai.crm.service.devops;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.inngke.ai.crm.db.crm.entity.Category;
import com.inngke.ai.crm.db.crm.entity.JianyingResource;
import com.inngke.ai.crm.db.crm.entity.JianyingResourceScene;
import com.inngke.ai.crm.db.crm.manager.CategoryManager;
import com.inngke.ai.crm.db.crm.manager.JianyingResourceManager;
import com.inngke.ai.crm.db.crm.manager.JianyingResourceSceneManager;
import com.inngke.ai.crm.dto.request.devops.*;
import com.inngke.ai.crm.dto.response.common.CategoryStyleExtDataDto;
import com.inngke.ai.crm.dto.response.devops.CategoryNode;
import com.inngke.ai.crm.dto.response.devops.JianYingResourceDto;
import com.inngke.ai.crm.dto.response.devops.JianYingResourceStyleDto;
import com.inngke.ai.crm.dto.response.devops.JianyingResourceListDto;
import com.inngke.ai.crm.dto.response.video.JianyingResourceSimpleDto;
import com.inngke.ai.dto.config.JyResourceSceneConfig;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.SnowflakeHelper;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.utils.JsonUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


@Service
public class DevOpsJianYingService {
    private static final Splitter COMMA_SPLITTER = Splitter.on(InngkeAppConst.COMMA_STR);
    private static final Joiner COMMA_JOINER = Joiner.on(InngkeAppConst.COMMA_STR);

    @Autowired
    private JianyingResourceSceneManager jianyingResourceSceneManager;

    @Autowired
    private JianyingResourceManager jianyingResourceManager;

    @Autowired
    private CategoryManager categoryManager;

    public BaseResponse<List<JianYingResourceDto>> getResourceSceneList(Long style) {
        return BaseResponse.success(
                jianyingResourceSceneManager.list(
                        Wrappers.<JianyingResourceScene>query()
                                .eq(JianyingResourceScene.STATUS, 1)
                                .eq(JianyingResourceScene.STYLE,style)
                ).stream().map(this::convertResourceSceneToDto).collect(Collectors.toList())
        );
    }

    public BaseResponse<Boolean> updateResourceScene(UpdateJingYingResourceRequest request) {
        JianyingResourceScene scene = new JianyingResourceScene();
        scene.setId(request.getId());
        scene.setTitle(request.getTitle());
        scene.setStyle(request.getStyle());
        scene.setKeywordType(request.getKeywordType());
        scene.setResourceConfig(request.getResourceSceneConfig());
        if (Objects.isNull(scene.getId())) {
            scene.setId(SnowflakeHelper.getId());
            scene.setCreateTime(LocalDateTime.now());
            jianyingResourceSceneManager.save(scene);
        } else {
            jianyingResourceSceneManager.updateById(scene);
        }
        return BaseResponse.success(true);
    }

    public BaseResponse<Boolean> deleteResourceScene(Long id) {
        return BaseResponse.success(jianyingResourceSceneManager.update(
                Wrappers.<JianyingResourceScene>update()
                        .set(JianyingResourceScene.STATUS, -1)
                        .eq(JianyingResourceScene.ID, id)
        ));
    }

    private JianYingResourceDto convertResourceSceneToDto(JianyingResourceScene scene) {
        JianYingResourceDto dto = new JianYingResourceDto();
        dto.setId(scene.getId());
        dto.setTitle(scene.getTitle());
        dto.setKeywordType(scene.getKeywordType());
        dto.setStyle(scene.getStyle());
        dto.setResourceSceneConfig(scene.getResourceConfig());
        return dto;
    }

    public BaseResponse<Boolean> updateResource(UpdateJianyingResourceRequest request) {
        // 参数校验
        if (request.getId() == null) {
            throw new InngkeServiceException("ID不能为空");
        }

        // 检查资源是否存在
        JianyingResource resource = jianyingResourceManager.getById(request.getId());
        if (resource == null) {
            throw new InngkeServiceException("资源不存在");
        }

        // 如果提供了styles，验证所有分类ID是否存在
        if (!StringUtils.isEmpty(request.getStyles())) {
            List<Long> categoryIds = Arrays.stream(request.getStyles().split(","))
                    .map(Long::parseLong)
                    .collect(Collectors.toList());

            List<Category> categories = categoryManager.list(
                    Wrappers.<Category>query()
                            .eq(Category.TYPE, "subtitle_style")
                            .in(Category.ID, categoryIds)
            );

            if (categories.size() != categoryIds.size()) {
                throw new InngkeServiceException("存在无效的风格分类ID");
            }
        }

        // 更新资源
        JianyingResource updateResource = new JianyingResource();
        updateResource.setId(request.getId());
        updateResource.setStyles(request.getStyles());
        updateResource.setName(request.getName());
        updateResource.setDemoUrl(request.getDemoUrl());
        updateResource.setMaterialConfig(request.getMaterialConfig());
        updateResource.setUpdateTime(LocalDateTime.now());

        boolean success = jianyingResourceManager.updateById(updateResource);
        return BaseResponse.success(success);
    }

    public BaseResponse<BasePaginationResponse<JianyingResourceListDto>> getResourceList(GetJianyingResourceListRequest request) {
        // 构建查询条件
        QueryWrapper<JianyingResource> wrapper = Wrappers.<JianyingResource>query()
                .eq(!StringUtils.isEmpty(request.getMaterialType()),JianyingResource.MATERIAL_TYPE, request.getMaterialType())
                .like(!StringUtils.isEmpty(request.getKeyword()),JianyingResource.NAME, request.getKeyword())
                .eq(Objects.nonNull(request.getId()) && request.getId() > 0, JianyingResource.ID, request.getId());

        wrapper.orderByDesc(JianyingResource.ID);

        int count = jianyingResourceManager.count(wrapper);

        // 计算分页参数
        int offset = (request.getPageNo() - 1) * request.getPageSize();
        wrapper.last("limit " + offset + "," + request.getPageSize());

        // 查询数据
        List<JianyingResource> records = jianyingResourceManager.list(wrapper);

        // 查询所有场景配置
        List<JianyingResourceScene> scenes = jianyingResourceSceneManager.list(
                Wrappers.<JianyingResourceScene>query()
                        .eq(JianyingResourceScene.STATUS, 1)
                        .select(JianyingResourceScene.ID, JianyingResourceScene.TITLE, JianyingResourceScene.RESOURCE_CONFIG)
        );

        // 查询subtitle_style类型的分类
        List<Category> categories = categoryManager.list(
                Wrappers.<Category>query()
                        .eq(Category.TYPE, "subtitle_style")
                        .select(Category.ID, Category.NAME)
        );
        Map<Long, Category> categoryMap = categories.stream()
                .collect(Collectors.toMap(Category::getId, category -> category));

        // 构建资源ID到场景标题的映射
        Map<Integer, Set<String>> resourceToTitles = new HashMap<>();
        for (JianyingResourceScene scene : scenes) {
            if (StringUtils.isEmpty(scene.getResourceConfig())) {
                continue;
            }
            List<JyResourceSceneConfig> configs = JsonUtil.jsonToList(scene.getResourceConfig(), JyResourceSceneConfig.class);
            if (configs == null || configs.isEmpty()) {
                continue;
            }

            for (JyResourceSceneConfig config : configs) {
                // 处理所有类型的resourceIds
                addResourceIds(resourceToTitles, config.getResourceIds(), scene.getTitle());
                
                // 处理textStyleIds
                addResourceIds(resourceToTitles, config.getTextStyleIds(), scene.getTitle());
            }
        }

        // 转换为DTO并设置关联文本类型
        List<JianyingResourceListDto> dtoList = records.stream()
                .map(resource -> {
                    JianyingResourceListDto dto = convertToListDto(resource);
                    Set<String> titles = resourceToTitles.get(resource.getId());
                    dto.setRelatedTextTypes(titles != null ? new ArrayList<>(titles) : new ArrayList<>());

                    // 设置样式分类
                    if (!StringUtils.isEmpty(resource.getStyles())) {
                        List<CategoryNode> styleText = Arrays.stream(resource.getStyles().split(","))
                                .map(categoryId -> {
                                    try {
                                        Category category = categoryMap.get(Long.parseLong(categoryId));
                                        if (category != null) {
                                            CategoryNode node = new CategoryNode();
                                            node.setId(category.getId());
                                            node.setName(category.getName());
                                            return node;
                                        }
                                    } catch (NumberFormatException e) {
                                        // ignore
                                    }
                                    return null;
                                })
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList());
                        dto.setStyleText(styleText);
                    } else {
                        dto.setStyleText(new ArrayList<>());
                    }
                    return dto;
                })
                .collect(Collectors.toList());

        // 构建分页响应
        BasePaginationResponse<JianyingResourceListDto> response = new BasePaginationResponse<>();
        response.setList(dtoList);
        response.setTotal(count);

        return BaseResponse.success(response);
    }

    private void addResourceIds(Map<Integer, Set<String>> resourceToTitles, Set<Integer> resourceIds, String title) {
        if (!CollectionUtils.isEmpty(resourceIds)) {
            for (Integer resourceId : resourceIds) {
                resourceToTitles.computeIfAbsent(resourceId, k -> new HashSet<>()).add(title);
            }
        }
    }

    private JianyingResourceListDto convertToListDto(JianyingResource resource) {
        JianyingResourceListDto dto = new JianyingResourceListDto();
        BeanUtils.copyProperties(resource, dto);
        return dto;
    }

    private JianyingResourceSimpleDto toJianyingResourceSimpleDto(JianyingResource jianyingResource) {
        JianyingResourceSimpleDto jianyingResourceSimpleDto = new JianyingResourceSimpleDto();
        jianyingResourceSimpleDto.setId(jianyingResource.getId());
        jianyingResourceSimpleDto.setResourceId(jianyingResource.getResourceId());
        jianyingResourceSimpleDto.setName(jianyingResource.getName());
        jianyingResourceSimpleDto.setDemoUrl(jianyingResource.getDemoUrl());
        jianyingResourceSimpleDto.setMaterialType(jianyingResource.getMaterialType());
        return jianyingResourceSimpleDto;
    }

    public Boolean updateJianYingResourceStyle(Long resourceId, Long styleId,Boolean add) {
        JianyingResource jianyingResource = jianyingResourceManager.getById(resourceId);
        List<String> styleIds = Lists.newArrayList(COMMA_SPLITTER.splitToList(jianyingResource.getStyles()));

        if (add){
            styleIds.add(styleId.toString());
        }else {
            styleIds.remove(styleId.toString());
        }

        return jianyingResourceManager.update(Wrappers.<JianyingResource>update()
                .eq(JianyingResource.ID,resourceId)
                .set(JianyingResource.STYLES,COMMA_JOINER.join(styleIds))
        );
    }


}
