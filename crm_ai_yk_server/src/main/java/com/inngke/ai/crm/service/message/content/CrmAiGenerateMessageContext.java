package com.inngke.ai.crm.service.message.content;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2023-09-04 14:06
 **/
public class CrmAiGenerateMessageContext extends CrmMessageContext {


    private Long id;

    private String name;

    private String activityCode;

    private String num;

    private String type;

    private LocalDateTime time;
    private Integer productId;

    private String currentCoin;

    public String getCurrentCoin() {
        return currentCoin;
    }

    public void setCurrentCoin(String currentCoin) {
        this.currentCoin = currentCoin;
    }

    public String getNum() {
        return num;
    }

    public void setNum(String num) {
        this.num = num;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getActivityCode() {
        return activityCode;
    }

    public void setActivityCode(String activityCode) {
        this.activityCode = activityCode;
    }

    public LocalDateTime getTime() {
        return time;
    }

    public void setTime(LocalDateTime time) {
        this.time = time;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public Integer getProductId() {
        return productId;
    }
}
