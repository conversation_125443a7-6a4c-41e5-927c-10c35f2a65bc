/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.inngke.ai.crm.db.crm.dao.AiGenerateTaskDao;
import com.inngke.ai.crm.db.crm.entity.*;
import com.inngke.ai.crm.db.crm.manager.*;
import com.inngke.ai.crm.dto.enums.AiGenerateTaskStatusEnum;
import com.inngke.ai.crm.dto.enums.AiProductIdEnum;
import com.inngke.ai.crm.dto.request.devops.TransferUserRequest;
import com.inngke.ai.crm.dto.request.publish.GetPendingVideoListRequest;
import com.inngke.ai.crm.dto.request.video.SaveVideoTaskInfoRequest;
import com.inngke.ai.crm.dto.response.AiGenerateTaskStatisticDto;
import com.inngke.ai.crm.dto.response.ai.AiGenerateRequest;
import com.inngke.ai.crm.dto.response.org.ProductStatisticsDto;
import com.inngke.ai.crm.dto.response.org.StaffProductStatisticsDtoDto;
import com.inngke.ai.crm.dto.response.publish.PendingVideoDto;
import com.inngke.common.service.JsonService;
import com.inngke.common.utils.DateTimeUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;


/**
 * <p>
 * AI生成作业 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-31
 */
@Service
public class AiGenerateTaskManagerImpl extends ServiceImpl<AiGenerateTaskDao, AiGenerateTask> implements AiGenerateTaskManager {

    private static final Logger logger = LoggerFactory.getLogger(AiGenerateTaskManagerImpl.class);

    private static final String MONTH = "month";

    private static final String DAY = "day";

    @Autowired
    private CoinManager coinManager;

    @Autowired
    private CoinLogManager coinLogManager;

    @Autowired
    private AiGenerateTaskIoManager aiGenerateTaskIoManager;

    @Autowired
    private CoinMinusLogManager coinMinusLogManager;

    @Autowired
    private AiGenerateTaskDao aiGenerateTaskDao;

    @Autowired
    private AiGenerateVideoOutputManager aiGenerateVideoOutputManager;

    @Autowired
    private JsonService jsonService;

    @Override
    public List<AiGenerateTask> listByPageId(Long userId, List<Integer> statusList, Long pageId, Integer size) {
        QueryWrapper<AiGenerateTask> queryWrapper = new QueryWrapper<AiGenerateTask>()
                .eq(AiGenerateTask.USER_ID, userId)
                .eq(AiGenerateTask.AI_PRODUCT_ID, AiProductIdEnum.XIAO_HOME_SHU.getType())
                .in(AiGenerateTask.STATUS, statusList)
                .orderByDesc(AiGenerateTask.ID)
                .last("limit " + size);
        if (Objects.nonNull(pageId)) {
            queryWrapper.lt(AiGenerateTask.ID, pageId);
        }
        return list(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(AiGenerateTask aiGenerateTask, AiGenerateRequest request, List<Coin> consumeCoins, List<CoinLog> coinLogs, List<CoinMinusLog> coinMinusLogs, Consumer<AiGenerateTask> afterHandle) {
        logger.info("生成AiGenerateTask开始：{}", aiGenerateTask.getId());
        // 插入数据
        if (count(Wrappers.<AiGenerateTask>query().eq(AiGenerateTask.ID, aiGenerateTask.getId())) == 0) {
            save(aiGenerateTask);

            AiGenerateTaskIo taskIo = new AiGenerateTaskIo();
            taskIo.setId(aiGenerateTask.getId());
            taskIo.setInputs(jsonService.toJson(request));
            taskIo.setOutputs(null);
            taskIo.setCreateTime(aiGenerateTask.getCreateTime());
            taskIo.setUpdateTime(aiGenerateTask.getCreateTime());
            aiGenerateTaskIoManager.save(taskIo);

            if (!CollectionUtils.isEmpty(consumeCoins)) {
                coinManager.updateBatchById(consumeCoins);
            }
            if (!CollectionUtils.isEmpty(coinLogs)) {
                coinLogManager.saveBatch(coinLogs);
            }
            if (!CollectionUtils.isEmpty(coinMinusLogs)) {
                coinMinusLogManager.saveBatch(coinMinusLogs);
            }
        }


        if (afterHandle != null) {
            afterHandle.accept(aiGenerateTask);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createRollback(CoinLog coinLogRollback, List<Coin> coinRollback, List<CoinMinusLog> coinMinusLogRollback, Long aiGenerateTaskId) {
        if (Objects.nonNull(coinLogRollback)) {
            coinLogManager.save(coinLogRollback);
        }
        if (!CollectionUtils.isEmpty(coinRollback)) {
            coinManager.updateBatchById(coinRollback);
        }
        if (!CollectionUtils.isEmpty(coinMinusLogRollback)) {
            coinMinusLogManager.saveBatch(coinMinusLogRollback);
        }

        AiGenerateTask aiGenerateTask = new AiGenerateTask();
        aiGenerateTask.setId(aiGenerateTaskId);
        aiGenerateTask.setStatus(AiGenerateTaskStatusEnum.FAIL.getCode());
        aiGenerateTask.setAiFinishTime(LocalDateTime.now());
        updateById(aiGenerateTask);
    }

    @Override
    public List<AiGenerateTask> getUserHistoryByProductId(Long userId, List<Integer> productIds, Long lastId, Integer pageSize, String sort) {
        return list(Wrappers.<AiGenerateTask>query()
                .eq(AiGenerateTask.USER_ID, userId)
                .in(!CollectionUtils.isEmpty(productIds), AiGenerateTask.AI_PRODUCT_ID, productIds)
                .lt(Objects.nonNull(lastId), AiGenerateTask.ID, lastId)
                .last("limit " + pageSize)
                .orderByDesc(StringUtils.isNotBlank(sort), sort)
                .orderByDesc(AiGenerateTask.ID)
        );
    }

    @Override
    public Integer count(Long userId, List<Integer> productIds) {
        return count(Wrappers.<AiGenerateTask>query()
                .eq(AiGenerateTask.USER_ID, userId)
                .in(!CollectionUtils.isEmpty(productIds), AiGenerateTask.AI_PRODUCT_ID, productIds)
        );
    }

    @Override
    public AiGenerateTask getUserTask(Long userId, Long taskId) {
        return getOne(Wrappers.<AiGenerateTask>query().eq(AiGenerateTask.USER_ID, userId).eq(AiGenerateTask.ID, taskId));
    }

    @Override
    public boolean removeUserTask(Long userId, Long taskId) {
        return remove(Wrappers.<AiGenerateTask>query().eq(AiGenerateTask.USER_ID, userId).eq(AiGenerateTask.ID, taskId));
    }

    @Override
    public List<ProductStatisticsDto> getProductStatistics(Long organizeId, String startTime, String endTime) {
        LocalDateTime startDateTime = LocalDateTime.of(DateTimeUtils.toLocalDate(startTime), LocalTime.MIN);
        LocalDateTime endDateTime = LocalDateTime.of(DateTimeUtils.toLocalDate(endTime), LocalTime.MAX);

//        String timeInterval = Duration.between(startDateTime, endDateTime).toDays() > 30 ? MONTH : DAY;
        String timeInterval = DAY;

        return aiGenerateTaskDao.getProductStatistics(organizeId, startDateTime, endDateTime, timeInterval).stream()
                .map(item -> fillData(item, ProductStatisticsDto.init())).sorted(Comparator.comparing(ProductStatisticsDto::getTime)).collect(Collectors.toList());

    }

    @Override
    public List<StaffProductStatisticsDtoDto> getStaffProductStatistics(Long organizeId, String startTime, String endTime) {
        LocalDateTime startDateTime = LocalDateTime.of(DateTimeUtils.toLocalDate(startTime), LocalTime.MIN);
        LocalDateTime endDateTime = LocalDateTime.of(DateTimeUtils.toLocalDate(endTime), LocalTime.MAX);

        String timeInterval = Duration.between(startDateTime, endDateTime).toDays() > 30 ? MONTH : DAY;

        List<StaffProductStatisticsDtoDto> result = aiGenerateTaskDao.getStaffProductStatistics(organizeId, startDateTime, endDateTime).stream().map(item -> {
            StaffProductStatisticsDtoDto dto = fillData(item, StaffProductStatisticsDtoDto.initDefault());
            Optional.ofNullable(item.get("userId")).map(Object::toString).map(Long::valueOf).ifPresent(dto::setStaffId);
            return dto;
        }).sorted(Comparator.comparing(ProductStatisticsDto::getTime)).collect(Collectors.toList());

        result.forEach(item -> item.setTotalAi(item.getUserDocQa() + item.getXiaoHongShu() + item.getVideo() + item.getRenderings()));

        result.sort(Comparator.comparing(StaffProductStatisticsDtoDto::getTotalAi));

        Collections.reverse(result);

        return result;
    }

    @Override
    public Map<Long, Long> getTaskUserIdMap(List<Long> pidList) {
        if (CollectionUtils.isEmpty(pidList)) {
            return Maps.newHashMap();
        }
        return this.list(Wrappers.<AiGenerateTask>query().in(AiGenerateTask.ID, pidList).select(AiGenerateTask.ID, AiGenerateTask.USER_ID))
                .stream().collect(Collectors.toMap(AiGenerateTask::getId, AiGenerateTask::getUserId));
    }

    /**
     * 获取ai生成任务数据,用于同步
     */
    @Override
    public List<AiGenerateTaskStatisticDto> getSyncAiGenerateTaskStatistic(Long id, Integer pageSize) {
        return getBaseMapper().getSyncAiGenerateTaskStatistic(id, pageSize);
    }

    @Override
    public List<AiGenerateTaskStatisticDto> getAiGenerateTaskStatistic(List<Long> ids) {
        return getBaseMapper().getAiGenerateTaskStatistic(ids);
    }

    @Override
    public List<AiGenerateTask> getByUserId(Long id, String... columns) {
        return list(Wrappers.<AiGenerateTask>query().eq(AiGenerateTask.USER_ID, id).select(columns));
    }

    @Override
    public void transfer(TransferUserRequest request) {
        update(Wrappers.<AiGenerateTask>update().eq(AiGenerateTask.USER_ID, request.getSourceUserId())
                .set(AiGenerateTask.USER_ID, request.getTargetUserId())
        );
    }

    @Override
    public String getUserLastInputs(long userId, int difyAppConfId) {
        return getBaseMapper().getUserLastInputs(userId, difyAppConfId == 0 ? null : difyAppConfId);
    }

    @Override
    public List<PendingVideoDto> getPublishTaskPendingVideo(Long organizeId, GetPendingVideoListRequest request) {
        return this.baseMapper.getPublishTaskPendingVideo(
                organizeId,
                request.getDirId(),
                request.getCreatorId(),
                request.getCreateTimeStart(),
                request.getCreateTimeEnd(),
                request.getKeyword(),
                (request.getPageNo() - 1) * request.getPageSize(),
                request.getPageSize()
        );
    }

    @Override
    public int getPublishTaskPendingVideoCount(Long organizeId, GetPendingVideoListRequest request) {
        return this.baseMapper.getPublishTaskPendingVideoCount(
                organizeId,
                request.getDirId(),
                request.getCreatorId(),
                request.getCreateTimeStart(),
                request.getCreateTimeEnd(),
                request.getKeyword()
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveVideoTaskInfo(Long id, SaveVideoTaskInfoRequest request) {

        this.update(Wrappers.<AiGenerateTask>update()
                .eq(AiGenerateTask.ID, id)
                .set(AiGenerateTask.TAGS, request.getTags())
                .set(AiGenerateTask.COVER_IMAGE, request.getCoverImage())
        );

        aiGenerateVideoOutputManager.update(Wrappers.<AiGenerateVideoOutput>update()
                .eq(AiGenerateVideoOutput.TASK_ID, id)
                .set(AiGenerateVideoOutput.VIDEO_TITLE, request.getTitle())
                .set(AiGenerateVideoOutput.VIDEO_CONTENT, request.getDesc())
        );
    }

    private <D extends ProductStatisticsDto> D fillData(Map<String, Object> item, D dto) {
        Optional.ofNullable(item.get("xiaoHongShu")).map(Object::toString).map(Integer::valueOf).ifPresent(dto::setXiaoHongShu);
        Optional.ofNullable(item.get("renderings")).map(Object::toString).map(Integer::valueOf).ifPresent(dto::setRenderings);
        Optional.ofNullable(item.get("video")).map(Object::toString).map(Integer::valueOf).ifPresent(dto::setVideo);
        Optional.ofNullable(item.get("userDocQa")).map(Object::toString).map(Integer::valueOf).ifPresent(dto::setUserDocQa);
        Optional.ofNullable(item.get("date")).map(Object::toString).ifPresent(dto::setDate);
        Optional.ofNullable(item.get("createTime")).map(Object::toString).map(Long::valueOf).map(DateTimeUtils::MillisToLocalDateTime).map(DateTimeUtils::getMilli)
                .ifPresent(dto::setTime);

        return dto;
    }
}
