package com.inngke.ai.crm.dto.enums;

public enum CreationTaskStaffStateEnum {

    INCOMPLETE(0, "未完成"),
    COMPLETED(1, "已完成");

    private final Integer state;

    private final String name;

    CreationTaskStaffStateEnum(Integer state, String name) {
        this.state = state;
        this.name = name;
    }

    public static CreationTaskStaffStateEnum parse(Integer state) {
        for (CreationTaskStaffStateEnum value : CreationTaskStaffStateEnum.values()) {
            if (value.getState().equals(state)) {
                return value;
            }
        }

        return null;
    }

    public Integer getState() {
        return state;
    }

    public String getName() {
        return name;
    }
}
