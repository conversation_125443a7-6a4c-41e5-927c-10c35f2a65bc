package com.inngke.ai.crm.dto.response;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023-10-17 18:18
 **/
public class CrmScanQrCodeResponse implements Serializable {

    /**
     * 请求id
     *
     * @demo 14t11d12
     */
    private String requestId;

    /**
     * 二维码状态 -2=过期 0=初始化 1=已扫码 2=已授权登录
     *
     * @demo -1
     */
    private Integer status;

    /**
     * 令牌
     *
     * @demo JD41r1.11Ffqrq.eqwe123WE1
     */
    private String token;

    /**
     * 来源 1=pc管理后台 2=pc客户端
     */
    private Integer source;

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Integer getSource() {
        return source;
    }

    public CrmScanQrCodeResponse setSource(Integer source) {
        this.source = source;
        return this;
    }
}
