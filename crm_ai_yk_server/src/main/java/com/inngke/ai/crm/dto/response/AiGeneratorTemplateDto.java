package com.inngke.ai.crm.dto.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

@Data
public class AiGeneratorTemplateDto implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 输入图片
     */
    private String image = "";

    /**
     * 图片列表
     */
    private List<String> images;

    /**
     * 输出图片
     */
    private String outPutImage;

    /**
     * 产品
     */
    private String product = "";

    /**
     * 产品id
     */
    private Integer productId;

    /**
     * 产品icon
     */
    private String productIcon;

    /**
     * 内容
     */
    private String detailContent;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 是否使用pro
     */
    private Boolean useExternalModel;

}
