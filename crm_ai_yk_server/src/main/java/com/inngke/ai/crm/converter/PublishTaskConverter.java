package com.inngke.ai.crm.converter;

import com.inngke.ai.crm.db.crm.entity.PublishTask;
import com.inngke.ai.crm.db.crm.entity.PublishVideo;
import com.inngke.ai.crm.db.crm.entity.Staff;
import com.inngke.ai.crm.dto.request.publish.CreatePublishTaskRequest;
import com.inngke.ai.crm.dto.response.publish.PublishTaskDto;
import com.inngke.ai.crm.dto.response.publish.SelectedVideoDto;
import com.inngke.common.core.utils.SnowflakeHelper;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.utils.DateTimeUtils;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

public class PublishTaskConverter {


    public static PublishTaskDto toPublishTaskDto(PublishTask publishTask) {
        return toPublishTaskDto(publishTask, null, null);
    }

    public static PublishTaskDto toPublishTaskDto(PublishTask publishTask, PublishTaskDto statisticsData, Integer videoCount) {
        PublishTaskDto publishTaskDto = new PublishTaskDto();
        publishTaskDto.setId(publishTask.getId());
        publishTaskDto.setName(publishTask.getName());

        publishTaskDto.setPublishedCount(0);
        publishTaskDto.setViewCount(0);
        publishTaskDto.setLikeCount(0);
        publishTaskDto.setCollectionCount(0);
        publishTaskDto.setCommentCount(0);
        if (Objects.nonNull(statisticsData)) {
            publishTaskDto.setPublishedCount(statisticsData.getPublishedCount());
            publishTaskDto.setViewCount(statisticsData.getViewCount());
            publishTaskDto.setLikeCount(statisticsData.getLikeCount());
            publishTaskDto.setCollectionCount(statisticsData.getCollectionCount());
            publishTaskDto.setCommentCount(statisticsData.getCommentCount());
        }

        publishTaskDto.setVideoCount(Optional.ofNullable(videoCount).orElse(0));
        publishTaskDto.setState(publishTask.getState());
        publishTaskDto.setPublishQr(publishTask.getPublishQr());
        publishTaskDto.setExpirationTime(DateTimeUtils.getMilli(publishTask.getExpirationTime()));
        return publishTaskDto;
    }

    public static PublishTask toPublishTask(Staff staff, CreatePublishTaskRequest request) {
        PublishTask publishTask = new PublishTask();
        publishTask.setId(SnowflakeHelper.getId());
        publishTask.setCreator(staff.getId());
        publishTask.setOrganizeId(staff.getOrganizeId());
        publishTask.setName(request.getName());
        publishTask.setDescription(request.getDescription());
        LocalDateTime expirationTime = DateTimeUtils.toLocalDateTime(request.getExpirationTime(), DateTimeUtils.YYYY_MM_DD);
        if (Objects.isNull(expirationTime)){
            throw new InngkeServiceException("任务有效期错误");
        }

        publishTask.setExpirationTime(expirationTime.plusDays(1).minusSeconds(1));
        publishTask.setCreateTime(LocalDateTime.now());

        return publishTask;

    }

    public static PublishVideo toPublishTaskVideo(Long publishTaskId, Long videoId) {
        PublishVideo publishVideo = new PublishVideo();
        publishVideo.setId(SnowflakeHelper.getId());
        publishVideo.setGenTaskId(videoId);
        publishVideo.setPublishTaskId(publishTaskId);
        publishVideo.setCreateTime(LocalDateTime.now());
        return publishVideo;
    }

}
