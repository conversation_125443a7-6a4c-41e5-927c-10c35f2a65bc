package com.inngke.ai.crm.service.schedule;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.ai.crm.api.video.VideoApi;
import com.inngke.ai.crm.api.video.dto.VideoContentCreateRequest;
import com.inngke.ai.crm.api.video.dto.VideoContentDto;
import com.inngke.ai.crm.core.CrmServiceConsts;
import com.inngke.ai.crm.db.crm.entity.AiGenerateTask;
import com.inngke.ai.crm.db.crm.entity.AiGenerateVideoOutput;
import com.inngke.ai.crm.db.crm.manager.*;
import com.inngke.ai.crm.dto.enums.AiGenerateTaskStatusEnum;
import com.inngke.ai.crm.service.DifyService;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.dto.Lock;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.service.LockService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.inngke.ai.crm.service.schedule.DouYinSchedule.IGNORE_PROFILE_ACTIVE;

/**
 * 视频创作补偿
 */
//@Component
public class VideoContentFixSchedule {
    private static final Logger logger = LoggerFactory.getLogger(VideoContentFixSchedule.class);

    @Autowired
    private AiGenerateVideoOutputManager aiGenerateVideoOutputManager;

    @Autowired
    private AiGenerateTaskManager aiGenerateTaskManager;

    @Autowired
    private VideoApi videoApi;

    @Autowired
    private DifyService difyService;

    @Autowired
    private LockService lockService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Value("${spring.profiles.active:dev}")
    private String profileActive;

    /**
     * 60秒执行一次,查询ai_generate_task表一小时未生成成功的任务
     */
    @Scheduled(fixedRate = 60000)
    public void videoFix() {
        if (IGNORE_PROFILE_ACTIVE.equals(profileActive)) {
            return;
        }
        Lock lock = lockService.getLock(CrmServiceConsts.CACHE_KEY_PRE + "videoContentFix", 60);
        if (Objects.isNull(lock)) {
            return;
        }
        Set<Long> taskIds = aiGenerateVideoOutputManager.list(
                Wrappers.<AiGenerateVideoOutput>query()
                        .gt(AiGenerateVideoOutput.CREATE_TIME, LocalDateTime.now().minusDays(30))
                        .isNotNull(AiGenerateVideoOutput.VIDEO_URL)
                        .ne(AiGenerateVideoOutput.VIDEO_URL, InngkeAppConst.EMPTY_STR)
                        .and(q -> q
                                .isNotNull(AiGenerateVideoOutput.VIDEO_CONTENT)
                                .or()
                                .eq(AiGenerateVideoOutput.VIDEO_CONTENT, InngkeAppConst.EMPTY_STR)
                        )
                        .select(AiGenerateVideoOutput.TASK_ID)
        ).stream().map(AiGenerateVideoOutput::getTaskId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(taskIds)) {
            return;
        }

        aiGenerateTaskManager.list(
                Wrappers.<AiGenerateTask>query()
                        .in(AiGenerateTask.ID, taskIds)
                        .eq(AiGenerateTask.STATUS, AiGenerateTaskStatusEnum.SUCCESS.getCode())
                        .select(AiGenerateTask.ID, AiGenerateTask.USER_ID)
        ).forEach(aiGenerateTask -> {
            VideoContentCreateRequest request = new VideoContentCreateRequest();
            request.setUserId(difyService.getUser(aiGenerateTask.getUserId()));
            request.setTaskId(aiGenerateTask.getId());
            try {
                videoContentFix(request);
            } catch (Exception e) {
                logger.error("修复videoContent失败", e);
            }
        });
    }

    private void videoContentFix(VideoContentCreateRequest request) {
//        String key = CrmServiceConsts.CACHE_KEY_PRE + "videoFix:" + request.getTaskId();
//        BaseResponse<VideoContentDto> response = videoApi.videoContentCreate(request);
//        if (BaseResponse.responseSuccessWithNonNullData(response)) {
//            String content = response.getData().getContent() + response.getData().getTag();
//            updateVideoContent(request.getTaskId(), content);
//            logger.info("修复videoContent成功,taskId:{},content={}", request.getTaskId(), content);
//            redisTemplate.delete(key);
//        } else {
//            // 失败了
//            ValueOperations valOps = redisTemplate.opsForValue();
//            Number errorCountNum = (Number) valOps.get(key);
//            logger.warn("修复videoContent失败,taskId:{},errorCount={}", request.getTaskId(), errorCountNum);
//            if (errorCountNum != null && errorCountNum.intValue() > 3) {
//                //不再重试
//                updateVideoContent(request.getTaskId(), InngkeAppConst.EMPTY_STR);
//            } else {
//                valOps.increment(key, 1);
//                redisTemplate.expire(key, 1, TimeUnit.DAYS);
//            }
//        }
    }

    private void updateVideoContent(long taskId, String content) {
        aiGenerateVideoOutputManager.update(
                Wrappers.<AiGenerateVideoOutput>update()
                        .set(AiGenerateVideoOutput.VIDEO_CONTENT, content)
                        .eq(AiGenerateVideoOutput.TASK_ID, taskId)
        );
    }
}
