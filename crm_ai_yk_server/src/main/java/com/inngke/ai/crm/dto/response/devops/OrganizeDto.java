package com.inngke.ai.crm.dto.response.devops;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class OrganizeDto implements Serializable {

    private Long id;

    /**
     * 企业名称
     */
    private String name;

    /**
     * 状态： -1=已停用 1=正常
     */
    private Integer status;

    /**
     * 余额，单位：分
     */
    private Integer balance;

    /**
     * 是否使用外部模型
     */
    private Integer useExternalModel;

    /**
     * 创建时间
     */
    private Long createTime;
}
