package com.inngke.ai.crm.converter;

import com.google.common.collect.Lists;
import com.inngke.ai.crm.db.crm.entity.VideoMashupScript;
import com.inngke.ai.crm.db.crm.entity.VideoProjectDraft;
import com.inngke.ai.crm.dto.request.video.VideoCreateWithMaterialRequest;
import com.inngke.ai.crm.dto.request.video.VideoCreateWithVideoMashupRequest;
import com.inngke.ai.crm.dto.request.video.VideoMashUpCreateRequest;
import com.inngke.ai.crm.dto.response.video.VideoProjectDraftDetail;
import com.inngke.ai.crm.dto.response.video.VideoProjectDraftDto;
import com.inngke.ai.dto.VideoMaterialItem;
import com.inngke.ai.dto.VideoUserScriptDto;
import com.inngke.ai.crm.dto.response.video.VideoProjectVideoMashupDetail;
import com.inngke.ai.dto.VideoUserScriptDto;
import com.inngke.ai.dto.VideoMaterialItem;
import com.inngke.ai.dto.utils.FormDataUtils;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.JsonUtil;
import com.inngke.ip.ai.dify.app.dto.VideoMashupDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

public class VideoProjectDraftConverter {


    private static final String DEFAULT_IMAGE = "https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/cover_bg/default-cover.png";

    public static VideoProjectDraftDto toVideoProjectDraftDto(VideoProjectDraft draft) {
        VideoProjectDraftDto videoProjectDraftDto = new VideoProjectDraftDto();
        VideoCreateWithMaterialRequest request = JsonUtil.jsonToObject(draft.getProjectContext(), VideoCreateWithMaterialRequest.class);
        Map<String, Object> promptMap = request.getPromptMap();
        int videoType = FormDataUtils.getInt(promptMap, FormDataUtils.FORM_KEY_VIDEO_TYPE, 1);

        boolean matchedMaterial = request.getScripts() != null;
        if (matchedMaterial) {
            if (videoType != 1) {
                for (VideoUserScriptDto script : request.getScripts()) {
                    if (CollectionUtils.isEmpty(script.getSceneMaterialList())) {
                        matchedMaterial = false;
                        break;
                    }
                }
            }
        }

        videoProjectDraftDto.setId(draft.getId());
        videoProjectDraftDto.setTitle(draft.getTitle());
        videoProjectDraftDto.setType(draft.getType());
        videoProjectDraftDto.setCoverImage(draft.getCoverImage());
        videoProjectDraftDto.setMatchedMaterial(matchedMaterial);
        videoProjectDraftDto.setCanBeMashUp(matchedMaterial);

        if (StringUtils.isBlank(videoProjectDraftDto.getCoverImage())) {
            videoProjectDraftDto.setCoverImage(DEFAULT_IMAGE);
        }
        videoProjectDraftDto.setCreateTime(DateTimeUtils.getMilli(draft.getCreateTime()));
        videoProjectDraftDto.setUpdateTime(DateTimeUtils.getMilli(draft.getUpdateTime()));
        return videoProjectDraftDto;
    }

    public static VideoProjectDraftDetail toVideoProjectDraftDetail(VideoProjectDraft draft) {
        VideoProjectDraftDetail videoProjectDraftDetail = new VideoProjectDraftDetail();
        VideoCreateWithMaterialRequest request = JsonUtil.jsonToObject(draft.getProjectContext(), VideoMashUpCreateRequest.class);

        if (!CollectionUtils.isEmpty(request.getScripts())) {
            request.getScripts().stream().filter(Objects::nonNull).forEach(script -> {
                if (script.getSceneMaterialList() == null) {
                    script.setSceneMaterialList(Lists.newArrayList());
                }
                if (script.getSceneMaterialOriginList() == null) {
                    script.setSceneMaterialOriginList(Lists.newArrayList());
                }
            });
        }

        request.setTaskId(draft.getId());
        videoProjectDraftDetail.setProjectContent(request);
        videoProjectDraftDetail.setCreateType(draft.getCreateType());
        videoProjectDraftDetail.setCreateFromId(draft.getCreateFromId());
        videoProjectDraftDetail.setStaffId(draft.getStaffId());
        videoProjectDraftDetail.setOrganizeId(draft.getOrganizeId());
        videoProjectDraftDetail.setVideoScriptId(draft.getVideoScriptId());
        videoProjectDraftDetail.setId(draft.getId());
        videoProjectDraftDetail.setTitle(draft.getTitle());
        videoProjectDraftDetail.setType(draft.getType());
        videoProjectDraftDetail.setCoverImage(draft.getCoverImage());
        videoProjectDraftDetail.setCreateTime(DateTimeUtils.getMilli(draft.getCreateTime()));
        videoProjectDraftDetail.setUpdateTime(DateTimeUtils.getMilli(draft.getUpdateTime()));
        VideoCreateWithMaterialRequest project = videoProjectDraftDetail.getProjectContent();
        project.getScripts().forEach(script -> {
            script.getMaterialList().forEach(VideoProjectDraftConverter::fixClipStartAndClipDuration);
            script.getMaterialOriginList().forEach(VideoProjectDraftConverter::fixClipStartAndClipDuration);
        });

        //处理前后帖
        if (request.getBeforeVideo() != null) {
            project.setBeforeScript(
                    new VideoUserScriptDto().setMaterialList(Lists.newArrayList(request.getBeforeVideo()))
            );
            request.setBeforeVideo(null);
        } else if (!CollectionUtils.isEmpty(request.getBeforeVideoList())) {
            project.setBeforeScript(
                    new VideoUserScriptDto().setMaterialList(request.getBeforeVideoList())
            );
            request.setBeforeVideoList(null);
        }
        if (project.getBeforeScript() == null) {
            project.setBeforeScript(new VideoUserScriptDto().setMaterialList(Lists.newArrayList()));
        }
        if (request.getAfterVideo() != null) {
            project.setAfterScript(
                    new VideoUserScriptDto().setMaterialList(Lists.newArrayList(request.getAfterVideo()))
            );
            request.setAfterVideo(null);
        } else if (!CollectionUtils.isEmpty(request.getAfterVideoList())) {
            project.setAfterScript(
                    new VideoUserScriptDto().setMaterialList(request.getAfterVideoList())
            );
            request.setAfterVideoList(null);
        }
        if (project.getAfterScript() == null) {
            project.setAfterScript(new VideoUserScriptDto().setMaterialList(Lists.newArrayList()));
        }
        return videoProjectDraftDetail;
    }

    private static void fixClipStartAndClipDuration(VideoMaterialItem material) {
        if (!CollectionUtils.isEmpty(material.getEffectiveIntervalSecond()) && material.getEffectiveIntervalSecond().size() >= 2) {
            int clipStart = Optional.ofNullable(material.getEffectiveIntervalSecond().get(0)).orElse(0) * 1000;
            Integer clipDuration = (Optional.ofNullable(material.getEffectiveIntervalSecond().get(1)).orElse(0) * 1000) - clipStart;

            if (material.getClipStart() == null) {
                material.setClipStart(clipStart);
            }
            if (material.getClipDuration() == null) {
                material.setClipDuration(clipDuration);
            }
        }
    }

    public static VideoProjectVideoMashupDetail videoProjectVideoMashupDetail(VideoProjectDraft draft, List<VideoMashupScript> videoMashupScripts) {
        VideoProjectVideoMashupDetail videoProjectVideoMashupDetail = new VideoProjectVideoMashupDetail();
        videoProjectVideoMashupDetail.setId(draft.getId());

        videoProjectVideoMashupDetail.setType(draft.getType());
        VideoCreateWithVideoMashupRequest projectContent = JsonUtil.jsonToObject(draft.getProjectContext(), VideoCreateWithVideoMashupRequest.class);

        projectContent.setVideoMashupList(videoMashupScripts.stream().map(videoMashupScript -> {
            VideoMashupDto videoMashupDto = new VideoMashupDto();
            videoMashupDto.setId(videoMashupScript.getId());
            videoMashupDto.setSubtitle(videoMashupScript.getSubtitle());
            videoMashupDto.setSelected(videoMashupScript.getSelected());
            videoMashupDto.setPreviewVideoUrl(videoMashupScript.getPreviewVideoUrl());
            return videoMashupDto;
        }).collect(Collectors.toList()));

        videoProjectVideoMashupDetail.setProjectContent(projectContent);
        videoProjectVideoMashupDetail.setCreateType(3);
        videoProjectVideoMashupDetail.setStaffId(draft.getStaffId());
        videoProjectVideoMashupDetail.setOrganizeId(draft.getOrganizeId());

        // 兼容旧的数据
        List<VideoMaterialItem> beforeVideos = projectContent.getBeforeVideoList();
        if (!CollectionUtils.isEmpty(beforeVideos)) {
            projectContent.setBeforeScript(new VideoUserScriptDto().setMaterialList(beforeVideos));
            projectContent.setBeforeVideoList(null);
        }
        if (projectContent.getBeforeScript() == null) {
            projectContent.setBeforeScript(new VideoUserScriptDto().setMaterialList(Lists.newArrayList()));
        }

        List<VideoMaterialItem> afterVideos = projectContent.getAfterVideoList();
        if (!CollectionUtils.isEmpty(afterVideos)) {
            projectContent.setAfterScript(new VideoUserScriptDto().setMaterialList(afterVideos));
            projectContent.setAfterVideoList(null);
        }
        if (projectContent.getAfterScript() == null) {
            projectContent.setAfterScript(new VideoUserScriptDto().setMaterialList(Lists.newArrayList()));
        }
        return videoProjectVideoMashupDetail;
    }
}
