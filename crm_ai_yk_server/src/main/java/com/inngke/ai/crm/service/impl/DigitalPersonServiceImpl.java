package com.inngke.ai.crm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.inngke.ai.crm.api.common.FaceRecognitionApi;
import com.inngke.ai.crm.converter.DigitalPersonConverter;
import com.inngke.ai.crm.converter.TtsConfigConverter;
import com.inngke.ai.crm.db.crm.entity.*;
import com.inngke.ai.crm.db.crm.manager.*;
import com.inngke.ai.crm.dto.enums.DigitalPersonTypeEnum;
import com.inngke.ai.crm.dto.request.digital.person.CreateDigitalPersonRequest;
import com.inngke.ai.crm.dto.request.digital.person.GetDigitalPersonListRequest;
import com.inngke.ai.crm.dto.response.digital.person.FaceLandmarkDto;
import com.inngke.ai.crm.dto.response.digital.person.DigitalPersonTagDto;
import com.inngke.ai.crm.dto.response.digital.person.DigitalPersonTemplateDto;
import com.inngke.ai.crm.dto.response.video.TtsConfigDto;
import com.inngke.ai.crm.service.ChanJingService;
import com.inngke.ai.crm.service.DigitalPersonService;
import com.inngke.ai.crm.service.dp.DigitalPersonProducerService;
import com.inngke.ai.dto.request.VideoAudioConfig;
import com.inngke.ai.dto.response.DigitalPersonVideoDto;
import com.inngke.ai.dto.response.JianYingFloatConfigDto;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.utils.JsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class DigitalPersonServiceImpl implements DigitalPersonService {

    private static final Logger logger = LoggerFactory.getLogger(DigitalPersonServiceImpl.class);

    private static final Integer NUMBER_1080 = 1080;
    private static final Integer NUMBER_1920 = 1920;

    @Autowired
    private DigitalPersonTemplateManager digitalPersonTemplateManager;
    @Autowired
    private DigitalPersonVideoManager digitalPersonVideoManager;
    @Autowired
    private DigitalPersonTagManager digitalPersonTagManager;
    @Autowired
    private ChanJingService chanJingService;
    @Autowired
    private UserManager userManager;
    @Autowired
    private DigitalPersonProducerService digitalPersonProducerService;
    @Autowired
    private TtsConfigManager ttsConfigManager;
    @Autowired
    private FaceRecognitionApi faceRecognitionApi;

    @Override
    public BaseResponse<Long> create(CreateDigitalPersonRequest request) {
        return BaseResponse.success(digitalPersonProducerService.create(request));
    }

    @Override
    public BaseResponse<List<DigitalPersonTemplateDto>> getDigitalPersonList(
            JwtPayload jwtPayload, GetDigitalPersonListRequest request) {

        User user = userManager.getById(jwtPayload.getCid());
        Long userOrganizeId = Optional.ofNullable(user.getOrganizeId()).orElse(0L);

        QueryWrapper<DigitalPersonTemplate> queryWrapper = Wrappers.<DigitalPersonTemplate>query()
                .like(StringUtils.isNotBlank(request.getKeyword()), DigitalPersonTemplate.NAME, request.getKeyword())
                .in(DigitalPersonTemplate.ORGANIZE_ID, Lists.newArrayList(userOrganizeId, 0L));
        if (Objects.nonNull(request.getTag())) {
            queryWrapper.apply("json_contains(tags,{0})", request.getTag().toString());
        }
        if (Objects.nonNull(user.getQunFengUserId()) && user.getQunFengUserId() > 0L){
            // 群峰用户不能使用即创类型的数字人
            queryWrapper.notIn(DigitalPersonTemplate.TYPE,
                    DigitalPersonTypeEnum.JI_CHUANG_DIGITAL_PERSON.getType(),
                    DigitalPersonTypeEnum.JI_CHUANG_CUSTOMIZED_DIGITAL_PERSON.getType());
        }
        queryWrapper.orderByDesc(DigitalPersonTemplate.SORT, DigitalPersonTemplate.CREATE_TIME);

        List<DigitalPersonTemplate> digitalPersonTemplateList = digitalPersonTemplateManager.list(queryWrapper);

        Map<Long, Integer> useCountMap = digitalPersonVideoManager.getUseCount(userOrganizeId);

        Map<Integer, TtsConfigDto> videoAudioConfigMap = ttsConfigManager.getAll().stream().filter(Objects::nonNull)
                .map(TtsConfigConverter::toTtsConfigDto)
                .collect(Collectors.toMap(VideoAudioConfig::getId, Function.identity()));

        Map<Long, DigitalPersonTagDto> digitalPersonTagMap = Optional.ofNullable(getDigitalPersonTagList()).map(BaseResponse::getData).orElse(Lists.newArrayList()).stream()
                .collect(Collectors.toMap(DigitalPersonTagDto::getId, Function.identity()));

        List<DigitalPersonTemplateDto> dpList = digitalPersonTemplateList.stream().map(
                digitalPersonTemplate -> DigitalPersonConverter.toDigitalPersonTemplateDto(
                        digitalPersonTemplate, useCountMap, videoAudioConfigMap,digitalPersonTagMap
                )
        ).collect(Collectors.toList());

        return BaseResponse.success(dpList);
    }

    @Override
    public BaseResponse<DigitalPersonVideoDto> getDigitalPerson(Long id) {
        DigitalPersonVideoDto digitalPersonVideoDto = new DigitalPersonVideoDto();
        digitalPersonVideoDto.setId(id);

        DigitalPersonVideo digitalPersonVideo = digitalPersonVideoManager.getById(id);
        digitalPersonVideoDto.setState(digitalPersonVideo.getState());

        if (digitalPersonVideo.getState() < 0) {
            digitalPersonVideoDto.setErrorMsg(digitalPersonVideo.getErrorMsg());
            return BaseResponse.success(digitalPersonVideoDto);
        }
        digitalPersonVideoDto.setUrl(digitalPersonVideo.getUrl());
        DigitalPersonTemplate digitalPersonTemplate = digitalPersonTemplateManager.getDeletedById(digitalPersonVideo.getTemplateId());
        Optional.ofNullable(digitalPersonTemplate).ifPresent(dp -> digitalPersonVideoDto.setJianYingFloatConfig(
                JsonUtil.jsonToObject(digitalPersonTemplate.getJianYingConfig(), JianYingFloatConfigDto.class)
        ));

        digitalPersonVideoDto.setJianYingResource(159);

        return BaseResponse.success(digitalPersonVideoDto);
    }

    @Override
    public BaseResponse<List<DigitalPersonTagDto>> getDigitalPersonTagList() {
        return BaseResponse.success(digitalPersonTagManager.list(Wrappers.<DigitalPersonTag>query().orderByDesc(DigitalPersonTag.SORT)).stream().map(digitalPersonTag -> {
            DigitalPersonTagDto digitalPersonTagDto = new DigitalPersonTagDto();
            digitalPersonTagDto.setId(digitalPersonTag.getId());
            digitalPersonTagDto.setName(digitalPersonTag.getName());
            return digitalPersonTagDto;
        }).collect(Collectors.toList()));
    }

    @Override
    public BaseResponse<DigitalPersonTemplateDto> getDigitalPersonTemplate(Long id) {
        DigitalPersonTemplate digitalPersonTemplate = digitalPersonTemplateManager.getById(id);
        if (Objects.isNull(digitalPersonTemplate)) {
            digitalPersonTemplate = digitalPersonTemplateManager.getDeletedById(id);
            if (Objects.isNull(digitalPersonTemplate)){
                throw new InngkeServiceException("数字人不存在");
            }
        }

        Integer useCount = digitalPersonVideoManager.getUseCountById(id);

        Map<Long, DigitalPersonTagDto> digitalPersonTagMap = Optional.ofNullable(getDigitalPersonTagList()).map(BaseResponse::getData).orElse(Lists.newArrayList()).stream()
                .collect(Collectors.toMap(DigitalPersonTagDto::getId, Function.identity()));

        TtsConfigDto videoAudioConfig = Optional.ofNullable(
                ttsConfigManager.getById(digitalPersonTemplate.getTtsId())
        ).map(TtsConfigConverter::toTtsConfigDto).orElse(new TtsConfigDto());

        return BaseResponse.success(
                DigitalPersonConverter.toDigitalPersonTemplateDto(digitalPersonTemplate, useCount, videoAudioConfig,digitalPersonTagMap)
        );
    }

    @Override
    public List<Integer> getFaceCenterPosition(String url) {
        BaseResponse<FaceLandmarkDto> response = faceRecognitionApi.getFaceLandmark(url);

        if (Objects.isNull(response)){
            throw new InngkeServiceException("识别失败");
        }

        return Optional.ofNullable(response.getData()).map(FaceLandmarkDto::getCenter).orElseThrow(() -> new InngkeServiceException("识别失败"));
    }


}
