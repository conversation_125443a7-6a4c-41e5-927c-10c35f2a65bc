package com.inngke.ai.crm.service.material;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.inngke.ai.crm.core.CrmServiceConsts;
import com.inngke.ai.crm.db.crm.manager.UserMaterialManager;
import com.inngke.ai.crm.db.crm.manager.VideoMaterialManager;
import com.inngke.ai.crm.dto.VideoMaterialTask;
import com.inngke.ai.crm.dto.exceptions.NoRetryException;
import com.inngke.ai.crm.service.material.task.MaterialTaskHandler;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.config.ApplicationContextHandler;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.dto.Lock;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.LockService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SessionCallback;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@Service
public class VideoMaterialTaskService {
    private static final Logger logger = LoggerFactory.getLogger(VideoMaterialTaskService.class);
    public static final String MATERIAL_TASK_KEY = CrmServiceConsts.CACHE_KEY_PRE + "materialTask:data";
    public static final Integer MAX_WORK_PROCESS = 5;
    public static final Executor ROTATE_EXECUTOR = Executors.newFixedThreadPool(MAX_WORK_PROCESS);

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private VideoMaterialManager videoMaterialManager;

    @Autowired
    private UserMaterialManager userMaterialManager;

    @Autowired
    private LockService lockService;

    private static final Map<String, MaterialTaskHandler> MATERIAL_PROCESS_CALLBACK_MAP = Maps.newConcurrentMap();

    public void submitRotateTask(VideoMaterialTask task) {
        MaterialTaskHandler taskHandler = getMaterialTaskHandler(task.getType());
        redisTemplate.opsForHash().put(MATERIAL_TASK_KEY, task.getType() + InngkeAppConst.UNDERLINE_STR + task.getId(), task);
        taskHandler.afterSubmitTask(task);

        //马上调用
//        this.handle();
    }

    public void handle() {
        Lock lock = lockService.getLock(CrmServiceConsts.CACHE_LOCK_KEY_PRE + "materialTaskProcess", 3600, false);
        if (lock == null) {
            return;
        }
        try {
            processTask();
        } finally {
            lock.unlock();
        }
    }

    private void processTask() {
        while (true) {
            List<VideoMaterialTask> videoMaterialRotateTaskList = Lists.newArrayList();
            HashOperations hashOps = redisTemplate.opsForHash();

            Set<String> fields = hashOps.keys(MATERIAL_TASK_KEY);

            // 使用SessionCallback实现原子操作
            Object transactionResults = redisTemplate.execute(new SessionCallback<>() {
                @Override
                public <K, V> List<Object> execute(RedisOperations<K, V> operations) {
                    // 1. 监视 Key 防止外部修改
                    operations.watch((K) MATERIAL_TASK_KEY);

                    try {
                        // 2. 创建事务内的 HashOperations（关键步骤！）
                        HashOperations<K, String, String> txHashOps = operations.opsForHash();

                        // 3. 获取当前数据（此操作会被加入事务队列，在 exec() 时执行）
                        Set<String> txFields = txHashOps.keys((K) MATERIAL_TASK_KEY);

                        // 4. 开启事务
                        operations.multi();

                        // 5. 执行事务操作（所有命令在此处加入队列）
                        List<String> limitedFields = txFields.stream()
                                .limit(MAX_WORK_PROCESS)
                                .collect(Collectors.toList());

                        if (!limitedFields.isEmpty()) {
                            // 3.1 获取值（加入事务队列）
                            txHashOps.multiGet((K) MATERIAL_TASK_KEY, limitedFields);
                            // 3.2 删除字段（加入事务队列）
                            txHashOps.delete((K) MATERIAL_TASK_KEY, limitedFields.toArray(new String[0]));
                        }
                        // 6. 提交事务（此时会执行所有队列中的命令）
                        return operations.exec();
                    } catch (Exception e) {
                        operations.discard();
                        throw e;
                    } finally {
                        operations.unwatch();
                    }
                }
            });

            List<Object> results = (List<Object>) transactionResults;

            // 处理事务结果
            if (!CollectionUtils.isEmpty(results)) {
                // 第0个元素是multiGet结果（限制后的值列表）
                // 第1个元素是delete结果
                List<VideoMaterialTask> tasks = (List<VideoMaterialTask>) results.get(0);
                if (!CollectionUtils.isEmpty(tasks)) {
                    videoMaterialRotateTaskList.addAll(tasks.stream()
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList()));
                }
            }

            if (CollectionUtils.isEmpty(videoMaterialRotateTaskList)) {
                return;
            }
            CountDownLatch countDownLatch = new CountDownLatch(videoMaterialRotateTaskList.size());
            videoMaterialRotateTaskList.forEach(task -> {
                AsyncUtils.runAsync(() -> {
                    try {
                        taskHandle(task);
                    } catch (NoRetryException e) {
                        logger.error(e.getMessage(), e);
                    } catch (Exception e) {
                        logger.error("[{}]发生未知错误，{}", task.getId(), task.getUrl(), e);
                    } finally {
                        countDownLatch.countDown();
                    }
                }, ROTATE_EXECUTOR, false);
            });
            try {
                countDownLatch.await();
            } catch (InterruptedException e) {
                throw new InngkeServiceException(e);
            }
        }
    }

    private void taskHandle(VideoMaterialTask task) {
        MaterialTaskHandler taskHandler = getMaterialTaskHandler(task.getType());
        HashOperations hashOps = redisTemplate.opsForHash();

        Serializable data = taskHandler.process(task);
        if (taskHandler.isTaskSuccess(task, data)) {
            taskHandler.callback(task, data);
        } else {
            int retryCount = Optional.ofNullable(task.getRetryCount()).orElse(0);
            if (retryCount < 3) {
                //重试
                task.setRetryCount(retryCount + 1);
                //放回池中，放最前吧，马上重试
                hashOps.put(MATERIAL_TASK_KEY, task.getType() + InngkeAppConst.UNDERLINE_STR + task.getId(), task);
                logger.warn("[{}]失败，准备第{}次重试，{}", task.getId(), retryCount + 1, task.getUrl());
            } else {
                taskHandler.callback(task, data);
                logger.error("[{}]重试{}次均失败，{}", task.getId(), retryCount, task.getUrl());
            }
        }
    }

    private MaterialTaskHandler getMaterialTaskHandler(String taskType) {
        return MATERIAL_PROCESS_CALLBACK_MAP.computeIfAbsent(taskType, ep -> {
            MaterialTaskHandler taskHandler;
            //将 第一个字幕变成小写，以匹配Spring Bean的命名规则
            ep = ep.substring(0, 1).toLowerCase() + ep.substring(1);
            try {
                taskHandler = ApplicationContextHandler.getApplicationContext().getBean(ep, MaterialTaskHandler.class);
            } catch (Exception e) {
                logger.error("获取回调端点实例失败：{}", taskType);
                throw new NoRetryException("获取回调端点实例失败：" + taskType);
            }
            return taskHandler;
        });
    }
}
