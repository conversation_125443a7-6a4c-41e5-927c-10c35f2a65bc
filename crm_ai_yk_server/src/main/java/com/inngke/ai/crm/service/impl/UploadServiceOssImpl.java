package com.inngke.ai.crm.service.impl;

import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.HttpMethod;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.model.ResponseHeaderOverrides;
import com.google.common.collect.Maps;
import com.inngke.ai.crm.client.common.UploadServiceForAi;
import com.inngke.ai.crm.core.config.AiGcConfig;
import com.inngke.ai.crm.core.config.AliAccountConfig;
import com.inngke.ai.crm.service.UploadService;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.JsonService;
import com.inngke.ip.common.dto.response.UploadTempCredentialDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.net.URL;
import java.util.Date;
import java.util.Map;

@Service
public class UploadServiceOssImpl implements UploadService {

    public static final String OSS_VIDEO_SERVER = "https://yk-ai-video.oss-cn-heyuan.aliyuncs.com";

    @Autowired
    private JsonService jsonService;

    @Autowired
    private UploadServiceForAi uploadServiceForAi;

    @Autowired
    private AiGcConfig aiGcConfig;

    @Autowired
    private AliAccountConfig aliAccountConfig;

    /**
     * 上传文件到OSS
     *
     * @param file     需要上传的文件
     * @param path     上传到OSS的路径（包含文件名）
     * @param mateData 自定义的mateData
     * @return 上传成功后的文件url
     */
    @Override
    public String uploadFile(File file, String path, Map<String, String> mateData) {
        UploadTempCredentialDto uploadTempCredential = uploadServiceForAi.getUploadTempCredential(aiGcConfig.getBid());
        OSS client = createClient(uploadTempCredential);
        if (path.startsWith(InngkeAppConst.OBLIQUE_LINE_STR)) {
            path = path.substring(1);
        }

        PutObjectRequest putObjectRequest = new PutObjectRequest(uploadTempCredential.getBucket(), path, file);
        if (!CollectionUtils.isEmpty(mateData)) {
            putObjectRequest.setMetadata(generateObjectMetadata(mateData));
        }

        //发送请求
        try {
            client.putObject(putObjectRequest);
        } catch (Exception e) {
            throw new InngkeServiceException("上传失败", e);
        } finally {
            client.shutdown();
        }
        return "https://" + uploadTempCredential.getBucket() + "." + uploadTempCredential.getRegion() + ".aliyuncs.com/" + path;
    }

    @Override
    public String buildDownloadUrl(String url) {
        UploadTempCredentialDto uploadTempCredential = uploadServiceForAi.getUploadTempCredential(aiGcConfig.getBid());

        String endpoint = "oss-cn-heyuan.aliyuncs.com";
        OSS client = new OSSClientBuilder().build(endpoint, aliAccountConfig.getAccessKey(), aliAccountConfig.getAccessKeySecret());

        GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(
                uploadTempCredential.getBucket(),
                url.replace(uploadTempCredential.getUploadUrl()+"/", InngkeAppConst.EMPTY_STR),
                HttpMethod.GET);
        ResponseHeaderOverrides responseHeader = new ResponseHeaderOverrides();
        responseHeader.setContentDisposition("attachment;filename=" + url.substring(url.lastIndexOf(InngkeAppConst.OBLIQUE_LINE_STR) + 1));
        request.setExpiration(new Date(new Date().getTime() + 3600 * 1000L));
        request.setResponseHeaders(responseHeader);

        URL downloadUrl = client.generatePresignedUrl(request);
        return downloadUrl.toString();
    }

    private OSS createClient(UploadTempCredentialDto uploadTempCredential) {
        ClientBuilderConfiguration config = new ClientBuilderConfiguration();
        config.setRequestTimeoutEnabled(true);
        //过期时间
//        config.setRequestTimeout((int) (uploadTempCredential.getExpired() - System.currentTimeMillis() / 1000 - 60));
        return new OSSClientBuilder()
                .build(
                        uploadTempCredential.getRegion() + ".aliyuncs.com",
                        uploadTempCredential.getTmpSecretId(),
                        uploadTempCredential.getTmpSecretKey(),
                        uploadTempCredential.getSessionToken(),
                        config
                );
    }

    private ObjectMetadata generateObjectMetadata(Map<String, String> customizeMetadata) {
        ObjectMetadata objectMetadata = new ObjectMetadata();
        Map<String, String> objectMeteMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(customizeMetadata)) {
            for (String key : customizeMetadata.keySet()) {
                objectMeteMap.put(key, customizeMetadata.get(key));
            }
        }
        objectMetadata.setUserMetadata(objectMeteMap);

        return objectMetadata;
    }

    public void setJsonService(JsonService jsonService) {
        this.jsonService = jsonService;
    }
}
