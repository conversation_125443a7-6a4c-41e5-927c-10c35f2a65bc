package com.inngke.ai.crm.db.crm.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.inngke.ai.crm.core.config.AiGcConfig;
import com.inngke.ai.crm.db.crm.dao.UserInviteLogDao;
import com.inngke.ai.crm.db.crm.entity.UserInviteLog;
import com.inngke.ai.crm.db.crm.manager.UserInviteLogManager;
import com.inngke.ai.crm.dto.enums.UserInviteLogStatusEnum;
import com.inngke.ai.crm.service.AiLockService;
import com.inngke.common.dto.Lock;
import com.inngke.common.service.SnowflakeIdService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 邀请记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-29
 */
@Service
public class UserInviteLogManagerImpl extends ServiceImpl<UserInviteLogDao, UserInviteLog> implements UserInviteLogManager {

    private static final Logger logger = LoggerFactory.getLogger(UserInviteLogManagerImpl.class);


    @Autowired
    private AiLockService lockService;

    @Autowired
    private SnowflakeIdService snowflakeIdService;

    @Autowired
    private AiGcConfig aiGcConfig;

    @Override
    public boolean saveInviteLog(Long invitedUserId, Long userId) {
        Lock lock = lockService.getInviteLogLock(invitedUserId, userId, false);
        if (lock == null) {
            return true;
        }
        logger.info("进入邀请逻辑，invitedUserId：{},userId:{}", invitedUserId, userId);
        UserInviteLog inviteUser = getInviteUser(invitedUserId, userId);
        if (Objects.isNull(inviteUser)) {
            UserInviteLog userInviteLog = new UserInviteLog();
            userInviteLog.setId(snowflakeIdService.getId());
            userInviteLog.setUserId(userId);
            userInviteLog.setInvitedUserId(invitedUserId);
            userInviteLog.setStatus(UserInviteLogStatusEnum.WAIT_CERTIFY.getCode());
            userInviteLog.setCreateTime(LocalDateTime.now());
            save(userInviteLog);
            return true;
        }
        if (UserInviteLogStatusEnum.SUCCESS.getCode().equals(inviteUser.getStatus())) {
            return true;
        }

        UserInviteLog update = new UserInviteLog();
        update.setId(inviteUser.getId());
        update.setUpdateTime(LocalDateTime.now());
        return updateById(update);
    }

    @Override
    public UserInviteLog getInviteLog(Long invitedUserId) {

        List<UserInviteLog> list = list(new QueryWrapper<UserInviteLog>()
                .eq(UserInviteLog.INVITED_USER_ID, invitedUserId)
                .ne(UserInviteLog.STATUS, UserInviteLogStatusEnum.FAIL.getCode())
                .orderByDesc(UserInviteLog.UPDATE_TIME)
                .last("limit 10"));
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        List<Long> inviteUserId = list.stream().map(UserInviteLog::getUserId).collect(Collectors.toList());
        // 名用户最多可邀请30名好友
        return getInviteLog(inviteUserId, list);
    }

    @Override
    public UserInviteLog getInviteLogLogin(Long inviteUserId, Long invitedUserId) {
        UserInviteLog userInviteLog = new UserInviteLog();
        userInviteLog.setId(snowflakeIdService.getId());
        userInviteLog.setUserId(inviteUserId);
        userInviteLog.setInvitedUserId(invitedUserId);
        userInviteLog.setStatus(UserInviteLogStatusEnum.WAIT_CERTIFY.getCode());
        LocalDateTime now = LocalDateTime.now();
        userInviteLog.setCreateTime(now);
        userInviteLog.setUpdateTime(now);

        save(userInviteLog);

        return getInviteLog(Lists.newArrayList(inviteUserId), Lists.newArrayList(userInviteLog));
    }

    private UserInviteLog getInviteLog(List<Long> inviteUserId, List<UserInviteLog> list) {
        // 名用户最多可邀请30名好友
        Map<Long, Integer> inviteCountMap = list(new QueryWrapper<UserInviteLog>()
                .in(UserInviteLog.USER_ID, inviteUserId)
                .eq(UserInviteLog.STATUS, UserInviteLogStatusEnum.SUCCESS.getCode())
                .groupBy(UserInviteLog.USER_ID)
                .select("count(user_id) as status", UserInviteLog.USER_ID))
                .stream().collect(Collectors.toMap(UserInviteLog::getUserId, UserInviteLog::getStatus));

        for (UserInviteLog userInviteLog : list) {
            Integer count = inviteCountMap.get(userInviteLog.getUserId());
            if (count == null || count.compareTo(aiGcConfig.getMaxInviteCount()) < 0) {
                return userInviteLog;
            }
        }
        return null;
    }

    private UserInviteLog getInviteUser(Long invitedUserId, Long userId) {
        QueryWrapper<UserInviteLog> queryWrapper = new QueryWrapper<UserInviteLog>()
                .eq(UserInviteLog.INVITED_USER_ID, invitedUserId)
                .eq(UserInviteLog.USER_ID, userId)
                .ne(UserInviteLog.STATUS, UserInviteLogStatusEnum.FAIL.getCode())
                .last("limit 1");

        List<UserInviteLog> list = list(queryWrapper);

        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }


}
