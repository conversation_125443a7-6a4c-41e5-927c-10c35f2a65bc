package com.inngke.ai.crm.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.inngke.ai.crm.converter.AiGenerateTaskConverter;
import com.inngke.ai.crm.core.util.VideoUtil;
import com.inngke.ai.crm.db.crm.dao.AiGenerateTaskDao;
import com.inngke.ai.crm.db.crm.entity.*;
import com.inngke.ai.crm.db.crm.manager.*;
import com.inngke.ai.crm.dto.IdPageDto;
import com.inngke.ai.crm.dto.enums.AiGenerateTaskStatusEnum;
import com.inngke.ai.crm.dto.enums.AiProductIdEnum;
import com.inngke.ai.crm.dto.enums.DouYinVideoReleaseStateEnum;
import com.inngke.ai.crm.dto.enums.VideoDraftTypeEnum;
import com.inngke.ai.crm.dto.request.*;
import com.inngke.ai.crm.dto.response.*;
import com.inngke.ai.crm.dto.response.ai.AiGenerateTaskDto;
import com.inngke.ai.crm.dto.response.ai.AiInputXiaoHongShuDto;
import com.inngke.ai.crm.dto.response.ai.AiOutputXiaoHongShuDto;
import com.inngke.ai.crm.dto.response.pro.DifyAppConfDto;
import com.inngke.ai.crm.dto.response.video.OeDiagnosisDto;
import com.inngke.ai.crm.service.*;
import com.inngke.ai.crm.service.es.AiGenerateTaskEsService;
import com.inngke.ai.dto.enums.AiErrorCodeEnum;
import com.inngke.ai.dto.enums.VideoTypeEnum;
import com.inngke.ai.dto.utils.FormDataUtils;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.JsonService;
import com.inngke.common.utils.DateTimeUtils;
import org.apache.lucene.search.TotalHits;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-08-31 11:22
 **/
@Service
public class CrmAiGeneratorServiceImpl implements CrmAiGeneratorService {


    private static final Logger logger = LoggerFactory.getLogger(CrmAiGeneratorServiceImpl.class);

    @Autowired
    private AiGenerateTaskManager aiGenerateTaskManager;

    @Autowired
    private AiGenerateTaskReleaseManager aiGenerateTaskReleaseManager;

    @Autowired
    private AiGenerateTaskIoManager aiGenerateTaskIoManager;

    @Autowired
    private AiGeneratorTaskConverterService aiGeneratorTaskConverterService;

    @Autowired
    private AiGenerateVideoOutputManager aiGenerateVideoOutputManager;

    @Autowired
    private VideoProjectDraftManager videoProjectDraftManager;

    @Autowired
    private OrganizeManager organizeManager;

    @Autowired
    private UserManager userManager;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private MaterialCategoryManager materialCategoryManager;

    @Autowired
    private DifyAppConfManager difyAppConfManager;

    @Autowired
    private StaffService staffService;

    @Autowired
    RestHighLevelClient restHighLevelClient;

    @Autowired
    StaffUserRelationService staffUserRelationService;

    @Autowired
    private DifyAppConfService difyAppConfService;

    @Autowired
    DepartmentCacheFactory departmentCacheFactory;

    @Autowired
    private VideoCreateTaskManager videoCreateTaskManager;

    @Autowired
    private StaffManager staffManager;

    @Autowired
    private AiGenerateTaskDao aiGenerateTaskDao;

    @Autowired
    private PublishVideoManager publishVideoManager;

    @Autowired
    private VideoOceanengineDiagnosisManager videoOceanengineDiagnosisManager;

    @Override
    public BaseResponse<IdPageDto<AiGenerateHistoryDto>> aiGenerateHistory(AiGenerateHistoryRequest request) {
        Long userId = request.getUserId();
        User user = userManager.getById(userId);
        if (Objects.isNull(user)) {
            return BaseResponse.error("用户不存在");
        }
        Long organizeId = user.getOrganizeId();

        IdPageDto<AiGenerateHistoryDto> result = new IdPageDto<>();

        List<AiGenerateTask> list = aiGenerateTaskManager.listByPageId(
                request.getUserId(), Lists.newArrayList(1, 2), request.getLastId(), request.getPageSize()
        );
        if (CollectionUtils.isEmpty(list)) {
            result.setList(List.of());
            result.setTotal(0);
            return BaseResponse.success(result);
        }

        List<Long> aiGenerateIds = list.stream().map(AiGenerateTask::getId).collect(Collectors.toList());

        Map<Long, AiGenerateTaskRelease> taskReleaseMap = aiGenerateTaskReleaseManager.getXhsAiGenerateTaskRelease(aiGenerateIds)
                .stream()
                .collect(Collectors.toMap(AiGenerateTaskRelease::getAiGenerateTaskId, Function.identity(), (k1, k2) -> k1));

        Map<Long, AiGenerateTaskIo> taskIoMap = aiGenerateTaskIoManager.list(
                Wrappers.<AiGenerateTaskIo>query().in(AiGenerateTaskIo.ID, aiGenerateIds)
        ).stream().collect(Collectors.toMap(AiGenerateTaskIo::getId, Function.identity()));

        Map<Long, List<DifyAppConf>> dfMap = difyAppConfManager.organizeIdGroupMap(organizeId, 2);
        Map<Integer, DifyAppConf> appConfMap = Optional.ofNullable(dfMap.get(organizeId))
                .orElse(Optional.ofNullable(dfMap.get(0L)).orElse(Lists.newArrayList()))
                .stream().collect(Collectors.toMap(DifyAppConf::getId, Function.identity()));

        List<AiGenerateHistoryDto> aiGenerateHistoryDtos = list.stream()
                .map(item -> aiGenerateHistoryDto(item, taskIoMap, taskReleaseMap, appConfMap)).collect(Collectors.toList());

        result.setList(Optional.of(aiGenerateHistoryDtos).orElse(new ArrayList<>()));

        return BaseResponse.success(result);
    }

    private AiGenerateHistoryDto aiGenerateHistoryDto(AiGenerateTask aiGenerateTask, Map<Long, AiGenerateTaskIo> taskIoMap, Map<Long, AiGenerateTaskRelease> map,
                                                      Map<Integer, DifyAppConf> appConfMap) {

        AiGenerateHistoryDto result = new AiGenerateHistoryDto();
        AiGenerateTaskIo taskIo = taskIoMap.get(aiGenerateTask.getId());
        if (Objects.nonNull(taskIo)) {
            String inputs = taskIo.getInputs();
            String outputs = taskIo.getOutputs();
            if (StringUtils.isNotEmpty(inputs)) {
                AiInputXiaoHongShuDto aiInputXiaoHongShuDto = jsonService.toObject(inputs, AiInputXiaoHongShuDto.class);
                result.setImage(aiInputXiaoHongShuDto.getImage());
                DifyAppConf difyAppConf = appConfMap.get(aiInputXiaoHongShuDto.getType());
                if (Objects.nonNull(difyAppConf)) {
                    result.setTypeText(difyAppConf.getName());
                }
                result.setImages(aiInputXiaoHongShuDto.getImages());
            }

            if (StringUtils.isNotEmpty(outputs)) {
                AiOutputXiaoHongShuDto aiOutputXiaoHongShuDto = jsonService.toObject(outputs, AiOutputXiaoHongShuDto.class);
                result.setDetailContent(aiOutputXiaoHongShuDto.getContent());
                result.setTitle(aiOutputXiaoHongShuDto.getTitle());
            }

        }

        result.setId(aiGenerateTask.getId());
        result.setCreateTime(DateTimeUtils.getMilli(aiGenerateTask.getCreateTime()));
        result.setStatus(aiGenerateTask.getStatus());
        result.setUseExternalModel(aiGenerateTask.getUseExternalModel());

        AiProductIdEnum aiTypeEnum = AiProductIdEnum.getByType(aiGenerateTask.getAiProductId());
        result.setTypeText(Objects.isNull(aiTypeEnum) ? "" : aiTypeEnum.getTitle());

        AiGenerateTaskRelease release = map.get(aiGenerateTask.getId());
        if (Objects.nonNull(release)) {
            CrmPublishInfo crmPublishInfo = CrmPublishInfo.toCrmPublishInfo(release);
            result.setPublishInfos(Lists.newArrayList(crmPublishInfo));
        }

        return result;
    }

    @Override
    public BaseResponse<Boolean> appraise(GeneLogAppraiseRequest request) {
        AiGenerateTask aiGenerateTask = aiGenerateTaskManager.getById(request.getId());
        if (Objects.nonNull(aiGenerateTask) &&
                Objects.nonNull(aiGenerateTask.getFeedback()) &&
                aiGenerateTask.getFeedback().equals(request.getScore())) {
            return BaseResponse.success(true);
        }

        AiGenerateTask updateDto = new AiGenerateTask();
        updateDto.setId(request.getId());
        updateDto.setFeedback(request.getScore());
        updateDto.setFeedbackTime(LocalDateTime.now());
        boolean update = aiGenerateTaskManager.updateById(updateDto);

        return BaseResponse.success(update);
    }

    @Override
    public BaseResponse<IdPageDto<AiGeneratorTemplateDto>> getAiGenerateHistory(GetAiGeneratorHistoryListRequest request) {
        List<AiGenerateTask> userHistoryByProductId =
                aiGenerateTaskManager.getUserHistoryByProductId(request.getUserId(), request.getProductIds(),
                        request.getLastId(), request.getPageSize(), request.getSort());

        List<AiGeneratorTemplateDto> aiGeneratorTemplateDtoList =
                aiGeneratorTaskConverterService.toAiGeneratorTemplateDtoList(userHistoryByProductId);

        IdPageDto<AiGeneratorTemplateDto> idPageDto = new IdPageDto<>();
        idPageDto.setList(aiGeneratorTemplateDtoList);
        idPageDto.setTotal(aiGenerateTaskManager.count(request.getUserId(), request.getProductIds()));

        return BaseResponse.success(idPageDto);
    }

    @Override
    public BaseResponse<IdPageDto<AiGeneratorTemplateDto>> getAiTemplate(GetAiGeneratorHistoryListRequest request) {
        Long userId = request.getUserId();
        boolean useExternalModel = organizeManager.isUseExternalModel(userManager.getUserOrganizeId(userId));
        Integer pageSize = request.getPageSize();

        request.setUserId(-1L);
        request.setSort(AiGenerateTask.SORT);
        request.setPageSize(999);
        BaseResponse<IdPageDto<AiGeneratorTemplateDto>> response = getAiGenerateHistory(request);

        Optional.ofNullable(response).map(BaseResponse::getData).map(IdPageDto::getList).ifPresent(list -> {
            List<AiGeneratorTemplateDto> templateList = list.stream().map(aiGeneratorTemplateDto -> {
                if (aiGeneratorTemplateDto.getProductId().equals(AiProductIdEnum.XIAO_HOME_SHU.getType())) {
                    aiGeneratorTemplateDto.setOutPutImage(aiGeneratorTemplateDto.getImage());
                    if (!Boolean.valueOf(useExternalModel).equals(aiGeneratorTemplateDto.getUseExternalModel())) {
                        return null;
                    }
                }
                return aiGeneratorTemplateDto;
            }).filter(Objects::nonNull).collect(Collectors.toList());
            if (templateList.size() > pageSize) {
                templateList = templateList.subList(0, pageSize);
            }
            response.getData().setList(templateList);
            response.getData().setTotal(response.getData().getList().size());
        });

        return response;
    }

    @Override
    public BaseResponse<AiGenerateTaskDto> getAiGeneratorHistoryInfo(GetAiGeneratorHistoryInfoRequest request) {
        AiGenerateTask userTask = aiGenerateTaskManager.getById(request.getTaskId());

        if (Objects.isNull(userTask)) {
            return BaseResponse.error("任务不存在");
        }

        AiGenerateTaskIo taskIo = aiGenerateTaskIoManager.getById(userTask.getId());
        if (taskIo == null) {
            return BaseResponse.error("任务不存在");
        }
        AiGenerateTaskDto aiGenerateTaskDto = aiGeneratorTaskConverterService.toAiGenerateTaskDto(userTask, taskIo);

        return BaseResponse.success(aiGenerateTaskDto);
    }

    @Override
    public BaseResponse<Boolean> deleteHistoryTask(GetAiGeneratorHistoryInfoRequest request) {
        return BaseResponse.success(aiGenerateTaskManager.removeUserTask(request.getUserId(), request.getTaskId()));
    }

    @Override
    public BaseResponse<IdPageDto<GetHistoryVideoListDto>> getHistoryVideoList(AiGenerateHistoryRequest request, boolean pc) {
        Staff staff = staffManager.getByUserId(request.getUserId());
        if (staff == null) {
            IdPageDto<GetHistoryVideoListDto> emptyRes = new IdPageDto<>();
            return BaseResponse.success(emptyRes);
        }

        Boolean tester = staff.getTester();

        Set<Long> staffDepartmentIds = Sets.newHashSet();
        if (!tester && Objects.isNull(request.getStaffId())){
            DepartmentCacheFactory.DepartmentCache cache = departmentCacheFactory.getCache(staff.getOrganizeId().intValue());
            staffDepartmentIds = cache.getAllChildrenIds(Lists.newArrayList(staff.getDepartmentId()));
            staffDepartmentIds.add(staff.getDepartmentId());
        }

        List<GetHistoryVideoListDto> list = new ArrayList<>();
        IdPageDto<GetHistoryVideoListDto> result = new IdPageDto<>();
        result.setList(list);
        result.setTotal(historyVideoListPcCount(request, tester, staffDepartmentIds));
        Map<Integer, String> appNameMap = difyAppConfManager.list().stream().collect((Collectors.toMap(DifyAppConf::getId, DifyAppConf::getName)));


        List<AiGenerateTask> historyVideoList;
        if (CollectionUtils.isEmpty(request.getIds())) {
            historyVideoList = pc ? historyVideoListPc(request, tester, staff.getOrganizeId(), staffDepartmentIds) : historyVideoList(request);
        } else {
            historyVideoList = Lists.newArrayList(aiGenerateTaskManager.listByIds(request.getIds()));
        }

        if (CollectionUtils.isEmpty(historyVideoList)) {
            IdPageDto<GetHistoryVideoListDto> idPageDto = new IdPageDto<>();
            idPageDto.setList(new ArrayList<>());
            idPageDto.setTotal(0);
            return BaseResponse.success(idPageDto);
        }

        List<Long> taskIds = historyVideoList.stream().map(AiGenerateTask::getId).collect(Collectors.toList());

        Map<Long, List<AiGenerateVideoOutput>> taskVideoGroup = aiGenerateVideoOutputManager.list(
                Wrappers.<AiGenerateVideoOutput>query()
                        .in(AiGenerateVideoOutput.TASK_ID, taskIds)
        ).stream().collect(Collectors.groupingBy(AiGenerateVideoOutput::getTaskId));

        List<Long> videoIds = taskVideoGroup.values().stream().flatMap(Collection::stream).map(AiGenerateVideoOutput::getId).collect(Collectors.toList());

        Map<Long, AiGenerateTaskRelease> douYinDataMap = aiGenerateTaskReleaseManager.getByIds(videoIds).stream().collect(Collectors.toMap(AiGenerateTaskRelease::getId, Function.identity()));
        Map<Long, AiGenerateTaskIo> taskIoMap = aiGenerateTaskIoManager.list(
                Wrappers.<AiGenerateTaskIo>query().in(AiGenerateTaskIo.ID, taskIds)
        ).stream().collect(Collectors.toMap(AiGenerateTaskIo::getId, Function.identity()));

        Map<Long, String> projectZipUrlMap = videoCreateTaskManager
                .list(
                        Wrappers.<VideoCreateTask>query()
                                .in(VideoCreateTask.TASK_ID, taskIds)
                                .select(VideoCreateTask.TASK_ID, VideoCreateTask.PROJECT_ZIP_URL)
                )
//                .getByIds(taskIds)
                .stream()
                .collect(Collectors.toMap(VideoCreateTask::getTaskId, VideoCreateTask::getProjectZipUrl, (v1, v2) -> StringUtils.isEmpty(v1) ? v2 : v1));

        Map<Long, Integer> countByGenTaskIdsMap = publishVideoManager.getCountByGenTaskIds(taskIds);

        Map<Long, VideoOceanengineDiagnosis> diagnosisMap = videoOceanengineDiagnosisManager.list(
                        Wrappers.<VideoOceanengineDiagnosis>query()
                                .in(VideoOceanengineDiagnosis.VIDEO_ID, taskIds)
                                .eq(VideoOceanengineDiagnosis.STATUS, 2)
                )
                .stream()
                .collect(Collectors.toMap(VideoOceanengineDiagnosis::getVideoId, Function.identity()));

        for (AiGenerateTask aiGenerateTask : historyVideoList) {
            AiGenerateTaskIo taskIo = taskIoMap.get(aiGenerateTask.getId());
            String inputs = taskIo.getInputs();
            Optional<AiGenerateVideoOutput> taskVideo = taskVideoGroup.getOrDefault(aiGenerateTask.getId(), Lists.newArrayList()).stream().findFirst();

            GetHistoryVideoListDto video = new GetHistoryVideoListDto();
            video.setId(aiGenerateTask.getId());
            video.setCreateTime(DateTimeUtils.getMilli(aiGenerateTask.getCreateTime()));
            video.setStatus(aiGenerateTask.getStatus());
            video.setHasPublishTask(countByGenTaskIdsMap.containsKey(aiGenerateTask.getId()));

            video.setReleaseStat(calculateVideoReleaseState(Lists.newArrayList(taskVideo.orElse(null))));
            video.setViews(
                    taskVideo.map(AiGenerateVideoOutput::getId)
                            .map(douYinDataMap::get).map(AiGenerateTaskRelease::getViewCount).orElse(0)
            );

            String imageUrl = taskVideo.map(AiGenerateVideoOutput::getVideoUrl).map(url ->
                    VideoUtil.getThumbUrl(url, "400", "")
            ).orElse(InngkeAppConst.EMPTY_STR);

            video.setVideoUrl(taskVideo.map(AiGenerateVideoOutput::getVideoUrl).orElse(InngkeAppConst.EMPTY_STR));
            video.setVideo1080Url(taskVideo.map(AiGenerateVideoOutput::getVideo1080Url).orElse(InngkeAppConst.EMPTY_STR));
            video.setVideoId(taskVideo.map(AiGenerateVideoOutput::getId).orElse(0L));
            video.setImage(imageUrl);
            video.setVideoNum(StringUtils.isEmpty(video.getVideoUrl()) ? 0 : 1);

            video.setWidth(1080);
            video.setHeigth(1920);

            if (!StringUtils.isEmpty(inputs)) {
                settingCategoryName(video, inputs, appNameMap);
            }

            if (Objects.isNull(video.getReleaseStat())) {
                video.setReleaseStat(DouYinVideoReleaseStateEnum.UN_RELEASE.getCode());
            }

            if (Objects.equals(aiGenerateTask.getStatus(), AiGenerateTaskStatusEnum.FAIL.getCode())) {
                //创作错误
                if (AiErrorCodeEnum.DIGITAL_PERSON_FAIL.getCode().equals(taskIo.getErrorCode())) {
                    video.setErrorMsg(taskIo.getErrorMsg());
                } else {
                    video.setErrorMsg(VideoUtil.replaceDisplayErrorMsg(taskIo.getErrorMsg()));
                }
                video.setErrorCode(taskIo.getErrorCode());
            }

            //有草稿id代表pc创作的 不允许重新创作
            video.setRecreate(Objects.isNull(aiGenerateTask.getDraftId()));

            Optional.ofNullable(projectZipUrlMap.get(aiGenerateTask.getId())).ifPresent(video::setProjectZipUrl);

            video.setCateId(aiGenerateTask.getCateId());

            video.setTitle(
                    taskVideo.map(AiGenerateVideoOutput::getVideoTitle)
                            .filter(StringUtils::isNotEmpty)
                            .orElse(aiGenerateTask.getTitle())
            );

            VideoOceanengineDiagnosis diagnosis = diagnosisMap.get(video.getId());
            if (diagnosis != null) {
                video.setOeDiagnosis(
                        new OeDiagnosisDto()
                                .setIsAdHighQualityMaterial(OeDiagnosisDto.getValue(diagnosis.getIsAdHighQualityMaterial()))
                                .setIsFirstPublishMaterial(OeDiagnosisDto.getValue(diagnosis.getIsFirstPublishMaterial()))
                                .setIsEcpHighQualityMaterial(OeDiagnosisDto.getValue(diagnosis.getIsEcpHighQualityMaterial()))
                                .setIsInefficientMaterial(OeDiagnosisDto.getValue(diagnosis.getIsInefficientMaterial()))
                                .setNotEcpHighQualityReason(diagnosis.getNotEcpHighQualityReason())
                                .setNotAdHighQualityReason(diagnosis.getNotAdHighQualityReason())
                                .setAudioStatus(diagnosis.getAudioStatus())
                                .setAudioRejectReason(diagnosis.getAudioRejectReason())
                );
            } else {
                video.setOeDiagnosis(new OeDiagnosisDto());
            }

            list.add(video);
        }
        result.setList(Optional.of(list).orElse(new ArrayList<>()));

        return BaseResponse.success(result);
    }

    private void settingCategoryName(GetHistoryVideoListDto getHistoryVideoListDto, String inputs, Map<Integer, String> appNameMap) {
        JSONObject jsonObject = jsonService.toObject(inputs, JSONObject.class);
        getHistoryVideoListDto.setVideoKeys(jsonObject.getString("videoKeys"));
        Integer batchCount = jsonObject.getInteger("batchVideoCount");
        Map promptMap = jsonObject.getObject("promptMap", Map.class);
        if (promptMap != null) {
            int appId = Integer.parseInt(Optional.ofNullable(promptMap.get("appId")).orElse("0").toString());
            String appName = appNameMap.getOrDefault(appId, InngkeAppConst.EMPTY_STR);
            getHistoryVideoListDto.setTypeText(appName);


            List<String> categoryNameList = ((List<Map>) promptMap.getOrDefault("categoryIds", Lists.newArrayList()))
                    .stream()
                    .map(map -> Optional.ofNullable(map.get("title")).orElse(InngkeAppConst.EMPTY_STR).toString())
                    .filter(item -> !StringUtils.isEmpty(item))
                    .collect(Collectors.toList());

            String categoryIdStr = promptMap.getOrDefault("categoryId", "0").toString();
            if (StringUtils.isEmpty(categoryIdStr)) {
                categoryIdStr = "0";
            }
            long categoryId = Long.parseLong(categoryIdStr);
            if (categoryId > 0) {
                MaterialCategory materialCategory = materialCategoryManager.getOne(Wrappers.<MaterialCategory>query().eq(MaterialCategory.ID, categoryId).select(MaterialCategory.NAME));
                if (materialCategory != null && !categoryNameList.contains(materialCategory.getName())) {
                    categoryNameList.add(materialCategory.getName());
                }
            }

            getHistoryVideoListDto.setMaterialCategoryNames(categoryNameList);
            int videoType = FormDataUtils.getInt(promptMap, FormDataUtils.FORM_KEY_VIDEO_TYPE, 0);
            getHistoryVideoListDto.setDraftType(videoType == VideoTypeEnum.MUSIC_BEAT_VIDEO.getVideoType() ?
                    VideoDraftTypeEnum.MUSIC_BEAT.getCode() : VideoDraftTypeEnum.STORYBOARD.getCode());
        }
        getHistoryVideoListDto.setVideoTotal(Optional.ofNullable(batchCount).orElse(1));

    }

    @Override
    public BaseResponse<BasePaginationResponse<AiGenerateTaskStatisticResponse>> getAiGenerateTaskStatistic(Long userId, GetAiTaskStatisticPagingRequest getAiTaskStatisticPagingRequest) {
        // 处理部门
        Staff staff = staffService.getStaffByUserId(userId);
        DepartmentCacheFactory.DepartmentCache cache = departmentCacheFactory.getCache(staff.getOrganizeId().intValue());
        Set<Long> allChildrenIdsAndSelfIds = cache.getAllChildrenIdsAndSelfIds(List.of(staff.getDepartmentId()));

        BoolQueryBuilder query = AiGenerateTaskConverter.toSearchTaskQuery(staff.getOrganizeId(), getAiTaskStatisticPagingRequest, allChildrenIdsAndSelfIds);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
                .query(query)
                .from((getAiTaskStatisticPagingRequest.getPageNo() - 1) * getAiTaskStatisticPagingRequest.getPageSize())
                .size(getAiTaskStatisticPagingRequest.getPageSize())
                .trackTotalHits(true);

        String sort = "asc";
        if (getAiTaskStatisticPagingRequest.getOrderType().equals(2)) {
            sort = "desc";
        }

        switch (getAiTaskStatisticPagingRequest.getOrderByFieldType()) {
            case 1:
                searchSourceBuilder.sort(AiGenerateTaskEsDoc.RELEASE_TIME, SortOrder.fromString(sort));
                break;
            case 2:
                searchSourceBuilder.sort(AiGenerateTaskEsDoc.VIEW_COUNT, SortOrder.fromString(sort));
                break;
            case 3:
                searchSourceBuilder.sort(AiGenerateTaskEsDoc.LIKE_COUNT, SortOrder.fromString(sort));
                break;
            case 4:
                searchSourceBuilder.sort(AiGenerateTaskEsDoc.COLLECTION_COUNT, SortOrder.fromString(sort));
                break;
            case 5:
                searchSourceBuilder.sort(AiGenerateTaskEsDoc.COMMENT_COUNT, SortOrder.fromString(sort));
                break;
            case 6:
                searchSourceBuilder.sort(AiGenerateTaskEsDoc.FORWARD_COUNT, SortOrder.fromString(sort));
                break;
            case 7:
                searchSourceBuilder.sort(AiGenerateTaskEsDoc.TASK_CREATE_TIME, SortOrder.fromString(sort));
                break;
        }

        SearchRequest searchRequest = new SearchRequest().indices(AiGenerateTaskEsService.INDEX_NAME).source(searchSourceBuilder);

        SearchResponse searchResponse;
        try {
            logger.info("search searchClientFromEs es:{}", searchRequest.source());
            searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            throw new InngkeServiceException(e);
        }

        BasePaginationResponse<AiGenerateTaskStatisticResponse> response = new BasePaginationResponse<>();
        response.setTotal(0);

        TotalHits totalHits = searchResponse.getHits().getTotalHits();
        if (totalHits == null || totalHits.value == 0) {
            response.setList(Lists.newArrayList());
            return BaseResponse.success(response);
        }
        response.setTotal((int) totalHits.value);
        response.setList(fillData(AiGenerateTaskConverter.toAiGenerateTaskStatisticResponse(searchResponse)));

        return BaseResponse.success(response);

    }

    private List<AiGenerateTaskStatisticResponse> fillData(List<AiGenerateTaskStatisticResponse> aiGenerateTaskStatisticResponses) {
        List<Long> userIds = aiGenerateTaskStatisticResponses.stream()
                .map(AiGenerateTaskStatisticResponse::getUserId).collect(Collectors.toList());

        Map<Integer, DifyAppConfDto> difyAppConfMapByByProductId = difyAppConfService
                .getDifyAppConfMapByByProductId(AiProductIdEnum.XIAO_HOME_SHU.getType());

        StaffUserRelationService.StaffUserRelation staffUserRelation = staffUserRelationService.initFromUserIds(userIds);

        aiGenerateTaskStatisticResponses.forEach(item -> {
            User user = staffUserRelation.getUser(item.getUserId());
            if (Objects.nonNull(user)) {
                item.setUserName(user.getRealName());
            }
            Department userDepartment = staffUserRelation.getUserDepartment(item.getUserId());
            if (Objects.nonNull(userDepartment)) {
                item.setDepartmentName(userDepartment.getName());
            }
            DifyAppConfDto difyAppConfDto = difyAppConfMapByByProductId.get(item.getXiaoHongShuNoteId());
            if (Objects.nonNull(difyAppConfDto)) {
                item.setXiaoHongShuNoteType(difyAppConfDto.getTitle());
            }

        });

        return aiGenerateTaskStatisticResponses;
    }

    private Integer calculateVideoReleaseState(List<AiGenerateVideoOutput> videos) {
        List<Integer> stateList = videos.stream().filter(Objects::nonNull).map(AiGenerateVideoOutput::getReleaseState).collect(Collectors.toList());

        if (stateList.contains(DouYinVideoReleaseStateEnum.RELEASED.getCode())) {
            return DouYinVideoReleaseStateEnum.RELEASED.getCode();
        }
        if (stateList.contains(DouYinVideoReleaseStateEnum.IN_RELEASE.getCode())) {
            return DouYinVideoReleaseStateEnum.IN_RELEASE.getCode();
        }

        return DouYinVideoReleaseStateEnum.UN_RELEASE.getCode();
    }

    private List<AiGenerateTask> historyVideoListPc(AiGenerateHistoryRequest request, Boolean tester, Long organizeId, Set<Long> departmentIds) {
        List<Long> organizeIds = Lists.newArrayList(organizeId);
        if (tester) {
            organizeIds.add(1L);
        }

        return aiGenerateTaskDao.historyVideoListPc(
                organizeIds,
                request.getStaffId(),
                departmentIds,
                request.getCreateTimeStart(),
                request.getCreateTimeEnd(),
                AiProductIdEnum.VIDEO_CROP_MATERIAL.getType(),
                request.getCateId(),
                request.getKeyword(),
                request.getLastId(),
                request.getPageSize(),
                request.getOcDiagnosisState()
        );
    }

    private Integer historyVideoListPcCount(AiGenerateHistoryRequest request, Boolean tester, Set<Long> departmentIds) {
        List<Long> organizeIds = Lists.newArrayList(userManager.getUserOrganizeId(request.getUserId()));
        if (tester) {
            organizeIds.add(1L);
        }
        return aiGenerateTaskDao.historyVideoListPcCount(
                organizeIds,
                request.getStaffId(),
                departmentIds,
                request.getCreateTimeStart(),
                request.getCreateTimeEnd(),
                AiProductIdEnum.VIDEO_CROP_MATERIAL.getType(),
                request.getCateId(),
                request.getKeyword(),
                request.getOcDiagnosisState()
        );
    }

    private QueryWrapper<AiGenerateTask> getVideoListQuery(AiGenerateHistoryRequest request, Boolean tester) {
        List<Long> organizeIds = Lists.newArrayList(userManager.getUserOrganizeId(request.getUserId()));
        if (tester) {
            organizeIds.add(1L);
        }
        return Wrappers.<AiGenerateTask>query()
                .in(AiGenerateTask.ORGANIZE_ID, organizeIds)
                .eq(Objects.nonNull(request.getStaffId()), AiGenerateTask.STAFF_ID, request.getStaffId())
                .gt(StringUtils.isNotEmpty(request.getCreateTimeStart()), AiGenerateTask.CREATE_TIME, request.getCreateTimeStart())
                .lt(StringUtils.isNotEmpty(request.getCreateTimeEnd()), AiGenerateTask.CREATE_TIME, request.getCreateTimeEnd())
                .eq(AiGenerateTask.AI_PRODUCT_ID, AiProductIdEnum.VIDEO_CROP_MATERIAL.getType())
                .like(Objects.nonNull(request.getCateId()) && request.getCateId() != 0L, AiGenerateTask.CATE_IDS, InngkeAppConst.COMMA_STR + request.getCateId() + InngkeAppConst.COMMA_STR)
                .like(StringUtils.isNotEmpty(request.getKeyword()), AiGenerateTask.TITLE, request.getKeyword());
    }

    private List<AiGenerateTask> historyVideoList(AiGenerateHistoryRequest request) {
        Long userId = request.getUserId();
        Integer size = request.getPageSize();
        Long pageId = request.getLastId();
        QueryWrapper<AiGenerateTask> queryWrapper = new QueryWrapper<AiGenerateTask>()
                .eq(Objects.nonNull(userId), AiGenerateTask.USER_ID, userId)
                .eq(Objects.nonNull(request.getStaffId()), AiGenerateTask.STAFF_ID, request.getStaffId())
                .gt(StringUtils.isNotEmpty(request.getCreateTimeStart()), AiGenerateTask.CREATE_TIME, request.getCreateTimeStart())
                .lt(StringUtils.isNotEmpty(request.getCreateTimeEnd()), AiGenerateTask.CREATE_TIME, request.getCreateTimeEnd())
                .eq(AiGenerateTask.AI_PRODUCT_ID, AiProductIdEnum.VIDEO_CROP_MATERIAL.getType())
                .eq(AiGenerateTask.DELETED, false)
                .like(Objects.nonNull(request.getCateId()), AiGenerateTask.CATE_ID, InngkeAppConst.COMMA_STR + request.getCateId() + InngkeAppConst.COMMA_STR)
                .like(StringUtils.isNotEmpty(request.getKeyword()), AiGenerateTask.TITLE, request.getKeyword())
//                .ne(AiGenerateTask.STATUS, AiGenerateTaskStateEnum.FAIL.getState())
                .orderByDesc(AiGenerateTask.ID)
                .last(InngkeAppConst.STR_LIMIT + size);
        if (Objects.nonNull(pageId)) {
            queryWrapper.lt(AiGenerateTask.ID, pageId);
        }
        return aiGenerateTaskManager.list(queryWrapper);
    }


}
