package com.inngke.ai.crm.dto.response.org;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class StaffProductStatisticsDtoDto extends ProductStatisticsDto {


    /**
     * 员工id
     *
     * @demo 1
     */
    private Long staffId;

    /**
     * 员工名称
     *
     * @demo 张三
     */
    private String staffName;

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 总创作次数
     */
    private Integer totalAi;


    public static StaffProductStatisticsDtoDto initDefault() {
        StaffProductStatisticsDtoDto staffProductStatisticsDtoDto = new StaffProductStatisticsDtoDto();
        staffProductStatisticsDtoDto.setStaffId(0L);
        staffProductStatisticsDtoDto.setStaffName("");
        staffProductStatisticsDtoDto.setXiaoHongShu(0);
        staffProductStatisticsDtoDto.setRenderings(0);
        staffProductStatisticsDtoDto.setVideo(0);
        staffProductStatisticsDtoDto.setUserDocQa(0);
        staffProductStatisticsDtoDto.setTime(0L);
        staffProductStatisticsDtoDto.setDate("");
        staffProductStatisticsDtoDto.setDepartmentName("");

        return staffProductStatisticsDtoDto;
    }
}
