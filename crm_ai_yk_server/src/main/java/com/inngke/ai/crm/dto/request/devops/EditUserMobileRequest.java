package com.inngke.ai.crm.dto.request.devops;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class EditUserMobileRequest implements Serializable {

    /**
     * 手机号
     */
    @NotBlank
    private String mobile;

    /**
     * 用户id
     */
    @NotNull
    @Min(value = 0L)
    private String userId;
}
