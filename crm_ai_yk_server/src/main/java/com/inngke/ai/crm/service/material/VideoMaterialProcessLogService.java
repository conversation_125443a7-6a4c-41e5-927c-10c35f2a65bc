package com.inngke.ai.crm.service.material;

import com.inngke.ai.crm.core.CrmServiceConsts;
import com.inngke.common.utils.DateTimeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class VideoMaterialProcessLogService {

    protected static final String PROCESSING_SET_KEY = CrmServiceConsts.CACHE_KEY_PRE + "material:processing:map";

    @Autowired
    private RedisTemplate<String, Long> processingRedisTemplate;


    public void setMaterialProcessing(Long id) {
        processingRedisTemplate.opsForHash().put(PROCESSING_SET_KEY, id, DateTimeUtils.getMilli(LocalDateTime.now()));
    }

    public void setMaterialProcessed(Long id) {
        processingRedisTemplate.opsForHash().delete(PROCESSING_SET_KEY, id);
    }

    public Set<Long> getProcessingIds() {
        Set<Object> keys = processingRedisTemplate.opsForHash().keys(PROCESSING_SET_KEY);
        return keys.stream().map(Object::toString).map(Long::valueOf).collect(Collectors.toSet());
    }

    public LocalDateTime getProcessTime(Long id) {
        Long time = (Long) processingRedisTemplate.opsForHash().get(PROCESSING_SET_KEY, id);
        return DateTimeUtils.millisToLocalDateTime(time);
    }

    public Boolean isProcessing(Long id) {
        return processingRedisTemplate.opsForHash().hasKey(PROCESSING_SET_KEY, id);
    }
}
