package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.service.OceanEngineAccessTokenService;
import com.inngke.ai.crm.service.material.task.VideoOceanEngineDiagnosisTaskHandler;
import com.inngke.common.core.config.sign.ApiSignature;
import com.inngke.common.dto.request.BaseIdsRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.ip.ai.volc.config.VolcEngineTokenConfig;
import com.inngke.ip.ai.volc.dto.response.AccessTokenResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @chapter 巨量引擎
 * @section 巨量引擎
 */
@RestController
@RequestMapping("/api/ai/volc")
public class VolcApiController {
    @Autowired
    private OceanEngineAccessTokenService oceanEngineAccessTokenService;

    @Autowired
    private VideoOceanEngineDiagnosisTaskHandler videoOceanEngineDiagnosisTaskHandler;

    @Autowired
    private VolcEngineTokenConfig volcEngineTokenConfig;

    /**
     * 获取AccessToken
     */
    @ApiSignature(signature = false)
    @GetMapping("/ocean-engine/access-token")
    public BaseResponse<String> getAccessToken() {
        return BaseResponse.success(oceanEngineAccessTokenService.getAccessToken());
    }

    /**
     * 初始化AccessToken
     */
    @ApiSignature(signature = false)
    @PostMapping("/ocean-engine/access-token")
    public BaseResponse<Boolean> setAccessToken(@RequestBody AccessTokenResponse request) {
        oceanEngineAccessTokenService.setAccessToken(request);
        return BaseResponse.success(true);
    }

    /**
     * 重试首发任务
     */
    @ApiSignature(signature = false)
    @PostMapping("/ocean-engine/retry-diagnosis")
    public BaseResponse<Boolean> retryDiagnosis(@RequestBody BaseIdsRequest request) {
        videoOceanEngineDiagnosisTaskHandler.retryDiagnosis(request.getIds());
        return BaseResponse.success(true);
    }

    /**
     * 获取火山引擎-音频技术Token
     * 本接口为测试、开发环境准备(不要重复生成，避免覆盖)
     */
    @ApiSignature(signature = false)
    @GetMapping("/sami/token")
    public BaseResponse<String> getSamiToken() {
        return BaseResponse.success(volcEngineTokenConfig.getToken());
    }
}
