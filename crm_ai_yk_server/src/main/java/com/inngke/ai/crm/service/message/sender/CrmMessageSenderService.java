package com.inngke.ai.crm.service.message.sender;

import com.inngke.ai.crm.service.message.CrmMessageTypeEnum;
import com.inngke.ai.crm.service.message.content.CrmMessageContext;
import com.inngke.ip.reach.dto.request.TemplateMessageSendRequest;

/**
 * <AUTHOR>
 * @since 2023-09-04 14:06
 **/
public interface CrmMessageSenderService {

    CrmMessageTypeEnum getMessageType();

    void init(CrmMessageContext ctx);

    TemplateMessageSendRequest sendMessage(CrmMessageContext ctx);
}
