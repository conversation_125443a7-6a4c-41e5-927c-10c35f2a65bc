package com.inngke.ai.crm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inngke.ai.crm.db.crm.entity.Article;
import com.inngke.ai.crm.db.crm.manager.ArticleManager;
import com.inngke.ai.crm.dto.request.CrmArticleListRequest;
import com.inngke.ai.crm.dto.response.CrmArticleListDto;
import com.inngke.ai.crm.service.CrmArticleService;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.utils.DateTimeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-12-21 14:31
 **/
@Service
public class CrmArticleServiceImpl implements CrmArticleService {

    @Autowired
    private ArticleManager articleManager;

    @Override
    public BaseResponse<BasePaginationResponse<CrmArticleListDto>> list(CrmArticleListRequest request) {
        BasePaginationResponse<CrmArticleListDto> result = new BasePaginationResponse<>();

        QueryWrapper<Article> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(Article.ENABLE, 1);

        int count = articleManager.count(queryWrapper);

        String limit = " limit " + (request.getPageNo() - 1) * request.getPageSize() + "," + request.getPageSize();
        queryWrapper.orderByDesc(Article.SORT, Article.RELEASE_TIME);
        queryWrapper.last(limit);
        List<Article> list = articleManager.list(queryWrapper);

        result.setTotal(count);
        result.setList(list.stream().map(this::crmArticleListDto).collect(Collectors.toList()));

        return BaseResponse.success(result);
    }

    private CrmArticleListDto crmArticleListDto(Article article) {
        CrmArticleListDto result = new CrmArticleListDto();
        result.setId(article.getId());
        result.setTitle(article.getTitle());
        result.setType(article.getType());
        result.setUrl(article.getUrl());
        result.setReleaseTime(DateTimeUtils.getMilli(article.getReleaseTime()));
        return result;
    }


}
