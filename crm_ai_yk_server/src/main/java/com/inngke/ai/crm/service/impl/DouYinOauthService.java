package com.inngke.ai.crm.service.impl;

import com.inngke.ai.crm.api.browser.DouYinBrowserApi;
import com.inngke.ai.crm.api.browser.dto.DouYinOauthRequest;
import com.inngke.ai.crm.api.browser.dto.SendDouYinOauthCodeRequest;
import com.inngke.ai.crm.client.common.GeoServiceForAi;
import com.inngke.ai.crm.core.CrmServiceConsts;
import com.inngke.ai.crm.dto.request.CrmMobileLoginRequest;
import com.inngke.ai.crm.dto.request.CrmSendMobileCodeRequest;
import com.inngke.ai.crm.service.DouYinService;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.Lock;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.LockService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

@Service
public class DouYinOauthService {
    private static final String SEND_CODE_LOCK_KEY = CrmServiceConsts.CACHE_LOCK_KEY_PRE + InngkeAppConst.CLN_STR + "send:dy:oauth:code:";
    private static final String OAUTH_LOCK_KEY = CrmServiceConsts.CACHE_LOCK_KEY_PRE + InngkeAppConst.CLN_STR + "dy:oauth:";
    @Resource
    private DouYinBrowserApi douYinBrowserApi;
    @Resource
    private DouYinService douYinService;
    @Resource
    private LockService lockService;
    @Resource
    private GeoServiceForAi geoServiceForAi;

    public Boolean sendCode(JwtPayload jwtPayload, CrmSendMobileCodeRequest request) {
        Lock lock = lockService.getLock(SEND_CODE_LOCK_KEY + request.getMobile(), 60);
        if (Objects.isNull(lock)) {
            throw new InngkeServiceException("请稍后再试");
        }

        try {
            String oauthUrl = douYinService.getAuthLinkNotMessage(jwtPayload.getCid(), InngkeAppConst.EMPTY_STR, InngkeAppConst.EMPTY_STR);

            SendDouYinOauthCodeRequest sendCodeRequest = new SendDouYinOauthCodeRequest();
            sendCodeRequest.setUserId(jwtPayload.getCid());
            sendCodeRequest.setMobile(request.getMobile());
            sendCodeRequest.setOauthUrl(oauthUrl);
            sendCodeRequest.setProvinceId(geoServiceForAi.ipGetProvinceId(request.getIp()));
            BaseResponse<Boolean> response = douYinBrowserApi.sendDouYinOauthCode(sendCodeRequest);

            if (!BaseResponse.responseSuccessWithNonNullData(response)) {
                throw new InngkeServiceException("获取验证码失败");
            }

            return response.getData();
        } finally {
            lock.unlock();
        }
    }

    public Boolean oauth(JwtPayload jwtPayload, CrmMobileLoginRequest request) {
        Lock lock = lockService.getLock(OAUTH_LOCK_KEY + request.getMobile(), 60);
        if (Objects.isNull(lock)) {
            throw new InngkeServiceException("请稍后再试");
        }

        try {
            String oauthUrl = douYinService.getAuthLinkNotMessage(jwtPayload.getCid(), request.getMobile(), InngkeAppConst.EMPTY_STR);

            DouYinOauthRequest sendCodeRequest = new DouYinOauthRequest();
            sendCodeRequest.setUserId(jwtPayload.getCid());
            sendCodeRequest.setMobile(request.getMobile());
            sendCodeRequest.setOauthUrl(oauthUrl);
            sendCodeRequest.setCode(request.getCode());
            sendCodeRequest.setProvinceId(geoServiceForAi.ipGetProvinceId(request.getIp()));
            BaseResponse<Boolean> response = douYinBrowserApi.douYinOauth(sendCodeRequest);

            if (!BaseResponse.responseSuccessWithNonNullData(response)) {
                throw new InngkeServiceException("授权失败:" + response.getMsg());
            }

            return response.getData();
        } finally {
            lock.unlock();
        }
    }
}
