package com.inngke.ai.crm.service;

import com.inngke.ai.crm.dto.IdPageDto;
import com.inngke.ai.crm.dto.request.*;
import com.inngke.ai.crm.dto.response.*;
import com.inngke.common.dto.response.BaseResponse;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-08-29 11:12
 **/
public interface CrmUserService {

    BaseResponse<CrmUserLoginDto> login(CrmUserLoginRequest request);

    BaseResponse<CrmUserMobileDto> mobile(CrmUserMobileRequest request);

    BaseResponse<UserInfoDto> userInfo(UserInfoRequest request);

    BaseResponse<IdPageDto<UserCoinRecordDto>> userCoinRecord(UserCoinRecordRequest request);

    BaseResponse<LoginConfigDto> config(LoginConfigRequest request);

    BaseResponse<UserInfoDto> updateUserInfo(UpdateUserInfoRequest request);

    BaseResponse<Boolean> invited(UserInvitedRequest request);

    BaseResponse<CrmUserLoginDto> loginTest(CrmUserLoginTestRequest request);

    BaseResponse<Boolean> manualCoin(CrmManualCoinRequest request);

    BaseResponse<List<StatisticTop10Dto>> getTop10Data(StatisticTop10Request request);

    void setSessionKey(int bid, Long cid, Integer tripartite, String sessionKey);

    String getSessionKey(int bid, Long cid, Integer tripartite);

    /**
     * 添加IP白名单
     *
     * @param ips ip列表，取第一个为远端地址
     */
    void addIpWhiteList(String ips);
}
