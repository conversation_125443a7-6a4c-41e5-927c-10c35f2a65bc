package com.inngke.ai.crm.api.browser.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class DouYinStatisticsDto implements Serializable {
    @JsonProperty("comment_count")
    private Integer commentCount;

    @JsonProperty("digg_count")
    private Integer diggCount;

    @JsonProperty("play_count")
    private Integer playCount;

    @JsonProperty("share_count")
    private Integer shareCount;

    @JsonProperty("collect_count")
    private Integer collectCount;
}
