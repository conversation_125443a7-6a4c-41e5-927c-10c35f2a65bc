package com.inngke.ai.crm.service;

import com.inngke.ai.crm.dto.response.video.JianyingResourceSimpleDto;

import java.util.List;

public interface JianyingResourceService {
    /**
     * 获取文本样式
     * 1. 先从 jianying_resource中查询出material_type=text的记录
     * 2. 再从 app_config中查询出 video.jy.resource.{type}.excludes记录
     * 3. 排除掉不需要的返回
     *
     * @param type 类型：subtitle=字幕 bigTitle=大字报 cover=封面
     */
    List<JianyingResourceSimpleDto> getTextResources(String type);
}
