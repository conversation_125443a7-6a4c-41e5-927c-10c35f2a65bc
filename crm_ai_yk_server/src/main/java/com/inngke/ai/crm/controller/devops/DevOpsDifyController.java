package com.inngke.ai.crm.controller.devops;

import com.inngke.ai.crm.dto.request.devops.AddXhsAppConfigRequest;
import com.inngke.ai.crm.dto.request.devops.GetAppConfigRequest;
import com.inngke.ai.crm.dto.response.devops.DifyAppConfDto;
import com.inngke.ai.crm.service.devops.DevOpsXiaoHongShuService;
import com.inngke.ai.crm.service.schedule.XiaoHongShuSchedule;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @chapter DevOps
 * @section dify应用管理
 */
@RestController
@RequestMapping("/api/ai/devops/dify")
public class DevOpsDifyController {

    @Autowired
    private DevOpsXiaoHongShuService devOpsXiaoHongShuService;

    /**
     * 获取应用配置
     */
    @GetMapping("/{organizeId:\\d+}/config/")
    public BaseResponse<List<DifyAppConfDto>> getAppConfig(GetAppConfigRequest request) {
        return BaseResponse.success(devOpsXiaoHongShuService.getAppConfig(request));
    }

    /**
     * 添加应用配置
     */
    @PostMapping("/{organizeId:\\d+}/config/")
    public BaseResponse<DifyAppConfDto> addAppConfig(@RequestBody AddXhsAppConfigRequest request) {
        return BaseResponse.success(devOpsXiaoHongShuService.saveAppConfig(request));
    }

    /**
     * 添加应用配置
     */
    @DeleteMapping("/{id:\\d+}/config/")
    public BaseResponse<Boolean> deleteConfig(@PathVariable Integer id) {
        return BaseResponse.success(devOpsXiaoHongShuService.deleteConfig(id));
    }

}
