package com.inngke.ai.crm.dto.request.devops;

import com.inngke.common.dto.request.BasePageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class GetDetectionRequest extends BasePageRequest {

    /**
     * 目录id
     */
    private Long dirId;

    /**
     * 状态 -1:不通过 0:待审核 1:待构建 2:构建成功
     */
    private Integer status;

    /**
     * 素材id
     */
    private Long materialId;
}
