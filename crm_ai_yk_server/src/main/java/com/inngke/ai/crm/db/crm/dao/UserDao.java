package com.inngke.ai.crm.db.crm.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.inngke.ai.crm.db.crm.entity.User;
import com.inngke.ai.crm.dto.response.StatisticTop10Dto;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 用户表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-29
 */
public interface UserDao extends BaseMapper<User> {

    @Select("<script>" +
            "SELECT * from (" +
            "SELECT " +
            "user.id user_id ," +
            "user.real_name user_name," +
            "staff.department_id department_id," +
            "<if test='orderByFieldType == 1'>" +
            " count(release_task.`status` = 1) " +
            "</if>" +
            "<if test='orderByFieldType == 2'>" +
            " sum(release_task.view_count) " +
            "</if>" +
            "<if test='orderByFieldType == 3'>" +
            " sum(release_task.comment_count) " +
            "</if>" +
            "<if test='orderByFieldType == 4'>" +
            " sum(release_task.like_count) " +
            "</if>" +
            " count " +
            "FROM `user` user INNER join ai_generate_task task on user.id = task.user_id INNER JOIN ai_generate_task_release release_task on task.id = release_task.ai_generate_task_id " +
            "LEFT JOIN staff on user.id = staff.user_id and staff.state = 1 " +
            "WHERE user.organize_id = #{organizeId} and task.organize_id = #{organizeId} and release_task.type = 2 and release_task.status = 1 and " +
            "release_task.release_time BETWEEN #{startTime} and #{endTime} " +
            "GROUP BY user.id ) a ORDER BY a.count " +
            "<if test='orderType == 1'>" +
            " asc " +
            "</if>" +
            "<if test='orderType == 2'>" +
            " desc " +
            "</if>" +
            "limit 10" +
            "</script>")
    List<StatisticTop10Dto> getTop10Data(@Param("organizeId") Long organizeId,
                                         @Param("startTime") LocalDateTime startTime,
                                         @Param("endTime") LocalDateTime endTime,
                                         @Param("orderByFieldType") Integer orderByFieldType,
                                         @Param("orderType")  Integer orderType);

}
