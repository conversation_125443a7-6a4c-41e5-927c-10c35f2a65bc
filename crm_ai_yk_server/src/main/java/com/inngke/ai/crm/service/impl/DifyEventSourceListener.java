package com.inngke.ai.crm.service.impl;

import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.TraceUtils;
import com.inngke.common.service.JsonService;
import com.inngke.ip.ai.dify.dto.response.DifyResponse;
import okhttp3.Response;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

public class DifyEventSourceListener extends EventSourceListener {
    private static final Logger logger = LoggerFactory.getLogger(DifyEventSourceListener.class);
    private Consumer<DifyResponse> eventHandle;
    private BiConsumer<Response, Throwable> errorHandle;
    private String traceId;

    private final AtomicInteger seq = new AtomicInteger(0);

    @Autowired
    private JsonService jsonService;

    public DifyEventSourceListener(Consumer<DifyResponse> eventHandle, BiConsumer<Response, Throwable> errorHandle) {
        this.eventHandle = eventHandle;
        this.errorHandle = errorHandle;
        this.traceId = TraceUtils.getOrCreate();
    }

    @Override
    public void onOpen(EventSource eventSource, okhttp3.Response response) {
        logger.info("sse onOpen: {}", response);
        super.onOpen(eventSource, response);
    }

    @Override
    public void onEvent(EventSource eventSource, String id, String type, String data) {
        if (StringUtils.isEmpty(data)) {
            logger.warn("sse data is empty");
            return;
        }
        TraceUtils.setTraceId(traceId);
        logger.info("[{}]sse event: {}", String.format("%04d", seq.addAndGet(1)), data);
        DifyResponse difyResponse = jsonService.toObject(data, DifyResponse.class);
        if (eventHandle != null) {
            eventHandle.accept(difyResponse);
        }
    }

    @Override
    public void onFailure(EventSource eventSource, Throwable t, okhttp3.Response response) {
        if (errorHandle == null) {
            logger.warn("sse error", t);
            return;
        }
        errorHandle.accept(response, t);
    }

    @Override
    public void onClosed(EventSource eventSource) {
        if (eventHandle != null) {
            DifyResponse difyResponse = new DifyResponse();
            difyResponse.setAnswer(InngkeAppConst.EMPTY_STR);
            eventHandle.accept(difyResponse);
        } else {
            logger.warn("sse onClosed");
        }
    }
}
