package com.inngke.ai.crm.service;

import com.inngke.ai.crm.dto.request.BaseUserId;
import com.inngke.ai.crm.dto.request.OrgCoinDistributeLogRequest;
import com.inngke.ai.crm.dto.request.StaffXiaoHongShuStatisticsRequest;
import com.inngke.ai.crm.dto.request.common.GetDouYinDataRequest;
import com.inngke.ai.crm.dto.request.org.*;
import com.inngke.ai.crm.dto.response.OrgCoinDistributeLogDto;
import com.inngke.ai.crm.dto.response.StaffXiaoHongShuStatisticsDto;
import com.inngke.ai.crm.dto.response.org.*;
import com.inngke.ai.crm.dto.response.video.DouYinDataDto;
import com.inngke.ai.crm.dto.response.video.DouYinStatisticalDataDto;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;

import java.util.List;

public interface OrganizeService {

    BaseResponse<OrganizeAccountInfoDto> getAccountInfo(BaseUserId request);

    BaseResponse<BasePaginationResponse<OrganizeDistributionLogDto>> getDistributionLogPaging(GetOrgDistributionLogPagingRequest request);

    BaseResponse<List<ProductStatisticsDto>> getProductStatistics(GetProductStatistics request);

    BaseResponse<List<StaffProductStatisticsDtoDto>> getStaffProductStatistics(GetStaffProductStatistics request);

    BaseResponse<BasePaginationResponse<StaffXiaoHongShuStatisticsDto>> getXiaoHongShuStatistics(StaffXiaoHongShuStatisticsRequest request);

    BaseResponse<BasePaginationResponse<OrgCoinDistributeLogDto>> orgCoinDistributeLog(OrgCoinDistributeLogRequest request);

    BaseResponse<BasePaginationResponse<DouYinStatisticalDataDto>> getDouYinData(GetDouYinDataRequest request);

    /**
     * 是否开启视频创作
     *
     * @param organizeId 企业ID
     */
    boolean isVideoOpened(Long organizeId);
}
