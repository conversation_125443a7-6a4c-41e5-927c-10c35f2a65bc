package com.inngke.ai.crm.service;

import com.inngke.ai.crm.dto.request.video.*;
import com.inngke.ai.crm.dto.response.video.VideoCreateResult;
import com.inngke.ai.crm.dto.response.video.VideoCreateStageResponse;
import com.inngke.ai.dto.VideoUserScriptDto;
import com.inngke.ai.dto.request.VideoCreateInfoRequest;
import com.inngke.ai.dto.request.VideoGenerateRequest;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;

import java.util.List;

public interface VideoCreateService {

    /**
     * 通过素材库创建视频
     *
     * @param jwtPayload 当前用户
     * @param request    视频创建请求
     * @return AI视频创建任务
     */
    VideoCreateResult createByMaterial(JwtPayload jwtPayload, VideoCreateWithMaterialRequest request, String referer,boolean mashUp);

    VideoCreateResult createByMashupVideo(JwtPayload jwtPayload, VideoCreateWithVideoMashupRequest request);

    VideoGenerateRequest getVideoGenerateRequestByTaskId(Long taskId);

    /**
     * 获取任务信息
     *
     * @param jwtPayload 当前用户
     * @param taskId     任务ID
     * @return 任务状态信息
     */
    String getTaskInfo(JwtPayload jwtPayload, long taskId);

    BaseResponse<Boolean> updateVideoCreateStatus(VideoCreateInfoRequest request);

    /**
     * 创建视频脚本
     */
    BaseResponse<List<VideoUserScriptDto>> createVideoScript(CreateVideoScriptRequest request);

    /**
     * 获取视频创作阶段状态
     */
    BaseResponse<VideoCreateStageResponse> getTaskCreationStage(JwtPayload jwtPayload, VideoTaskCreationStageRequest request);

    /**
     * 获取任务输入
     */
    VideoCreateWithMaterialRequest getTaskInputs(JwtPayload jwtPayload, long taskId);

    VideoCreateStageResponse scriptMaterial(JwtPayload jwtPayload, VideoCreateWithMaterialRequest request);

    VideoCreateStageResponse rematchScriptMaterial(JwtPayload jwtPayload, RematchScriptMaterialRequest request);

    Boolean retry(long taskId);
}
