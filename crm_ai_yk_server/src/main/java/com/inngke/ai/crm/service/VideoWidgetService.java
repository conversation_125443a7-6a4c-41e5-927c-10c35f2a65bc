package com.inngke.ai.crm.service;

import com.inngke.ai.crm.dto.request.video.CreateAiWidgetTemplateRequest;
import com.inngke.ai.crm.dto.request.video.WidgetSaveRequest;
import com.inngke.ai.crm.dto.request.video.WidgetTemplateQuery;
import com.inngke.ai.crm.dto.response.video.WidgetDetail;
import com.inngke.ai.crm.dto.response.video.WidgetEditConfigDto;
import com.inngke.ai.crm.dto.response.video.WidgetTemplateListItem;
import com.inngke.common.dto.JwtPayload;

import java.util.List;

public interface VideoWidgetService {
    WidgetEditConfigDto getWidgetEditConfig(JwtPayload jwtPayload);

    List<WidgetTemplateListItem> listWidgetTemplates(JwtPayload jwtPayload, WidgetTemplateQuery request);

    WidgetTemplateListItem save(JwtPayload jwtPayload, WidgetSaveRequest request);

    WidgetDetail getDetail(JwtPayload jwtPayload, long id);

    WidgetDetail createAiWidgetTemplate(JwtPayload jwtPayload, CreateAiWidgetTemplateRequest request);

    void delete(JwtPayload jwtPayload, long id);
}
