package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.dto.request.video.ScriptCategorySetRequest;
import com.inngke.ai.crm.dto.request.video.VideoCreateWithMaterialRequest;
import com.inngke.ai.crm.dto.request.video.VideoScriptRequest;
import com.inngke.ai.crm.dto.request.video.VideoScriptSaveRequest;
import com.inngke.ai.crm.dto.response.video.VideoScriptDetailDto;
import com.inngke.ip.ai.dify.app.dto.VideoScriptItem;
import com.inngke.ai.crm.dto.response.video.VideoScriptDto;
import com.inngke.ai.crm.service.VideoScriptService;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.request.BaseIdsRequest;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @chapter 视频
 * @section 脚本
 */
@RestController
@RequestMapping("/api/ai/video-script")
public class VideoScriptApiController {

    @Autowired
    private VideoScriptService videoScriptService;

    /**
     * 生成脚本
     *
     * @param jwtPayload 当前用户
     * @param request    表单请求
     */
    @PostMapping("/create")
    public BaseResponse<List<VideoScriptItem>> create(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody VideoCreateWithMaterialRequest request
    ) {
        return BaseResponse.success(videoScriptService.create(jwtPayload, request));
    }

    /**
     * 脚本列表
     *
     * @param request 筛选请求
     */
    @GetMapping("/list")
    public BaseResponse<BasePaginationResponse<VideoScriptDto>> list(
            @RequestAttribute JwtPayload jwtPayload,
            VideoScriptRequest request
    ) {

        return BaseResponse.success(videoScriptService.list(jwtPayload, request));
    }

    /**
     * 批量保存脚本库
     */
    @PostMapping
    public BaseResponse<Boolean> saveVideoScripts(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody VideoScriptSaveRequest request
    ) {
        return BaseResponse.success(videoScriptService.saveVideoScripts(jwtPayload, request));
    }

    /**
     * 脚本详情
     */
    @GetMapping("/{id:\\d+}/detail")
    public BaseResponse<VideoScriptDetailDto> getScriptDetail(@RequestAttribute JwtPayload jwtPayload, @PathVariable Long id) {
        return BaseResponse.success(videoScriptService.getDetail(jwtPayload, id));
    }

    /**
     * 批量移动目录
     */
    @PutMapping("/category/set")
    public BaseResponse<Boolean> categorySet(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody @Validated ScriptCategorySetRequest request) {
        return BaseResponse.success(videoScriptService.categorySet(jwtPayload, request));
    }

    /**
     * 批量删除
     */
    @DeleteMapping("/batch")
    public BaseResponse<Boolean> deleteBatch(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody @Validated BaseIdsRequest request) {
        return BaseResponse.success(videoScriptService.deleteBatch(jwtPayload, request));
    }

    /**
     * 通过脚本快速创作
     *
     * @param scriptId 脚本ID
     * @return 创作草稿ID
     */
    @PostMapping("/{scriptId:\\d+}/create-draft")
    public BaseResponse<Long> quickCreateDraft(
            @RequestAttribute JwtPayload jwtPayload,
            @PathVariable long scriptId
    ) {
        return BaseResponse.success(videoScriptService.createDraft(jwtPayload, scriptId));
    }
}
