package com.inngke.ai.crm.controller.devops;

import com.inngke.ai.crm.dto.request.base.UserIdRequest;
import com.inngke.ai.crm.dto.request.devops.*;
import com.inngke.ai.crm.dto.response.devops.UserDto;
import com.inngke.ai.crm.service.devops.UserService;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * @chapter DevOps
 * @section 用户管理
 */
@RestController
@RequestMapping("/api/ai/devops/user")
public class DevOpsUserController {

    @Autowired
    private UserService userService;

    /**
     * 获取用户列表
     */
    @GetMapping("/list")
    public BaseResponse<BasePaginationResponse<UserDto>> getUserList(GetUserListRequest request) {
        return BaseResponse.success(userService.getUserList(request));
    }

    /**
     * 保存用户信息
     */
    @PutMapping
    public BaseResponse<UserDto> saveUser(@RequestBody SaveUserRequest request) {
        return BaseResponse.success(userService.saveUser(request));
    }

    /**
     * 添加积分
     */
    @PostMapping("/coin")
    public BaseResponse<Boolean> addCoin(@RequestBody AddCoinRequest request) {
        return BaseResponse.success(userService.addCoin(request));
    }

    @PostMapping("transfer")
    public BaseResponse<Boolean> transfer(@Validated @RequestBody TransferUserRequest request){
        return BaseResponse.success(userService.transfer(request));
    }

    @PutMapping("mobile")
    public BaseResponse<Boolean> editMobile(@Validated @RequestBody EditUserMobileRequest request){
        return BaseResponse.success(userService.editMobile(request));
    }

    @DeleteMapping("/{userId:\\d+}/xhs/account")
    public BaseResponse<Boolean> unbindUserXhs(@PathVariable Long userId){
        UserIdRequest request = new UserIdRequest();
        request.setUserId(userId);

        return BaseResponse.success(userService.unbindXhs(request));
    }

    /**
     * 为用户添加manager权限
     */
    @PutMapping("/{id}/permission")
    public BaseResponse<Boolean> addPermission(@PathVariable Long id) {
        return BaseResponse.success(userService.addPermission(id));
    }

    /**
     * 回收用户积分
     */
    @PutMapping("/{id}/collect-points")
    public BaseResponse<Boolean> collectPoints(@PathVariable Long id){
        return BaseResponse.success(userService.collectPoints(id));
    }
}
