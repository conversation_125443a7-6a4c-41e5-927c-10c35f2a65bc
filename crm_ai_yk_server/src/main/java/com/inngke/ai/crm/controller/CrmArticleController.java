package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.dto.request.CrmArticleListRequest;
import com.inngke.ai.crm.dto.response.CrmArticleListDto;
import com.inngke.ai.crm.service.CrmArticleService;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @chapter 学习中心
 * @section 学习中心
 * @since 2023-12-20 13:52
 **/
@RestController
@RequestMapping("/api/ai/event-notify")
public class CrmArticleController {

    @Autowired
    private CrmArticleService articleService;


    /**
     * 学习中心列表
     */
    @GetMapping
    public BaseResponse<BasePaginationResponse<CrmArticleListDto>> list(CrmArticleListRequest request,
                                                                        @RequestAttribute JwtPayload jwtPayload) {
        request.setUserId(jwtPayload.getCid());
        return articleService.list(request);
    }


}
