package com.inngke.ai.crm.dto.response.media;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/4/17 17:42
 */
@Data
@Accessors(chain = true)
public class VideoUnderstandingContentResponse implements Serializable {

    /**
     * 视频理解内容状态 -1 出错，0 理解中 ，1理解成功。
     */
    private Integer status;

    /**
     * 视频理解内容
     */
    private String content;

}
