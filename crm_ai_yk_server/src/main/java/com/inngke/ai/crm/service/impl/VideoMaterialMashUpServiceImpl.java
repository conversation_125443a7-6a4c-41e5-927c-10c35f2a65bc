package com.inngke.ai.crm.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.inngke.ai.crm.core.CrmServiceConsts;
import com.inngke.ai.crm.db.crm.entity.*;
import com.inngke.ai.crm.db.crm.manager.*;
import com.inngke.ai.crm.dto.enums.AppConfigCodeEnum;
import com.inngke.ai.crm.dto.form.SelectOption;
import com.inngke.ai.crm.dto.request.video.UpdateMashUpTaskStatusRequest;
import com.inngke.ai.crm.dto.request.video.VideoCreateWithMaterialRequest;
import com.inngke.ai.crm.dto.request.video.VideoMashUpCountRequest;
import com.inngke.ai.crm.dto.request.video.VideoMashUpCreateRequest;
import com.inngke.ai.crm.dto.response.video.MashUpConfigDto;
import com.inngke.ai.crm.dto.response.video.VideoCreateResult;
import com.inngke.ai.crm.dto.response.video.VideoProjectDraftDetail;
import com.inngke.ai.crm.service.*;
import com.inngke.ai.dto.MaterialCategoryDto;
import com.inngke.ai.dto.VideoMaterialItem;
import com.inngke.ai.dto.VideoUserScriptDto;
import com.inngke.ai.dto.enums.DigitalDisplayEnum;
import com.inngke.ai.dto.request.VideoAudioConfig;
import com.inngke.ai.dto.request.VideoDigitalHumanConfig;
import com.inngke.ai.dto.request.VideoGenerateRequest;
import com.inngke.ai.dto.utils.FormDataUtils;
import com.inngke.ai.dto.widget.WidgetGroup;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.SnowflakeHelper;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.Lock;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.JsonService;
import com.inngke.common.service.LockService;
import com.inngke.common.utils.JsonUtil;
import com.inngke.ip.ai.vector.dto.MaterialInfoDto;
import com.inngke.ip.ai.vector.dto.SearchRequest;
import com.inngke.ip.ai.vector.service.VectorSearchService;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Service
public class VideoMaterialMashUpServiceImpl implements VideoMaterialMashUpService {

    private static final Logger logger = LoggerFactory.getLogger(VideoMaterialMashUpServiceImpl.class);
    private static final Integer MAX_MATERIAL_COUNT = 20;
    private static final Integer ADD_MASH_UP_MATERIAL_COUNT = 5;
    private static final Double MIN_SCORE = 0.1;
    public static final Integer MASH_UP_TYPE_RANDOM = 2;
    private static final String MASH_UP_RECEIVE_TASK_KEY = CrmServiceConsts.CACHE_LOCK_KEY_PRE + "mashUpTask";

    @Autowired
    private VideoCreateService videoCreateService;
    @Autowired
    private MaterialCategoryManager materialCategoryManager;
    @Autowired
    private VideoProjectDraftService videoProjectDraftService;
    @Autowired
    private StaffService staffService;
    @Autowired
    private VectorSearchService vectorSearchService;
    @Autowired
    private JsonService jsonService;
    @Autowired
    private TtsConfigManager ttsConfigManager;
    @Autowired
    private VideoBgmMaterialManager videoBgmMaterialManager;
    @Autowired
    private AppConfigManager appConfigManager;
    @Autowired
    private MashUpTaskManager mashUpTaskManager;
    @Autowired
    private LockService lockService;
    @Autowired
    private AiGenerateTaskIoManager aiGenerateTaskIoManager;
    @Autowired
    private DigitalPersonTemplateManager digitalPersonTemplateManager;


    @Override
    public Integer mashUpCount(VideoMashUpCountRequest request) {
        List<Integer> scriptCounts = Splitter.on(InngkeAppConst.COMMA_STR).splitToList(request.getScriptCounts())
                .stream().map(cStr -> {
                    Integer cInt = Integer.valueOf(cStr);
                    if (cInt < 0) {
                        cInt = null;
                    }
                    return cInt;
                }).collect(Collectors.toList());
        MashUpConfigDto mashUpConfig = getMashUpConfig();
        Optional.ofNullable(request.getNoDuplicatePercent()).ifPresent(mashUpConfig::setNoDuplicatePercent);
        return getMashUpCount(mashUpConfig, scriptCounts, request.getMashUpType());
    }

    @Override
    public Boolean mashUpCreate(JwtPayload jwtPayload, VideoMashUpCreateRequest request) {

        videoProjectDraftService.saveDraft(jwtPayload, request, null);

        MashUpConfigDto mashUpConfig = getMashUpConfig();
        Optional.ofNullable(request.getNoDuplicatePercent()).ifPresent(mashUpConfig::setNoDuplicatePercent);
        Map<String, Object> promptMap = request.getPromptMap();
        Boolean digitalPersonOpen = FormDataUtils.getBoolean(promptMap, FormDataUtils.FORM_KEY_DIGITAL_PERSON_OPEN, false);

        final Integer[] miniMaterialCount = {Integer.MAX_VALUE};
        List<Integer> scriptCounts = request.getScripts().stream().map(script -> {
            //数字人，口播全屏的情况，单个脚本不参与混编计算
            Integer materialCount = Optional.ofNullable(script.getMaterialOriginList()).map(List::size).orElse(0);
            if (materialCount < miniMaterialCount[0]){
                miniMaterialCount[0] = materialCount;
            }
            if (digitalPersonOpen && DigitalDisplayEnum.FULL.getCode().equals(script.getDigitalPersonDisplay())) {
                return null;
            }
            return Optional.ofNullable(script.getMaterialOriginList()).map(List::size).orElse(null);
        }).collect(Collectors.toList());

        long count = scriptCounts.stream().filter(Objects::nonNull).count();

        Integer mashUpCount;
        if (count > 0){
            mashUpCount = getMashUpCount(mashUpConfig, scriptCounts, request.getMashUpType());
        }else {
            mashUpCount = miniMaterialCount[0];
            scriptCounts = Lists.newArrayList();
            for (int i = 0; i < request.getScripts().size(); i++) {
                scriptCounts.add(mashUpCount);
            }
        }

        if (mashUpCount < request.getCount()) {
            throw new InngkeServiceException("参数错误 最大生成数量「" + mashUpCount + "」");
        }

        request.setNoDuplicatePercent(mashUpConfig.getNoDuplicatePercent());

        Long organizeId = staffService.getUserStaffOrganizeId(jwtPayload.getCid());

        List<List<Integer>> weavedMaterialIndex = randomWeaveMaterial((double) request.getNoDuplicatePercent() / 100,
                request.getCount(), scriptCounts, request.getMashUpType()
        );

        logger.info("视频裂变-生成任务:「{}」", jsonService.toJson((Serializable) weavedMaterialIndex));

        List<VideoCreateWithMaterialRequest> mashUpRequestList = buildMashUpTaskRequest(organizeId, request, weavedMaterialIndex);

        List<Long> mashUpTaskIds = mashUpRequestList.stream().map(videoCreateWithMaterialRequest -> {
            videoCreateWithMaterialRequest.setTaskId(SnowflakeHelper.getId());
            videoCreateWithMaterialRequest.setDraftId(request.getDraftId());
            return videoCreateService.createByMaterial(jwtPayload, videoCreateWithMaterialRequest, null, true);
        }).map(VideoCreateResult::getTaskId).collect(Collectors.toList());

        //将视频任务添加到 video_create_task
        boolean submitted = mashUpTaskManager.submitMashUpTask(mashUpTaskIds);

        return true;
    }

    private static List<List<Integer>> randomWeaveMaterial(
            double noDuplicatePercent, Integer count, List<Integer> scriptCount, Integer mashUpType) {

        //不重复的编排
        List<List<Integer>> weaveIndexList = createNoRepeat(scriptCount, mashUpType, count);
        if (CollectionUtils.isEmpty(weaveIndexList) || weaveIndexList.size() == count) {
            return weaveIndexList;
        }

        if (noDuplicatePercent == 0.0) {
            return weaveIndexList;
        }

        int i = 0;
        while (weaveIndexList.size() < count) {
            i++;
            List<Integer> currentWeaveIndex = Lists.newArrayList();
            if (i > 200000) {
                return weaveIndexList;
            }

            for (Integer materialCount : scriptCount) {
                if (materialCount == null) {
                    currentWeaveIndex.add(null);
                    continue;
                }
                currentWeaveIndex.add(RandomUtils.nextInt(0, materialCount));
            }

            boolean pass = true;
            for (List<Integer> weaveIndex : weaveIndexList) {
                double repeatRate = getRepeatRate(weaveIndex, currentWeaveIndex);
                if (repeatRate > noDuplicatePercent) {
                    pass = false;
                    break;
                }
            }

            if (pass) {
                weaveIndexList.add(currentWeaveIndex);
            }
        }

        return weaveIndexList;
    }

    private static double getRepeatRate(List<Integer> list1, List<Integer> list2) {
        // 参数验证
        if (list1 == null || list2 == null) {
            return -1.0;
        }

        int size = list1.size();
        if (size != list2.size() || size == 0) {
            return -1.0;
        }

        // 使用 Java 8 Stream API 计算重复次数
        long repeatCount = IntStream.range(0, size)
                .filter(i -> Objects.equals(list1.get(i), list2.get(i)))
                .count();

        return (double) repeatCount / size;
    }

    @Override
    public VideoProjectDraftDetail addMaterial(JwtPayload jwtPayload, VideoCreateWithMaterialRequest request) {
        Long organizeId = staffService.getUserStaffOrganizeId(jwtPayload.getCid());

        Long draftId = request.getDraftId();

        List<VideoUserScriptDto> scripts = request.getScripts();

        Set<Long> materialIds = scripts.stream().map(VideoUserScriptDto::getMaterialOriginList).flatMap(Collection::stream)
                .map(VideoMaterialItem::getMaterialId).collect(Collectors.toSet());

        Map<String, Object> promptMap = request.getPromptMap();

        String categoryJson = Optional.ofNullable(promptMap.get(FormDataUtils.FORM_KEY_CATEGORY_IDS)).map(
                categoryObj -> jsonService.toJson((Serializable) categoryObj)
        ).orElse(InngkeAppConst.EMPTY_STR);
        Set<Long> categoryIds = Sets.newHashSet();

        if (StringUtils.isNotBlank(categoryJson)) {
            categoryIds = Optional.ofNullable(
                            jsonService.toObjectList(categoryJson, SelectOption.class)).map(Collection::stream)
                    .map(stream -> stream.map(SelectOption::getValue).map(Objects::toString).map(Long::valueOf)
                            .collect(Collectors.toSet())
                    ).orElse(Sets.newHashSet());
        }

        if (CollectionUtils.isEmpty(categoryIds)) {
            categoryIds = materialCategoryManager.getIdsByOrganizeIdType(organizeId, MaterialCategory.TYPE_VIDEO);
        }

        boolean vertical = FormDataUtils.getInt(promptMap, FormDataUtils.FORM_KEY_VERTICAL, 1) != 2;

        Set<Long> finalCategoryIds = categoryIds;

        VideoUserScriptDto videoUserScriptDto = scripts.stream().findFirst().orElseThrow(() -> new InngkeServiceException("暂未生成脚本，本存在"));
        if (videoUserScriptDto.getMaterialOriginList().size() >= MAX_MATERIAL_COUNT) {
            throw new InngkeServiceException("最多添加" + MAX_MATERIAL_COUNT + "个备选素材");
        }

        List<VideoUserScriptDto> addedMaterialScripts = scripts.stream().map(script ->
                addScriptMaterial(script, materialIds, finalCategoryIds, vertical)).collect(Collectors.toList());
        request.setScripts(addedMaterialScripts);

        return videoProjectDraftService.saveDraft(jwtPayload, request, null);
    }

    @Override
    public VideoGenerateRequest receiveMashUpTask() {
        Lock lock = lockService.getLock(MASH_UP_RECEIVE_TASK_KEY, 5);
        if (Objects.isNull(lock)) {
            return null;
        }

        try {
            MashUpTask mashUpTask = mashUpTaskManager.receiveMashUpTask();
            if (Objects.isNull(mashUpTask)) {
                return null;
            }

            return videoCreateService.getVideoGenerateRequestByTaskId(mashUpTask.getTaskId());
        } finally {
            lock.unlock();
        }
    }

    @Override
    public Boolean updateMashUpTaskStatus(UpdateMashUpTaskStatusRequest request) {
        return mashUpTaskManager.updateMashUpTaskStatus(request);
    }

    private VideoUserScriptDto addScriptMaterial(
            VideoUserScriptDto script, Set<Long> materialIds, Set<Long> categoryIds, boolean vertical) {
        SearchRequest request = new SearchRequest();
        request.setAnyCategoryIds(Lists.newArrayList(categoryIds));
        request.setKeywords(Lists.newArrayList(script.getScene(), script.getAside()).stream()
                .filter(StringUtils::isNotBlank).collect(Collectors.toList()));
        request.setDuration(Double.valueOf(Math.ceil(script.getDuration() / 1000.0)).intValue());
        request.setVertical(vertical);
        request.setPageSize(200);
        List<MaterialInfoDto> response = vectorSearchService.search(request);
        List<MaterialInfoDto> result = Lists.newArrayList();

        Map<Long, List<MaterialInfoDto>> materialGroup = response.stream().collect(Collectors.groupingBy(MaterialInfoDto::getMaterialId));
        for (List<MaterialInfoDto> value : materialGroup.values()) {
            result.add(value.get(0));
        }

        List<MaterialInfoDto> filteredMaterial = result.stream()
                .filter(materialItem ->
                        !materialIds.contains(materialItem.getMaterialId()) && materialItem.getScore() >= MIN_SCORE
                ).limit(ADD_MASH_UP_MATERIAL_COUNT).collect(Collectors.toList());

        if (filteredMaterial.size() != ADD_MASH_UP_MATERIAL_COUNT) {
            throw new InngkeServiceException("素材不足，请手动添加素材");
        }

        script.getMaterialOriginList().addAll(result.stream().filter(
                materialItem -> !materialIds.contains(materialItem.getMaterialId())
        ).limit(ADD_MASH_UP_MATERIAL_COUNT).map((materialItem) -> {
            VideoMaterialItem scriptMaterial = toScriptMaterial(materialItem);
            scriptMaterial.setClipStart(scriptMaterial.getOptimal() * 1000);
            scriptMaterial.setClipDuration(script.getDuration());
            return scriptMaterial;
        }).collect(Collectors.toList()));

        //添加到已选素材ids
        script.getMaterialOriginList().stream().map(VideoMaterialItem::getMaterialId).forEach(materialIds::add);

        List<WidgetGroup> widgets = script.getWidgets();
        if (!CollectionUtils.isEmpty(widgets)) {
            WidgetGroup firstWidget = widgets.stream().findFirst().orElse(null);
            while (widgets.size() != script.getMaterialOriginList().size()) {
                widgets.add(firstWidget);
            }
        }

        return script;
    }

    private VideoMaterialItem toScriptMaterial(MaterialInfoDto materialItem) {
        VideoMaterialItem videoMaterialItem = new VideoMaterialItem();
        videoMaterialItem.setMaterialId(materialItem.getMaterialId());
        videoMaterialItem.setDuration(materialItem.getDuration());
        videoMaterialItem.setTags(materialItem.getTags());
        videoMaterialItem.setUrl(materialItem.getUrl());
        videoMaterialItem.setLowQualityUrl(materialItem.getLowQualityUrl());
        videoMaterialItem.setScore(materialItem.getScore());
        videoMaterialItem.setWidth(materialItem.getWidth());
        videoMaterialItem.setHeight(materialItem.getHeight());
        videoMaterialItem.setOptimal(materialItem.getOptimal());
        videoMaterialItem.setEffectiveIntervalSecond(materialItem.getEffectiveIntervalSecond());
        videoMaterialItem.setClipStart(materialItem.getClipStart());
        videoMaterialItem.setClipDuration(materialItem.getClipDuration());
        videoMaterialItem.setCategoryList(materialItem.getCategoryList().stream().map(cate -> {
            MaterialCategoryDto materialCategoryDto = new MaterialCategoryDto();
            materialCategoryDto.setId(cate.getId());
            materialCategoryDto.setName(cate.getName());
            materialCategoryDto.setPid(cate.getPid());
            return materialCategoryDto;
        }).collect(Collectors.toList()));
        videoMaterialItem.setCreateTime(materialItem.getCreateTime());
        return videoMaterialItem;
    }


    private List<VideoCreateWithMaterialRequest> buildMashUpTaskRequest(Long organizeId, VideoMashUpCreateRequest request, List<List<Integer>> weavedMaterialIndex) {

        Map<String, Object> promptMap = request.getPromptMap();

        //获取音色待选列表
        Map<Integer, List<TtsConfig>> genderTtsMap = getRandomGenderTtsMapList(request.getChooseDigitalHumanConfigs(), organizeId);

        //获取背景音乐待选列表
        List<VideoBgmMaterial> bgmMaterialList = getRandomMusicList(request.getPromptMap(), organizeId);

        VideoCreateWithMaterialRequest defaultMashUpRequest = JsonUtil.jsonToObject(JsonUtil.toJsonString(request), VideoCreateWithMaterialRequest.class);

        //清空已选素材
        defaultMashUpRequest.getScripts().forEach(script -> script.setMaterialList(Lists.newArrayList()));

        //数字人

        boolean chooseDigitalIsEmpty = false;
        List<VideoDigitalHumanConfig> chooseDigitalHumanConfigs = request.getChooseDigitalHumanConfigs();
        if (CollectionUtils.isEmpty(request.getChooseDigitalHumanConfigs())) {
            chooseDigitalHumanConfigs = getAlternativeDigitalPersonConfig(organizeId);
            chooseDigitalIsEmpty = true;
        }

        List<VideoCreateWithMaterialRequest> mashUpRequestList = Lists.newArrayList();

        List<VideoUserScriptDto> scriptsList;
        int index = 0;
        for (List<Integer> materialIndex : weavedMaterialIndex) {
            VideoCreateWithMaterialRequest mashUpRequest = JsonUtil.jsonToObject(JsonUtil.toJsonString(defaultMashUpRequest), VideoCreateWithMaterialRequest.class);

            //随机背景音乐
            randomBgmConfig(mashUpRequest, bgmMaterialList);

            //随机语音
            randomDigitalPersonConfig(mashUpRequest, chooseDigitalHumanConfigs, genderTtsMap, organizeId, index, chooseDigitalIsEmpty);

            //随机前贴视频
            randomBeforeAfterScript(mashUpRequest, request, index);

            scriptsList = mashUpRequest.getScripts();
            for (int i = 0; i < materialIndex.size(); i++) {
                VideoUserScriptDto sourceScript = request.getScripts().get(i);
                VideoUserScriptDto script = scriptsList.get(i);

                Integer currentMaterialIndex = materialIndex.get(i);
                if (currentMaterialIndex != null) {
                    VideoMaterialItem videoMaterial = sourceScript.getMaterialOriginList().get(currentMaterialIndex);
                    script.setMaterialList(Lists.newArrayList(videoMaterial));

                    //分镜贴片
                    List<WidgetGroup> scriptWidgets = script.getWidgets();
                    if (!CollectionUtils.isEmpty(scriptWidgets) && scriptWidgets.size() > currentMaterialIndex) {
                        WidgetGroup widgetGroup = scriptWidgets.get(currentMaterialIndex);
                        script.setWidgets(Lists.newArrayList(widgetGroup));
                    }
                } else {
                    script.setMaterialList(Lists.newArrayList());
                }
            }

            //全局贴片
            videoWidgets(mashUpRequest, request, index);

            mashUpRequestList.add(mashUpRequest);
            index++;
        }

        return mashUpRequestList;
    }

    private void videoWidgets(VideoCreateWithMaterialRequest mashUpRequest, VideoMashUpCreateRequest request, int index) {
        List<WidgetGroup> widgets = request.getWidgets();
        if (CollectionUtils.isEmpty(widgets)) {
            return;
        }

        // 全局贴片
        WidgetGroup widgetGroup = widgets.get(index % widgets.size());
        mashUpRequest.setWidgets(Lists.newArrayList(widgetGroup));
    }

    private void randomBeforeAfterScript(VideoCreateWithMaterialRequest mashUpRequest, VideoMashUpCreateRequest request, int index) {
        VideoUserScriptDto beforeScript = request.getBeforeScript();
        VideoUserScriptDto afterScript = request.getAfterScript();
        if (beforeScript != null && !CollectionUtils.isEmpty(beforeScript.getMaterialList())) {
            VideoMaterialItem videoMaterial = beforeScript.getMaterialList().get(index % beforeScript.getMaterialList().size());
            mashUpRequest.setBeforeScript(new VideoUserScriptDto().setMaterialList(Lists.newArrayList(videoMaterial)));
        }

        if (afterScript != null && !CollectionUtils.isEmpty(afterScript.getMaterialList())) {
            VideoMaterialItem videoMaterial = afterScript.getMaterialList().get(index % afterScript.getMaterialList().size());
            mashUpRequest.setAfterScript(new VideoUserScriptDto().setMaterialList(Lists.newArrayList(videoMaterial)));
        }
    }


    private void randomBgmConfig(VideoCreateWithMaterialRequest mashUpRequest, List<VideoBgmMaterial> bgmMaterialList) {
        Long bgmId = bgmMaterialList.get(RandomUtils.nextInt(0, bgmMaterialList.size())).getId();
        mashUpRequest.getPromptMap().put(FormDataUtils.FORM_KEY_CHOOSE_MUSIC, bgmId);
    }

    private void randomDigitalPersonConfig(
            VideoCreateWithMaterialRequest mashUpRequest,
            List<VideoDigitalHumanConfig> chooseDigitalHumanConfigs,
            Map<Integer, List<TtsConfig>> genderTtsMap, Long organizeId, Integer index, boolean chooseDigitalIsEmpty) {
        Map<String, Object> promptMap = mashUpRequest.getPromptMap();

        Boolean digitalPersonOpen = FormDataUtils.getBoolean(promptMap, FormDataUtils.FORM_KEY_DIGITAL_PERSON_OPEN, false);
        //启用数字人
        if (digitalPersonOpen) {
            if (CollectionUtils.isEmpty(chooseDigitalHumanConfigs)) {
                throw new InngkeServiceException("获取数字失败");
            }
            List<VideoDigitalHumanConfig> randomDigitalPersonConfig = Lists.newArrayList();
            for (int i = 0; i < mashUpRequest.getDigitalHumanConfigs().size(); i++) {
                if (!chooseDigitalIsEmpty) {
                    randomDigitalPersonConfig.add(chooseDigitalHumanConfigs.get((index * mashUpRequest.getDigitalHumanConfigs().size() + i) % chooseDigitalHumanConfigs.size()));
                } else {
                    randomDigitalPersonConfig.add(chooseDigitalHumanConfigs.get(RandomUtils.nextInt(0, chooseDigitalHumanConfigs.size())));
                }
            }
            mashUpRequest.setDigitalHumanConfigs(randomDigitalPersonConfig);
            return;
        }

        Boolean audioOpen = FormDataUtils.getBoolean(promptMap, FormDataUtils.FORM_KEY_AUDIO_OPEN, false);
        //开启语音
        if (audioOpen) {
            randomAudioConfig(mashUpRequest, genderTtsMap);
        }
    }

    private List<VideoDigitalHumanConfig> getAlternativeDigitalPersonConfig(Long organizeId) {
        List<DigitalPersonTemplate> digitalPersonTemplateList = digitalPersonTemplateManager.list(
                Wrappers.<DigitalPersonTemplate>query()
                        .in(DigitalPersonTemplate.ORGANIZE_ID, Lists.newArrayList(organizeId, 0))
                        .eq(DigitalPersonTemplate.TYPE, 1)
        );
        List<Integer> ttsIds = digitalPersonTemplateList.stream().map(DigitalPersonTemplate::getTtsId).filter(Objects::nonNull).collect(Collectors.toList());

        Collection<TtsConfig> ttsConfigs = ttsConfigManager.listByIds(ttsIds);
        Map<Integer, VideoAudioConfig> ttsConfigMap = ttsConfigs.stream().map(this::toTtsConfig)
                .collect(Collectors.toMap(VideoAudioConfig::getId, Function.identity()));

        return digitalPersonTemplateList.stream().map(digitalPersonTemplate -> {
            VideoDigitalHumanConfig videoDigitalHumanConfig = new VideoDigitalHumanConfig();
            videoDigitalHumanConfig.setTemplateId(digitalPersonTemplate.getId());
            videoDigitalHumanConfig.setName(digitalPersonTemplate.getName());
            Optional.ofNullable(digitalPersonTemplate.getTtsId()).map(ttsConfigMap::get)
                    .ifPresent(videoDigitalHumanConfig::setAudioConfig);
            return videoDigitalHumanConfig;
        }).collect(Collectors.toList());
    }


    private void randomAudioConfig(
            VideoCreateWithMaterialRequest mashUpRequest,
            Map<Integer, List<TtsConfig>> genderTtsMap) {
        Integer gender = RandomUtils.nextInt(1, 3);

        for (VideoDigitalHumanConfig videoDigitalHumanConfig : mashUpRequest.getDigitalHumanConfigs()) {

            List<TtsConfig> genderConfigs = Optional.ofNullable(genderTtsMap.get(gender)).orElse(genderTtsMap.get(3 - gender));

            TtsConfig ttsConfig = genderConfigs.get(RandomUtils.nextInt(0, genderConfigs.size()));

            VideoAudioConfig audioConfig = toTtsConfig(ttsConfig);

            videoDigitalHumanConfig.setAudioConfig(audioConfig);

            gender = 3 - gender;
        }
    }


    private Integer getMashUpCount(MashUpConfigDto mashUpConfig, List<Integer> scriptCounts, Integer mashUpType) {
        return randomWeaveMaterial((double) mashUpConfig.getNoDuplicatePercent() / 100, 20, scriptCounts, mashUpType).size();
    }

    private MashUpConfigDto getMashUpConfig() {
        String configStr = appConfigManager.getValueByCode(AppConfigCodeEnum.MASH_UP_CONFIG.getCode());
        if (StringUtils.isBlank(configStr)) {
            return new MashUpConfigDto();
        }

        return JsonUtil.jsonToObject(configStr, MashUpConfigDto.class);
    }

    private static List<List<Integer>> createNoRepeat(List<Integer> scriptCount, Integer mashUpType, Integer count) {
        List<List<Integer>> weaveIndexList = Lists.newArrayList();

        int minCount = scriptCount.stream().filter(Objects::nonNull).mapToInt(Integer::intValue).min().orElse(0);

        List<List<Integer>> preWeave = Lists.newArrayList();
        for (Integer i : scriptCount) {
            List<Integer> range = new ArrayList<>(minCount);
            for (int j = 0; j < minCount; j++) {
                if (i == null) {
                    range.add(null);
                } else {
                    range.add(j);
                }
            }

            if (MASH_UP_TYPE_RANDOM.equals(mashUpType)) {
                Collections.shuffle(range, new Random());
            }
            preWeave.add(range);
        }

        for (int i = 0; i < minCount; i++) {
            int finalI = i;
            weaveIndexList.add(preWeave.stream().map(materialIndex -> materialIndex.get(finalI)).collect(Collectors.toList()));
            if (weaveIndexList.size() == count) {
                break;
            }
        }

        return weaveIndexList;
    }

    private List<VideoBgmMaterial> getRandomMusicList(Map<String, Object> promptMap, Long organizeId) {
        Object chooseMusicIds = promptMap.get(FormDataUtils.FORM_KEY_CHOOSE_MUSIC_IDS);
        if (Objects.isNull(chooseMusicIds)) {
            return videoBgmMaterialManager.getAll(organizeId);
        }

        String chooseMusicIdsStr = jsonService.toJson((Serializable) chooseMusicIds);
        List<Long> musicIds = jsonService.toObjectList(chooseMusicIdsStr, Long.class);
        if (!CollectionUtils.isEmpty(musicIds)) {
            musicIds = musicIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(musicIds)) {
            return videoBgmMaterialManager.getAll(organizeId);
        }

        return Lists.newArrayList(videoBgmMaterialManager.listByIds(musicIds));
    }

    private Map<Integer, List<TtsConfig>> getRandomGenderTtsMapList(List<VideoDigitalHumanConfig> digitalHumanConfigs, Long organizeId) {
        if (digitalHumanConfigs != null && !CollectionUtils.isEmpty(digitalHumanConfigs)) {
            List<Integer> ttsIds = digitalHumanConfigs.stream().map(VideoDigitalHumanConfig::getAudioConfig)
                    .map(VideoAudioConfig::getId).collect(Collectors.toList());
            Map<Integer, VideoAudioConfig> requestTtsConfigMap = digitalHumanConfigs.stream().map(VideoDigitalHumanConfig::getAudioConfig)
                    .collect(Collectors.toMap(VideoAudioConfig::getId, Function.identity(), (existing, replacement) -> existing));

            Map<Integer, List<TtsConfig>> ttsConfigMap = ttsConfigManager.listByIds(ttsIds).stream().collect(Collectors.groupingBy(TtsConfig::getGender));

            //将请求的音色配置的语速赋值到 ttsConfigMap中
            ttsConfigMap.forEach((key, ttsConfigList) ->
                    ttsConfigList.forEach(ttsConfig ->
                            Optional.ofNullable(
                                    requestTtsConfigMap.get(ttsConfig.getId())
                            ).map(VideoAudioConfig::getSpeedRatio).ifPresent(speedRatio->
                                    ttsConfig.setSpeedRatio((double) (speedRatio/100))
                            )
                    )
            );

            return ttsConfigMap;
        }

        return ttsConfigManager.getAll(organizeId).stream().collect(Collectors.groupingBy(TtsConfig::getGender));
    }

    private VideoAudioConfig toTtsConfig(TtsConfig ttsConfig) {
        VideoAudioConfig audioConfig = new VideoAudioConfig();

        audioConfig.setId(ttsConfig.getId());
        audioConfig.setVoice(ttsConfig.getVoiceType());
        audioConfig.setStyle(ttsConfig.getStyle());
        audioConfig.setPlatform(ttsConfig.getPlatform());
        audioConfig.setSpeedRatio(Double.valueOf(ttsConfig.getSpeedRatio() * 100).intValue());
        audioConfig.setVolume(ttsConfig.getVolumeRatio());
        audioConfig.setAvatar(ttsConfig.getImageUrl());
        audioConfig.setName(ttsConfig.getTitle());
        audioConfig.setGender(ttsConfig.getGender());

        return audioConfig;
    }
}
