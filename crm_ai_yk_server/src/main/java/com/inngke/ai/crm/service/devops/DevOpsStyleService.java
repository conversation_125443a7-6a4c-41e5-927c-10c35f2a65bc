package com.inngke.ai.crm.service.devops;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.ai.crm.db.crm.entity.Category;
import com.inngke.ai.crm.db.crm.manager.CategoryManager;
import com.inngke.ai.crm.dto.request.devops.SaveJianYingResourceStyleRequest;
import com.inngke.ai.crm.dto.response.common.CategoryStyleExtDataDto;
import com.inngke.ai.crm.dto.response.devops.JianYingResourceStyleDto;
import com.inngke.common.core.utils.SnowflakeHelper;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.utils.JsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class DevOpsStyleService {

    @Autowired
    private CategoryManager categoryManager;

    public BaseResponse<List<JianYingResourceStyleDto>> getStylesList(Long organizeId) {
        List<Category> categories = categoryManager.list(
                Wrappers.<Category>query()
                        .eq(Category.TYPE, "subtitle_style")
                        .eq(Category.ORGANIZE_ID, organizeId)
        );

        List<JianYingResourceStyleDto> stylesList = categories.stream()
                .map(category -> {
                    JianYingResourceStyleDto node = new JianYingResourceStyleDto();
                    node.setId(category.getId());
                    node.setName(category.getName());
                    node.setIcon(category.getIcon());
                    if (StringUtils.isNotBlank(category.getExtData())){
                        CategoryStyleExtDataDto extData = JsonUtil.jsonToObject(category.getExtData(), CategoryStyleExtDataDto.class);
                        if (Objects.nonNull(extData)){
                            Optional.ofNullable(extData.getSubtitlesStyle()).ifPresent(node::setSubtitlesStyle);
                            Optional.ofNullable(extData.getFontSize()).ifPresent(node::setFontSize);
                            Optional.ofNullable(extData.getTextFont()).ifPresent(node::setTextFont);
                        }
                    }
                    node.setCode(category.getCode());
                    return node;
                }).collect(Collectors.toList());

        return BaseResponse.success(stylesList);
    }


    public Boolean saveStyle(SaveJianYingResourceStyleRequest request) {

        Category existCode = categoryManager.getOne(Wrappers.<Category>query().eq(Category.CODE, request.getCode()).last("limit 1"));

        if ( Objects.nonNull(existCode) && !existCode.getId().equals(request.getId())) {
            throw new InngkeServiceException("code不能重复【" + request.getCode() + "】已存在");
        }

        //创建
        Category category;
        if (Objects.isNull(request.getId())){
            category = new Category();
            category.setType("subtitle_style");
            category.setCreateTime(LocalDateTime.now());
        }else {
            //更新
            category = categoryManager.getById(request.getId());
            if (Objects.isNull(category)){
                throw new InngkeServiceException("风格不存在");
            }
            category.setUpdateTime(null);
        }
        category.setName(request.getName());
        category.setIcon(request.getIcon());
        category.setCode(String.valueOf(request.getCode()));

        CategoryStyleExtDataDto extData = new CategoryStyleExtDataDto();
        Optional.ofNullable(request.getSubtitlesStyle()).ifPresent(extData::setSubtitlesStyle);
        Optional.ofNullable(request.getFontSize()).ifPresent(extData::setFontSize);
        Optional.ofNullable(request.getTextFont()).ifPresent(extData::setTextFont);
        String extDataStr = JsonUtil.toJsonString(extData);
        category.setExtData(extDataStr);
        category.setOrganizeId(request.getOrganizeId());

        categoryManager.saveStyle(category);

        return true;
    }

    public Boolean deleteStyle(Long id) {
        return categoryManager.deleteStyle(id);
    }

    public Boolean cloneStyle(Long organizeId, Long id) {
        return categoryManager.cloneStyle(organizeId,id);
    }
}
