package com.inngke.ai.crm.service;

import com.inngke.ai.crm.dto.enums.AppTypeEnum;
import com.inngke.ai.crm.dto.request.DifyApiRequest;
import com.inngke.common.core.utils.EnvUtils;
import com.inngke.common.enums.EnvEnum;
import com.inngke.ip.ai.dify.dto.request.ChatMessagesRequest;
import com.inngke.ip.ai.dify.dto.response.DifyResponse;
import okhttp3.Response;

import java.util.function.BiConsumer;
import java.util.function.Consumer;

public interface DifyService {
    default String getUser(long userId) {
        return (EnvUtils.getEnv() == EnvEnum.PROD ? "P_" : "T_") + userId;
    }

    /**
     * 通用Dify接口
     * 此接口会根据数据库中的配置和场景码，自动调用对应的接口
     *
     * @param request 请求
     * @return 响应
     */
    DifyResponse difyApi(AppTypeEnum appType, DifyApiRequest request);

    /**
     * Dify会话型消息
     *
     * @param appToken            应用token
     * @param chatMessagesRequest 请求
     * @param messageHandle       事件处理
     * @param errorHandle         错误处理
     */
    void chatMessages(String appToken, ChatMessagesRequest chatMessagesRequest, Consumer<DifyResponse> messageHandle, BiConsumer<Response, Throwable> errorHandle);

    /**
     * Dify文本型消息
     *
     * @param appToken            应用token
     * @param chatMessagesRequest 请求
     * @param messageHandle       事件处理
     * @param errorHandle         错误处理
     */
    void completionMessages(String appToken, ChatMessagesRequest chatMessagesRequest, Consumer<DifyResponse> messageHandle, BiConsumer<Response, Throwable> errorHandle);

    /**
     * Dify会话型消息(SSE)
     *
     * @param appToken            应用token
     * @param chatMessagesRequest 请求
     * @param eventHandle         事件处理
     * @param errorHandle         错误处理
     */
    void chatMessagesStream(String appToken, ChatMessagesRequest chatMessagesRequest, Consumer<DifyResponse> eventHandle, BiConsumer<Response, Throwable> errorHandle);

    /**
     * Dify文本型消息(SSE)
     *
     * @param appToken            应用token
     * @param chatMessagesRequest 请求
     * @param eventHandle         事件处理
     * @param errorHandle         错误处理
     */
    void completionMessagesStream(String appToken, ChatMessagesRequest chatMessagesRequest, Consumer<DifyResponse> eventHandle, BiConsumer<Response, Throwable> errorHandle);
}
