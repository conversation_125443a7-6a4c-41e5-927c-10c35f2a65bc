package com.inngke.ai.crm.service.devops;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.inngke.ai.crm.db.crm.entity.AppConfig;
import com.inngke.ai.crm.db.crm.entity.Organize;
import com.inngke.ai.crm.db.crm.entity.User;
import com.inngke.ai.crm.db.crm.manager.AppConfigManager;
import com.inngke.ai.crm.db.crm.manager.OrganizeManager;
import com.inngke.ai.crm.db.crm.manager.UserManager;
import com.inngke.ai.crm.dto.enums.AppConfigCodeEnum;
import com.inngke.ai.crm.dto.request.devops.CreateOrganizeRequest;
import com.inngke.ai.crm.dto.response.devops.OrganizeDto;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.utils.DateTimeUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class OrganizeService {

    @Autowired
    private OrganizeManager organizeManager;
    @Autowired
    private UserManager userManager;

    @Autowired
    private AppConfigManager appConfigManager;


    /**
     * 获取企业列表
     */
    public BasePaginationResponse<OrganizeDto> getOrganizeList() {
        BasePaginationResponse<OrganizeDto> response = new BasePaginationResponse<>();
        QueryWrapper<Organize> query = Wrappers.<Organize>query().ne(Organize.STATUS, -1).orderByDesc(Organize.ID);
        response.setTotal(organizeManager.count(query));
        response.setList(organizeManager.list(query).stream().map(this::toOrganizeDto).collect(Collectors.toList()));
        return response;
    }

    private OrganizeDto toOrganizeDto(Organize organize) {
        OrganizeDto organizeDto = new OrganizeDto();
        organizeDto.setId(organize.getId());
        organizeDto.setName(organize.getName());
        organizeDto.setStatus(organize.getStatus());
        organizeDto.setBalance(organize.getBalance());
        organizeDto.setUseExternalModel(organize.getUseExternalModel());
        organizeDto.setCreateTime(DateTimeUtils.getMilli(organize.getCreateTime()));
        return organizeDto;
    }

    public Map<String, String> getOrganizeConfig(Long organizeId) {
        List<String> codeList = getCodeList(organizeId);

        String suffix = getSuffix(organizeId);

        return appConfigManager.list(
                Wrappers.<AppConfig>query().in(AppConfig.CODE, codeList)
        ).stream().collect(Collectors.toMap(config -> config.getCode().replace(suffix, InngkeAppConst.EMPTY_STR), AppConfig::getValue));
    }

    public Map<String, String> saveOrganizeConfig(Long organizeId, Map<String, String> requestConfig) {
        Map<String, String> config = toOrganizeConfig(organizeId, requestConfig);
        if (CollectionUtils.isEmpty(config)){
            return getOrganizeConfig(organizeId);
        }

        QueryWrapper<AppConfig> query = Wrappers.<AppConfig>query().in(AppConfig.CODE, config.keySet())
                .select(AppConfig.ID, AppConfig.CODE, AppConfig.VALUE);

        Map<String, AppConfig> configMap = appConfigManager.list(query).stream().collect(Collectors.toMap(AppConfig::getCode, Function.identity()));

        for (String key : config.keySet()) {
            configMap.computeIfAbsent(key, a -> {
                AppConfig appConfig = new AppConfig();
                appConfig.setCode(key);
                appConfig.setCreateTime(LocalDateTime.now());
                return appConfig;
            });

            configMap.get(key).setValue(config.get(key));
        }

        appConfigManager.saveOrUpdateBatch(configMap.values());

        return getOrganizeConfig(organizeId);
    }


    public OrganizeDto createOrganize(CreateOrganizeRequest request) {
        Organize organize = new Organize();
        Optional.ofNullable(request.getId()).ifPresent(organize::setId);
        organize.setName(request.getName());
        organize.setBalance(request.getBalance());
        organize.setCreateTime(LocalDateTime.now());

        organizeManager.addOrUpdateOrganize(organize);
        return toOrganizeDto(organizeManager.getById(organize.getId()));
    }

    public Boolean delOrganize(Long id) {
        if (userManager.count(Wrappers.<User>query().eq(User.ORGANIZE_ID, id)) > 0) {
            throw new InngkeServiceException("删除失败,企业下还有员工");
        }

        organizeManager.update(Wrappers.<Organize>update().eq(Organize.ID, id).set(Organize.STATUS, -1));

        return true;
    }

    private List<String> getCodeList(Long organizeId){
        List<String> exclusion = Lists.newArrayList("aiVideo.example");

        String suffix = getSuffix(organizeId);

        return Arrays.stream(AppConfigCodeEnum.values()).map(appConfig-> appConfig.getCode() + suffix)
                .filter(code -> !exclusion.contains(code)).collect(Collectors.toList());
    }

    private Map<String, String> toOrganizeConfig(Long organizeId,Map<String, String> config) {
        //企业配置后缀
        String suffix = getSuffix(organizeId);

        HashMap<String, String> resConfig = Maps.newHashMap();
        for (String key : config.keySet()) {
            if (StringUtils.isNotBlank(config.get(key))){
                resConfig.put(key+suffix,config.get(key));
            }
        }

        return resConfig;
    }

    //企业配置后缀
    private String getSuffix(Long organizeId){
        return organizeId != 0L ? InngkeAppConst.DOT_STR + organizeId: InngkeAppConst.EMPTY_STR;
    }

    public String getOrganizeConfig(Long organizeId, String code) {
        String privateCode = code + InngkeAppConst.DOT_STR + organizeId;
        Map<String, String> codeValueMap = appConfigManager.getValueByCodeList(Lists.newArrayList(code, privateCode));

        return Optional.ofNullable(codeValueMap.get(privateCode)).orElse(codeValueMap.get(code));
    }
}
