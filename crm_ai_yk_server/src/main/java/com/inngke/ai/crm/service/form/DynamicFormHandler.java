package com.inngke.ai.crm.service.form;

import com.inngke.ai.crm.db.crm.entity.DifyAppConf;
import com.inngke.ai.crm.dto.form.CommonFormConfig;
import com.inngke.ai.crm.dto.response.pro.ProAiArticleTemplateDto;

import java.io.Serializable;

public interface DynamicFormHandler {
    /**
     * 需要处理的表单key
     */
    String getFormKey();

    /**
     * 处理表单
     *
     * @param organizeId   企业ID
     * @param config       当前需要处理的表单配置
     * @param formConfigs  表单配置
     * @param preFormValue 当前表单关联的值
     * @return 如果返回false时，会将表单删除掉
     */
    boolean handle(long organizeId, DifyAppConf difyAppConf, CommonFormConfig config, ProAiArticleTemplateDto formConfigs, String preFormValue);

    void clear();
}
