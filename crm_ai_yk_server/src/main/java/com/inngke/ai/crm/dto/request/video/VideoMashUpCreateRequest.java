package com.inngke.ai.crm.dto.request.video;

import com.inngke.ai.crm.dto.form.MusicSelectFormConfig;
import com.inngke.ai.crm.dto.form.SelectOption;
import com.inngke.ai.dto.VideoMaterialItem;
import com.inngke.ai.dto.VideoUserScriptDto;
import com.inngke.ai.dto.request.VideoDigitalHumanConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class VideoMashUpCreateRequest extends VideoCreateWithMaterialRequest {

}
