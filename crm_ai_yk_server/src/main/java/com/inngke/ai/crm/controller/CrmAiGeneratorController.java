package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.dto.IdPageDto;
import com.inngke.ai.crm.dto.request.*;
import com.inngke.ai.crm.dto.response.AiGenerateHistoryDto;
import com.inngke.ai.crm.dto.response.AiGenerateResponse;
import com.inngke.ai.crm.dto.response.GetHistoryVideoListDto;
import com.inngke.ai.crm.dto.response.GetXiaoHongShuConfigDto;
import com.inngke.ai.crm.dto.response.ai.AiGenerateTaskDto;
import com.inngke.ai.crm.dto.response.ai.SimpleDifyAppConfDto;
import com.inngke.ai.crm.service.CrmAiGeneratorService;
import com.inngke.ai.crm.service.DifyAppConfService;
import com.inngke.ai.crm.service.XiaoHongShuGenerateService;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;
import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 * @chapter AI
 * @section AI生成
 * @since 2023-08-29 19:47
 **/
@RestController
@RequestMapping("/api/ai/generator")
public class CrmAiGeneratorController {
    @Autowired
    private XiaoHongShuGenerateService xiaoHongShuGenerateService;

    @Autowired
    private CrmAiGeneratorService crmAiGeneratorService;

    @Autowired
    private DifyAppConfService difyAppConfService;

    @Autowired
    @Qualifier("aiThreadPool")
    private Executor executor;

    /**
     * 小红书图文生成接口
     */
    @PostMapping("/xiao-hong-shu")
    public BaseResponse<AiGenerateResponse> createAiXiaoHongShu(
            @RequestAttribute JwtPayload jwtPayload,
            @Validated @RequestBody AiGenerateImageTextRequest request
    ) {
        return xiaoHongShuGenerateService.createXiaoHongShu(jwtPayload.getCid(), request);
    }

    @PostMapping("/xiao-hong-shu/{id}/retry")
    public BaseResponse<Boolean> createAiXiaoHongShuRetry(@PathVariable Long id){
        return xiaoHongShuGenerateService.createXiaoHongShuRetry(id);
    }

    /**
     * 查询小红书图文生成详情
     * 以SSE方式响应，包含四大段内容
     * 1. 创作基本情况：task
     * 2. 创作标题：title
     * 3. 创作内容：content
     * 4. 创作标签：tag
     *
     * @param request 请求参数
     * @return SSE
     */
    @GetMapping("/xiao-hong-shu")
    public SseEmitter getXiaoHongShu(@RequestAttribute JwtPayload jwtPayload,
                                     @Validated AiGenerateGetRequest request) {
        SseEmitter event = new SseEmitter();
        AsyncUtils.runAsync(() -> {
            //获取小红书文案
            xiaoHongShuGenerateService.get(event, request);
        }, executor, false);
        return event;
    }

    /**
     * 获取小红书配置
     */
    @GetMapping("/xiao-hong-shu-config")
    public BaseResponse<GetXiaoHongShuConfigDto> getXiaoHongShuConfig() {
        return xiaoHongShuGenerateService.getXiaoHongShuConfig();
    }

    /**
     * 更新小红书信息
     */
    @PutMapping("xhs-outputs")
    public BaseResponse<Boolean> updateXhsOutPuts(@RequestAttribute JwtPayload jwtPayload,
                                                  @Validated @RequestBody CrmXhsOutputsRequest request) {
        request.setUserId(jwtPayload.getCid());
        return xiaoHongShuGenerateService.updateXhsOutPuts(request);
    }

    /**
     * 小红书创作历史
     */
    @GetMapping("/list")
    public BaseResponse<IdPageDto<AiGenerateHistoryDto>> aiGenerateHistory(
            @RequestAttribute JwtPayload jwtPayload,
            AiGenerateHistoryRequest request
    ) {
        request.setUserId(jwtPayload.getCid());
        return crmAiGeneratorService.aiGenerateHistory(request);
    }

    /**
     * 视频创作历史
     */
    @GetMapping("/history-video")
    public BaseResponse<IdPageDto<GetHistoryVideoListDto>> getHistoryVideoList(
            @RequestAttribute JwtPayload jwtPayload,
            AiGenerateHistoryRequest request) {

        request.setUserId(jwtPayload.getCid());
        return crmAiGeneratorService.getHistoryVideoList(request,false);
    }

    /**
     * 视频创作历史(PC)
     */
    @GetMapping("/history-video/pc")
    public BaseResponse<IdPageDto<GetHistoryVideoListDto>> getHistoryVideoListPc(
            @RequestAttribute JwtPayload jwtPayload,
            AiGenerateHistoryRequest request) {

        request.setUserId(jwtPayload.getCid());
        return crmAiGeneratorService.getHistoryVideoList(request,true);
    }

    /**
     * 任务详情
     */
    @GetMapping("/{taskId:\\d+}/")
    public BaseResponse<AiGenerateTaskDto> getHistoryInfo(
            @RequestAttribute JwtPayload jwtPayload,
            @PathVariable("taskId") Long taskId) {
        GetAiGeneratorHistoryInfoRequest request = new GetAiGeneratorHistoryInfoRequest();
        request.setUserId(jwtPayload.getCid());
        request.setTaskId(taskId);

        return crmAiGeneratorService.getAiGeneratorHistoryInfo(request);
    }

    /**
     * 删除AI生成任务
     */
    @DeleteMapping("/{taskId:\\d+}/")
    public BaseResponse<Boolean> deleteTask(@RequestAttribute JwtPayload jwtPayload, @PathVariable("taskId") Long taskId) {
        GetAiGeneratorHistoryInfoRequest request = new GetAiGeneratorHistoryInfoRequest();
        request.setUserId(jwtPayload.getCid());
        request.setTaskId(taskId);
        return crmAiGeneratorService.deleteHistoryTask(request);
    }

    /**
     * ai生成评价
     */
    @PutMapping("/appraise")
    public BaseResponse<Boolean> appraise(
            @RequestAttribute JwtPayload jwtPayload,
            @Validated @RequestBody GeneLogAppraiseRequest request
    ) {
        return crmAiGeneratorService.appraise(request);
    }

    /**
     * 获取dify应用列表
     */
    @GetMapping("/dify-app/{aiProductId:\\d+}/list")
    public BaseResponse<List<SimpleDifyAppConfDto>> getDifyAppList(@RequestAttribute JwtPayload jwtPayload, @PathVariable("aiProductId") Integer aiProductId){
        return difyAppConfService.getDifyAppList(jwtPayload,aiProductId);
    }

}
