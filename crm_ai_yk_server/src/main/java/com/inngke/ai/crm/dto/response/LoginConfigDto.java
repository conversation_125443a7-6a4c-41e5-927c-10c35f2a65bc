package com.inngke.ai.crm.dto.response;

import com.inngke.ai.crm.dto.request.NotifyTemplate;
import com.inngke.ai.crm.dto.response.ai.AiProductDto;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-08-30 10:12
 **/
public class LoginConfigDto implements Serializable {

    /**
     * AI产品表
     */
    private List<AiProductDto> aiProductDtoList;

    /**
     * 邀请好友信息
     */
    private InviteUserInfo inviteConfig;


    /**
     * 存储配置
     */
    private StorageConfigDto storage;

    /**
     * 模板通知配置
     */
    private NotifyTemplate notifyTemplate;


    public NotifyTemplate getNotifyTemplate() {
        return notifyTemplate;
    }

    public void setNotifyTemplate(NotifyTemplate notifyTemplate) {
        this.notifyTemplate = notifyTemplate;
    }

    public StorageConfigDto getStorage() {
        return storage;
    }

    public void setStorage(StorageConfigDto storage) {
        this.storage = storage;
    }

    public InviteUserInfo getInviteConfig() {
        return inviteConfig;
    }

    public void setInviteConfig(InviteUserInfo inviteConfig) {
        this.inviteConfig = inviteConfig;
    }

    public List<AiProductDto> getAiProductDtoList() {
        return aiProductDtoList;
    }

    public void setAiProductDtoList(List<AiProductDto> aiProductDtoList) {
        this.aiProductDtoList = aiProductDtoList;
    }
}
