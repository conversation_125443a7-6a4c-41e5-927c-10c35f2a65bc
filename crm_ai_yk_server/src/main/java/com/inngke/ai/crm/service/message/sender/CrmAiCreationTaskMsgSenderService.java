package com.inngke.ai.crm.service.message.sender;

import com.inngke.ai.crm.client.common.ShortLinkServiceForAi;
import com.inngke.ai.crm.core.config.AiGcConfig;
import com.inngke.ai.crm.db.crm.entity.CreationTask;
import com.inngke.ai.crm.db.crm.manager.CreationTaskManager;
import com.inngke.ai.crm.service.message.CrmMessageTypeEnum;
import com.inngke.ai.crm.service.message.content.CrmAiGenerateMessageContext;
import com.inngke.ai.crm.service.message.content.CrmCreationTaskSmsContext;
import com.inngke.ai.crm.service.message.content.CrmMessageContext;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.ip.common.dto.request.ShortLinkGenerateRequest;
import com.inngke.ip.common.dto.response.ShortLinkGenerateDto;
import com.inngke.ip.reach.dto.request.TemplateMessageSendRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
public class CrmAiCreationTaskMsgSenderService extends CrmMessageSenderServiceAbs {

    @Autowired
    private ShortLinkServiceForAi shortLinkServiceForAi;

    @Autowired
    private AiGcConfig aiGcConfig;

    @Override
    public CrmMessageTypeEnum getMessageType() {
        return CrmMessageTypeEnum.CREATION_TASK_MSG;
    }

    @Override
    public void init(CrmMessageContext ctx) {

    }

    @Override
    public TemplateMessageSendRequest sendMessage(CrmMessageContext ctx) {
        CrmCreationTaskSmsContext context = (CrmCreationTaskSmsContext) ctx;
        if (Objects.isNull(context.getCreationTask())){
            throw new InngkeServiceException("发送短信，任务详情未空");
        }

        ShortLinkGenerateRequest shortLinkGenerateRequest = new ShortLinkGenerateRequest();
        shortLinkGenerateRequest.setBid(aiGcConfig.getBid());
        shortLinkGenerateRequest.setPagePath("subpackages/generator/video/pro");
        shortLinkGenerateRequest.setPageRequest("creationTaskId=" + context.getCreationTask().getId());

        ShortLinkGenerateDto generate = shortLinkServiceForAi.generate(shortLinkGenerateRequest);

        CreationTask creationTask = context.getCreationTask();
        return getTemplateRequestBuilder()
                .setVar("task_name", creationTask.getName())
                .setVar("task_time", DateTimeUtils.format(creationTask.getStartTime(),"yyyy-MM-dd HH:mm"))
                .setVar("task_description",  StringUtils.length(creationTask.getTaskDesc()) > 35 ? StringUtils.substring(creationTask.getTaskDesc(),0,30)+"...":creationTask.getTaskDesc())
                .setVar("task_link", generate.getCode())
                .setMobile(context.getMobile())
                .build();
    }
}
