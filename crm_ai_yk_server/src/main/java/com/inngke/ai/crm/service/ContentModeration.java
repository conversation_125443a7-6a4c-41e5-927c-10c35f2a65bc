package com.inngke.ai.crm.service;

import com.google.common.collect.Lists;
import com.inngke.ai.crm.db.crm.manager.ContentModerationManager;
import com.tencentcloudapi.ims.v20201229.models.LabelResult;
import com.tencentcloudapi.ims.v20201229.models.LibResult;
import com.tencentcloudapi.ims.v20201229.models.ObjectResult;
import com.tencentcloudapi.ims.v20201229.models.OcrResult;
import com.tencentcloudapi.tms.v20201229.models.DetailResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Supplier;

@Service
public class ContentModeration {
    private static final Logger logger = LoggerFactory.getLogger(ContentModeration.class);

    private static final List<String> NOT_ADOPT_LABEL_SCENE = Lists.newArrayList("Porn", "Sexy", "Terror", "Illegal", "Polity");
    private static final String PASS = "Pass";
    private static final int RETRY_COUNT = 3;

//    @Resource
//    private TmsClient tmsClient;

//    @Resource
//    private ImsClient imsClient;

    @Resource
    private ContentModerationManager contentModerationManager;

    public Boolean textModeration(Long id, String content) {
        return true;
//        return executeAndRetry(() -> {
//            if (StringUtils.isBlank(content)) {
//                return true;
//            }
//
//            TextModerationRequest request = new TextModerationRequest();
//            request.setContent(Base64.getEncoder().encodeToString(content.getBytes()));
//            request.setDataId(id.toString());
//
//            TextModerationResponse response;
//            try {
//                response = tmsClient.TextModeration(request);
//                if (Objects.isNull(response)) {
//                    throw new InngkeServiceException("文本检测异常返回空");
//                }
//                contentModerationManager.saveLog(id,content,TextModerationResponse.toJsonString(response));
//            } catch (TencentCloudSDKException e) {
//                throw new RuntimeException(e);
//            }
//
//
////            return checkTheResponseLabels(Arrays.stream(response.getDetailResults()).map(this::toLabelResult).collect(Collectors.toList()));
//            return true;
//        });
    }

    public Boolean imageModeration(Long id, String imageUrl) {
        return true;
//        return executeAndRetry(() -> {
//            ImageModerationRequest request = new ImageModerationRequest();
//            request.setFileUrl(imageUrl);
//            request.setDataId(id.toString());
//            ImageModerationResponse response;
//            try {
//                response = imsClient.ImageModeration(request);
//
//                if (Objects.isNull(response)) {
//                    throw new InngkeServiceException("图片检测异常返回空");
//                }
//                contentModerationManager.saveLog(id, imageUrl, TextModerationResponse.toJsonString(response));
//            } catch (TencentCloudSDKException e) {
//                throw new RuntimeException(e);
//            }
//
//            List<LabelResult> labelResults = aggregatedImageResponseLabel(response.getLabelResults(), response.getObjectResults(), response.getOcrResults(), response.getLibResults());
//
////            return checkTheResponseLabels(labelResults);
//            return true;
//        });
    }


    private Boolean executeAndRetry(Supplier<Boolean> supplier) {

        for (int i = 0; i < RETRY_COUNT; i++) {
            try {
                Boolean result = supplier.get();
                if (Objects.nonNull(result)) {
                    return result;
                }
            } catch (Exception e) {
                logger.info("执行发生错误{}次", i, e);
            }
        }

        logger.info("执行发生错误3次，放弃!");
        return true;
    }

    private Boolean checkTheResponseLabels(List<LabelResult> labelResults) {
        for (LabelResult labelResult : labelResults) {
            if (NOT_ADOPT_LABEL_SCENE.contains(labelResult.getScene()) && !PASS.equals(labelResult.getSuggestion())) {
                return false;
            }
        }
        return true;
    }

    private List<LabelResult> aggregatedImageResponseLabel(LabelResult[] labelResults, ObjectResult[] objectResults, OcrResult[] ocrResults, LibResult[] libResults) {
        List<LabelResult> aggLabel = Lists.newArrayList();

        Optional.ofNullable(labelResults).ifPresent(labels -> Collections.addAll(aggLabel, labels));

        Optional.ofNullable(objectResults).ifPresent(labels -> {
            for (ObjectResult label : labels) {
                aggLabel.add(toLabelResult(label));
            }
        });

        Optional.ofNullable(ocrResults).ifPresent(labels -> {
            for (OcrResult label : labels) {
                aggLabel.add(toLabelResult(label));
            }
        });

        Optional.ofNullable(libResults).ifPresent(labels -> {
            for (LibResult label : labels) {
                aggLabel.add(toLabelResult(label));
            }
        });

        return aggLabel;
    }

    private LabelResult toLabelResult(LibResult label) {
        return toLabelResult(label.getScene(), label.getSuggestion(), label.getLabel(), label.getSubLabel(), label.getScore());
    }


    private LabelResult toLabelResult(OcrResult label) {
        return toLabelResult(label.getScene(), label.getSuggestion(), label.getLabel(), label.getSubLabel(), label.getScore());
    }

    private LabelResult toLabelResult(ObjectResult label) {
        return toLabelResult(label.getScene(), label.getSuggestion(), label.getLabel(), label.getSubLabel(), label.getScore());
    }

    private LabelResult toLabelResult(DetailResults label) {
        return toLabelResult(label.getLabel(), label.getSuggestion(), label.getLabel(), label.getSubLabel(), label.getScore());
    }

    private LabelResult toLabelResult(String scene, String suggestion, String label, String subLabel, Long score) {
        LabelResult labelResult = new LabelResult();
        labelResult.setScene(scene);
        labelResult.setSuggestion(suggestion);
        labelResult.setLabel(label);
        labelResult.setSubLabel(subLabel);
        labelResult.setScore(score);
        return labelResult;
    }
}
