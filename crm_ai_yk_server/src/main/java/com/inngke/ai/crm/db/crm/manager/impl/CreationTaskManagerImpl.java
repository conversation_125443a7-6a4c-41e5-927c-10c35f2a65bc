/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.inngke.ai.crm.db.crm.entity.CreationTask;
import com.inngke.ai.crm.db.crm.dao.CreationTaskDao;
import com.inngke.ai.crm.db.crm.manager.CreationTaskManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 企业创作任务 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
@Service
public class CreationTaskManagerImpl extends ServiceImpl<CreationTaskDao, CreationTask> implements CreationTaskManager {

    @Override
    public List<CreationTask> getStartedTask(Long organizeId) {
        return list(Wrappers.<CreationTask>query()
                .eq(CreationTask.ORGANIZE_ID,organizeId)
                .lt(CreationTask.START_TIME, LocalDateTime.now())
        );
    }

    @Override
    public List<CreationTask> getByIds(Collection<Long> creationTaskIds, String... columns) {
        if (CollectionUtils.isEmpty(creationTaskIds)) {
            return Lists.newArrayList();
        }
        return list(Wrappers.<CreationTask>query().in(CreationTask.ID, creationTaskIds).select(columns));
    }
}
