package com.inngke.ai.crm.converter;

import com.google.common.base.Joiner;
import com.google.common.collect.Sets;
import com.inngke.ai.crm.db.crm.entity.Staff;
import com.inngke.ai.crm.db.crm.entity.VideoScript;
import com.inngke.ai.crm.dto.request.video.VideoScriptSaveRequest;
import com.inngke.ai.crm.dto.response.video.VideoScriptDetailDto;
import com.inngke.ai.crm.dto.response.video.VideoScriptDto;
import com.inngke.ai.dto.utils.FormDataUtils;
import com.inngke.ip.ai.dify.app.dto.VideoScriptItem;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.utils.DateTimeUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public class VideoScriptsConverter {

    public static VideoScript toVideoScript(VideoScriptItem videoScriptItem, Staff staff, VideoScriptSaveRequest request, String parentCategoryIds) {
        VideoScript videoScript = new VideoScript();
        videoScript.setId(videoScriptItem.getId());
        videoScript.setTitle(videoScriptItem.getTitle());
        videoScript.setScriptContent(videoScriptItem.getScriptContent());
        videoScript.setOrganizeId(staff.getOrganizeId());
        videoScript.setDepartmentId(staff.getDepartmentId());
        videoScript.setStaffId(staff.getId());
        videoScript.setCateId(Optional.ofNullable(request.getCategoryId()).orElse(0L));
        videoScript.setCateIds(parentCategoryIds);
        videoScript.setDifyAppId(FormDataUtils.getInt(request.getPromptMap(), FormDataUtils.FORM_KEY_APP_ID, 0));
        videoScript.setCreateTime(LocalDateTime.now());
        return videoScript;
    }

    public static VideoScriptDetailDto toVideoScriptDto(VideoScript videoScript) {
        VideoScriptDetailDto videoScriptDto = new VideoScriptDetailDto();
        videoScriptDto.setId(videoScript.getId());
        videoScriptDto.setTitle(videoScript.getTitle());
        videoScriptDto.setScriptContent(videoScript.getScriptContent());
        videoScriptDto.setOrganizeId(videoScript.getOrganizeId());
        videoScriptDto.setStaffId(videoScript.getStaffId());
        videoScriptDto.setCateId(videoScript.getCateId());
        videoScriptDto.setCreateTime(DateTimeUtils.getMilli(videoScript.getCreateTime()));
        videoScriptDto.setUpdateTime(DateTimeUtils.getMilli(videoScript.getUpdateTime()));
        return videoScriptDto;
    }
}
