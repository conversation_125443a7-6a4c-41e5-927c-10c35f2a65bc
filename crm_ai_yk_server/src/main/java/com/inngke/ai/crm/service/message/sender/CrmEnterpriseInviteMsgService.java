package com.inngke.ai.crm.service.message.sender;

import com.inngke.ai.crm.client.common.ShortLinkServiceForAi;
import com.inngke.ai.crm.core.config.AiGcConfig;
import com.inngke.ai.crm.service.message.CrmMessageTypeEnum;
import com.inngke.ai.crm.service.message.content.CrmAiGenerateMessageContext;
import com.inngke.ai.crm.service.message.content.CrmMessageContext;
import com.inngke.ip.common.dto.request.ShortLinkGenerateRequest;
import com.inngke.ip.common.dto.response.ShortLinkGenerateDto;
import com.inngke.ip.reach.dto.request.TemplateMessageSendRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023-10-23 14:18
 **/
@Service
public class CrmEnterpriseInviteMsgService extends CrmMessageSenderServiceAbs {

    private static final Logger logger = LoggerFactory.getLogger(CrmEnterpriseInviteMsgService.class);

    @Autowired
    private ShortLinkServiceForAi shortLinkServiceForAi;

    @Autowired
    private AiGcConfig aiGcConfig;

    @Override
    public CrmMessageTypeEnum getMessageType() {
        return CrmMessageTypeEnum.ENTERPRISE_INVITE_MSG;
    }

    @Override
    public void init(CrmMessageContext ctx) {

    }

    @Override
    public TemplateMessageSendRequest sendMessage(CrmMessageContext ctx) {
        CrmAiGenerateMessageContext context = (CrmAiGenerateMessageContext) ctx;
        ShortLinkGenerateRequest shortLinkGenerateRequest = new ShortLinkGenerateRequest();
        shortLinkGenerateRequest.setBid(aiGcConfig.getBid());
        shortLinkGenerateRequest.setPagePath("subpackages/personal/recharge/exchange");
        shortLinkGenerateRequest.setPageRequest("activityCode=" + context.getActivityCode());

        ShortLinkGenerateDto generate = shortLinkServiceForAi.generate(shortLinkGenerateRequest);


        return getTemplateRequestBuilder()
                .setVar("vipType", context.getType())
                .setVar("enterpriseName", context.getName())
                .setVar("leadsLink",  generate.getCode())
                .setVar("num", context.getNum())
                .setMobile(context.getMobile())
                .build();
    }
}
