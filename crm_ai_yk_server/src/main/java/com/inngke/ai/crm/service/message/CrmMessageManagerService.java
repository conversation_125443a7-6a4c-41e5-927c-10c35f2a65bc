package com.inngke.ai.crm.service.message;

import com.google.common.collect.Lists;
import com.inngke.ai.crm.client.TemplateMessageSendServiceClientForUser;
import com.inngke.ai.crm.client.common.MqServiceClientForAi;
import com.inngke.ai.crm.core.config.AiGcConfig;
import com.inngke.ai.crm.service.message.content.CrmMessageContext;
import com.inngke.ai.crm.service.message.sender.CrmMessageSenderService;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.JsonService;
import com.inngke.ip.common.dto.request.MqDelaySendRequest;
import com.inngke.ip.common.dto.request.NotifyMessageRequest;
import com.inngke.ip.reach.dto.request.TemplateMessageSendRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;


@Service
public class CrmMessageManagerService {

    private static final String STR_NOTIFY_SEND = "notify_send";

    private static final Logger logger = LoggerFactory.getLogger(CrmMessageManagerService.class);

    @Autowired
    private List<CrmMessageSenderService> messageSenderService;

    @Autowired
    private MqServiceClientForAi mqServiceClientForAi;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private AiGcConfig aiGcConfig;

    @Autowired
    protected TemplateMessageSendServiceClientForUser templateMessageSendServiceClientForUser;

    public TemplateMessageSendRequest templateMessageSendRequest(CrmMessageContext ctx) {
        return findSender(ctx).sendMessage(ctx);
    }


    public void send(CrmMessageContext ctx) {
        TemplateMessageSendRequest request = templateMessageSendRequest(ctx);
        if (Objects.isNull(request)) {
            logger.info("消息发送失败构建请求失败, ctx={}", jsonService.toJson(ctx));
            return;
        }

        //延迟,定时发送
        if (Objects.nonNull(ctx.getDeliverAt()) || Objects.nonNull(ctx.getDeliverAfter())) {
            MqDelaySendRequest mqDelaySendRequest = new MqDelaySendRequest();
            mqDelaySendRequest.setBid(aiGcConfig.getBid());
            mqDelaySendRequest.setTopic(STR_NOTIFY_SEND);
            mqDelaySendRequest.setOperatorId(0L);

            NotifyMessageRequest notifyMessageRequest = new NotifyMessageRequest();
            notifyMessageRequest.setContent(jsonService.toJson(request));
            notifyMessageRequest.setType(Lists.newArrayList("template"));
            notifyMessageRequest.setBid(aiGcConfig.getBid());
            mqDelaySendRequest.setPayload(jsonService.toJson(notifyMessageRequest));

            Optional.ofNullable(ctx.getDeliverAfter()).ifPresent(mqDelaySendRequest::setDeliverAfter);
            Optional.ofNullable(ctx.getDeliverAt()).ifPresent(mqDelaySendRequest::setDeliverAt);
            Boolean response = mqServiceClientForAi.sendDelay(mqDelaySendRequest);
            logger.info("发送模版消息延迟mq返回:{}", response);
            return;
        }

        //直接发送
        request.setBid(aiGcConfig.getBid());
        templateMessageSendServiceClientForUser.send(request);
    }


    private CrmMessageSenderService findSender(CrmMessageContext ctx) {
        for (CrmMessageSenderService senderService : messageSenderService) {
            if (senderService.getMessageType().equals(ctx.getMessageType())) {
                return senderService;
            }
        }

        throw new InngkeServiceException("未获取到消息类型【" + ctx.getMessageType().getName() + "】的处理器");
    }
}
