package com.inngke.ai.crm.dto.response.video;

import lombok.Data;

import java.io.Serializable;

@Data
public class VideoProjectDraftDto implements Serializable {
    /**
     * 创作（草稿）ID
     *
     * @demo 1323421211212
     */
    private Long id;

    /**
     * 创作（草稿）标题
     *
     * @demo 顾家家居双11大放送
     */
    private String title;

    /**
     * 类型：1=裂变混剪 2=分镜脚本 3=成片混剪
     */
    private Integer type;

    /**
     * 视频封面图URL
     * 逻辑：获取第一个画面的视频切片首帧
     *
     * @demo https://static.inngke.com/aaa.jpg
     */
    private String coverImage;

    /**
     * 是否已经匹配素材
     */
    private Boolean matchedMaterial;

    /**
     * 是否可以裂变
     */
    private Boolean canBeMashUp;

    /**
     * 创建时间，时间戳，粒度：毫秒
     *
     * @demo 1629398400000
     */
    private Long createTime;

    /**
     * 更新时间，时间戳，粒度：毫秒
     *
     * @demo 1629398400000
     */
    private Long updateTime;
}
