/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 内容安全检测记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ContentModeration implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 内容id
     */
    private Long contentId;

    /**
     * 识别内容
     */
    private String content;

    /**
     * 返回信息
     */
    private String response;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    public static final String ID = "id";

    public static final String CONTENT_ID = "content_id";

    public static final String CONTENT = "content";

    public static final String RESPONSE = "response";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

}
