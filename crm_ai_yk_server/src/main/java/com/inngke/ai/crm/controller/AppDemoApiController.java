package com.inngke.ai.crm.controller;

import com.google.common.base.Splitter;
import com.google.common.collect.Maps;
import com.inngke.ai.crm.db.crm.entity.AppDemo;
import com.inngke.ai.crm.db.crm.manager.AppDemoManager;
import com.inngke.ai.crm.dto.request.AppDemoBatchRandomRequest;
import com.inngke.ai.crm.dto.request.AppDemoRandomRequest;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.config.sign.ApiSignature;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @chapter AI
 * @section AI生成
 */
@RestController
@RequestMapping("/api/ai/app-demo")
public class AppDemoApiController {
    @Autowired
    private AppDemoManager appDemoManager;

    /**
     * 随机获取AI应用内容
     */
    @GetMapping("/random")
    @ApiSignature(signature = false)
    public BaseResponse<List<String>> random(AppDemoRandomRequest request) {
        return BaseResponse.success(
                appDemoManager.randomList(request.getOrganizeId(), request.getAppTypeName(), request.getContentType(), request.getCount())
                        .stream()
                        .map(AppDemo::getContent)
                        .collect(Collectors.toList())
        );
    }

    /**
     * 批量随机获取AI应用内容
     */
    @GetMapping("/batch-random")
    @ApiSignature(signature = false)
    public BaseResponse<Map<String, List<String>>> batchRandom(AppDemoBatchRandomRequest request) {
        Map<String, List<String>> randomResult = Maps.newHashMap();
        Splitter.on(InngkeAppConst.COMMA_STR)
                .trimResults()
                .omitEmptyStrings()
                .split(request.getContentTypeCount())
                .forEach(item -> {
                    String[] kv = item.split(InngkeAppConst.CLN_STR);
                    if (kv.length != 2) {
                        throw new InngkeServiceException("非法请求，请求结构必须是：{contentType}:{count}，多个使用英文逗号分隔");
                    }
                    String contentType = kv[0].trim();
                    int count = Integer.parseInt(kv[1]);
                    randomResult.put(
                            contentType,
                            appDemoManager.randomList(request.getOrganizeId(), request.getAppTypeName(), contentType, count)
                                    .stream()
                                    .map(AppDemo::getContent)
                                    .collect(Collectors.toList())
                    );
                });

        return BaseResponse.success(randomResult);
    }
}
