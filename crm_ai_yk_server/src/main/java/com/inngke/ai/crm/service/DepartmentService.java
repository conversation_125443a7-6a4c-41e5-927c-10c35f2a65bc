package com.inngke.ai.crm.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.inngke.ai.crm.converter.DepartmentConverter;
import com.inngke.ai.crm.db.crm.entity.Department;
import com.inngke.ai.crm.db.crm.entity.Staff;
import com.inngke.ai.crm.db.crm.entity.User;
import com.inngke.ai.crm.db.crm.entity.UserVip;
import com.inngke.ai.crm.db.crm.manager.DepartmentManager;
import com.inngke.ai.crm.db.crm.manager.StaffManager;
import com.inngke.ai.crm.db.crm.manager.UserManager;
import com.inngke.ai.crm.db.crm.manager.UserVipManager;
import com.inngke.ai.crm.dto.request.org.department.CreateDepartmentRequest;
import com.inngke.ai.crm.dto.request.org.department.EditDepartmentRequest;
import com.inngke.ai.crm.dto.request.org.department.GetDepartmentStaffTreeRequest;
import com.inngke.ai.crm.dto.request.org.department.SearchDepartmentListRequest;
import com.inngke.ai.crm.dto.response.org.department.DepartmentDto;
import com.inngke.ai.crm.dto.response.org.department.DepartmentStaffTreeDto;
import com.inngke.ai.crm.dto.response.org.department.DepartmentTreeDto;
import com.inngke.ai.crm.service.impl.DepartmentCacheFactory;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.SnowflakeIdService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class DepartmentService {
    @Resource
    private DepartmentManager departmentManager;
    @Resource
    private SnowflakeIdService snowflakeIdService;
    @Resource
    private UserManager userManager;
    @Resource
    private UserVipManager userVipManager;
    @Resource
    private DepartmentCacheFactory departmentCacheFactory;
    @Resource
    private StaffManager staffManager;

    /**
     * 添加部门
     */
    public BaseResponse<Boolean> createDepartment(JwtPayload jwtPayload, CreateDepartmentRequest request) {
        Long organizeId = getOrganizeId(jwtPayload);

        Department department = DepartmentConverter.toDepartment(request);
        department.setId(snowflakeIdService.getId());
        department.setOrganizeId(organizeId);
        department.setCreateTime(LocalDateTime.now());

        Department parentDepartment = departmentManager.getById(request.getParentId());
        if (Objects.isNull(parentDepartment) || !organizeId.equals(parentDepartment.getOrganizeId())) {
            return BaseResponse.error("父部门不存在");
        }

        if (departmentManager.exist(request.getParentId(), request.getName())) {
            return BaseResponse.error("部门【" + request.getName() + "】已存在");
        }
        if (!departmentManager.save(department)) {
            return BaseResponse.error("添加部门失败");
        }

        departmentCacheFactory.incCacheVersion(organizeId.intValue());
        return BaseResponse.success(true);
    }

    /**
     * 编辑部门
     */
    public BaseResponse<Boolean> editDepartment(JwtPayload jwtPayload, EditDepartmentRequest request) {
        Long organizeId = getOrganizeId(jwtPayload);
        Map<Long, Department> departmentMap = departmentManager.getByIds(Lists.newArrayList(request.getId(), request.getParentId()))
                .stream().collect(Collectors.toMap(Department::getId, Function.identity()));

        Department department = departmentMap.get(request.getId());
        if (Objects.isNull(department) || !organizeId.equals(department.getOrganizeId())) {
            return BaseResponse.error("部门不存在");
        }

        Department parentDepartment = departmentMap.get(request.getParentId());
        if (Objects.isNull(parentDepartment) || !organizeId.equals(parentDepartment.getOrganizeId())) {
            return BaseResponse.error("父部门不存在");
        }

        department.setUpdateTime(null);
        department.setName(request.getName());
        department.setParentId(request.getParentId());

        if (!departmentManager.updateById(department)) {
            return BaseResponse.error("编辑部门失败");
        }
        departmentCacheFactory.incCacheVersion(organizeId.intValue());

        return BaseResponse.success(true);
    }

    /**
     * 获取部门详情
     */
    public BaseResponse<DepartmentDto> getDepartment(JwtPayload jwtPayload, Long id) {
        Department department = departmentManager.getById(getOrganizeId(jwtPayload), id);
        return BaseResponse.success(DepartmentConverter.toDepartmentDto(department));
    }

    /**
     * 获取部门树
     */
    public BaseResponse<DepartmentTreeDto> getDepartmentTree(JwtPayload jwtPayload) {
        DepartmentCacheFactory.DepartmentCache cache = departmentCacheFactory.getCache(getOrganizeId(jwtPayload).intValue());
        List<DepartmentTreeDto> roots = cache.getRoots();

        if (CollectionUtils.isEmpty(roots)) {
            return BaseResponse.success(null);
        }

        return BaseResponse.success(cache.getAuthTree(getUserDepartmentId(jwtPayload)));
    }

    /**
     * 搜索部门列表
     */
    public BaseResponse<List<DepartmentDto>> searchDepartmentList(JwtPayload jwtPayload, SearchDepartmentListRequest request) {
        Long organizeId = getOrganizeId(jwtPayload);

        QueryWrapper<Department> query = Wrappers.<Department>query()
                .eq(Department.ORGANIZE_ID, organizeId)
                .like(StringUtils.isNotBlank(request.getName()), Department.NAME, request.getName())
                .last("limit " + request.getSize());

        return BaseResponse.success(departmentManager.list(query).stream().map(DepartmentConverter::toDepartmentDto).collect(Collectors.toList()));
    }

    public BaseResponse<DepartmentStaffTreeDto> getDepartmentStaffTree(JwtPayload jwtPayload, GetDepartmentStaffTreeRequest request) {
        Long organizeId = getOrganizeId(jwtPayload);
        Staff staff = staffManager.getByUserId(jwtPayload.getCid());
        Long currentDepartmentId = Optional.ofNullable(request.getId()).orElse(staff.getDepartmentId());
        Department department = departmentManager.getById(organizeId, currentDepartmentId);
        if (Objects.isNull(department)){
            return BaseResponse.error("部门不存在");
        }

        DepartmentStaffTreeDto departmentStaffTree = DepartmentConverter.toDepartmentStaffTreeDto(department);

        List<Department> children = departmentManager.getByParentId(organizeId, currentDepartmentId);
        departmentStaffTree.setChildren(children.stream().map(DepartmentConverter::toDepartmentStaffTreeDto).collect(Collectors.toList()));

        List<Staff> staffList = staffManager.getByDepartmentId(department.getId());
        List<DepartmentStaffTreeDto> staffTreeList = staffList.stream().map(DepartmentConverter::toDepartmentStaffTreeDto).collect(Collectors.toList());

        departmentStaffTree.getChildren().addAll(staffTreeList);

        return BaseResponse.success(departmentStaffTree);
    }


    public BaseResponse<Boolean> deleteDepartment(JwtPayload jwtPayload, Long id) {
        Long organizeId = getOrganizeId(jwtPayload);

        if (staffManager.getDepartmentStaffCount(id) > 0){
            return BaseResponse.error("当前部门下有员工，请先删除或迁移员工后再删除部门");
        }

        if (userVipManager.getDepartmentStaffCount(id)>0){
            return BaseResponse.error("当前部门下有员工，请先删除或迁移员工后再删除部门");
        }

        if (!departmentManager.remove(Wrappers.<Department>query().eq(Department.ORGANIZE_ID,organizeId).eq(Department.ID,id))){
            return BaseResponse.error("删除失败");
        }

        departmentCacheFactory.incCacheVersion(organizeId.intValue());
        return BaseResponse.success(true);
    }

    private Long getOrganizeId(JwtPayload jwtPayload) {
        Long userOrganizeId = userManager.getUserOrganizeId(jwtPayload.getCid());
        if (Objects.isNull(userOrganizeId)) {
            throw new InngkeServiceException("帐号异常：不是企业员工");
        }

        return userOrganizeId;
    }

    private Long getUserDepartmentId(JwtPayload jwtPayload) {
        Long userDepartmentId = staffManager.getDepartmentIdByUserId(jwtPayload.getCid());
        if (Objects.isNull(userDepartmentId)) {
            throw new InngkeServiceException("帐号异常：不是企业员工");
        }

        return userDepartmentId;
    }
}
