package com.inngke.ai.crm.service.material.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.ai.crm.core.CrmServiceConsts;
import com.inngke.ai.crm.db.crm.entity.MaterialCategory;
import com.inngke.ai.crm.db.crm.entity.VideoMaterial;
import com.inngke.ai.crm.db.crm.manager.MaterialCategoryManager;
import com.inngke.ai.crm.db.crm.manager.VideoMaterialManager;
import com.inngke.common.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
public class MaterialCategoryCountScheduler {

    private static final String MATERIAL_COUNT_CACHE_KEY = CrmServiceConsts.CACHE_KEY_PRE + "material:category:count";
    private static final String MATERIAL_COUNT_LOCK_KEY = CrmServiceConsts.CACHE_KEY_PRE + "material:category:count:lock";
    private static final int PAGE_SIZE = 1000;
    private static final long LOCK_TIMEOUT_SECONDS = 3600; // 1小时

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private MaterialCategoryManager materialCategoryManager;

    @Autowired
    private VideoMaterialManager videoMaterialManager;

    @Scheduled(cron = "0 0 */5 * * ?") // 每30分钟执行一次
    public void updateMaterialCategoryCount() {
        // 使用分布式锁避免多个实例同时执行
        Boolean locked = redisTemplate.opsForValue().setIfAbsent(MATERIAL_COUNT_LOCK_KEY, "1", LOCK_TIMEOUT_SECONDS, TimeUnit.SECONDS);
        if (Boolean.FALSE.equals(locked)) {
            log.info("Another instance is updating material category count cache");
            return;
        }

        try {
            log.info("Start updating material category count cache");
            
            // 获取所有视频分类
            List<MaterialCategory> allCategories = materialCategoryManager.list(
                Wrappers.<MaterialCategory>lambdaQuery()
                    .eq(MaterialCategory::getType, MaterialCategory.TYPE_VIDEO)
            );
            if (CollectionUtils.isEmpty(allCategories)) {
                return;
            }

            // 使用复合key（categoryId:type）来统计每个分类下不同类型的素材数量
            Map<String, Integer> categoryTypeCountMap = new HashMap<>();
            
            // 使用分页查询处理大数据量
            long currentPage = 1;
            LambdaQueryWrapper<VideoMaterial> queryWrapper = Wrappers.<VideoMaterial>lambdaQuery()
                .gt(VideoMaterial::getStatus, -1)
                .select(VideoMaterial::getCategoryIds, VideoMaterial::getType); // 只查询需要的字段

            while (true) {
                IPage<VideoMaterial> page = new Page<>(currentPage, PAGE_SIZE);
                IPage<VideoMaterial> materialPage = videoMaterialManager.page(page, queryWrapper);
                
                if (CollectionUtils.isEmpty(materialPage.getRecords())) {
                    break;
                }

                // 处理当前页的数据
                for (VideoMaterial material : materialPage.getRecords()) {
                    String categoryIdsStr = material.getCategoryIds();
                    Integer materialType = material.getType();
                    
                    if (categoryIdsStr != null && !categoryIdsStr.isEmpty() && materialType != null) {
                        List<Long> categoryIds = JsonUtil.jsonToList(categoryIdsStr, Long.class);
                        if (categoryIds != null) {
                            for (Long categoryId : categoryIds) {
                                // 总数统计
                                String totalKey = categoryId + ":0"; // 0表示所有类型的总数
                                categoryTypeCountMap.merge(totalKey, 1, Integer::sum);
                                
                                // 按类型统计
                                String typeKey = categoryId + ":" + materialType;
                                categoryTypeCountMap.merge(typeKey, 1, Integer::sum);
                            }
                        }
                    }
                }

                if (currentPage >= materialPage.getPages()) {
                    break;
                }
                currentPage++;

                // 每处理10页记录一次日志
                if (currentPage % 10 == 0) {
                    log.info("Processed {} pages of materials", currentPage);
                }
            }

            // 更新Redis缓存
            Map<String, String> cacheMap = categoryTypeCountMap.entrySet().stream()
                .collect(Collectors.toMap(
                    Map.Entry::getKey,
                    entry -> entry.getValue().toString()
                ));

            redisTemplate.delete(MATERIAL_COUNT_CACHE_KEY);
            if (!cacheMap.isEmpty()) {
                redisTemplate.opsForHash().putAll(MATERIAL_COUNT_CACHE_KEY, cacheMap);
            }

            log.info("Successfully updated material category count cache, processed {} categories with types", cacheMap.size());
        } catch (Exception e) {
            log.error("Failed to update material category count cache", e);
        } finally {
            // 释放分布式锁
            redisTemplate.delete(MATERIAL_COUNT_LOCK_KEY);
        }
    }
} 