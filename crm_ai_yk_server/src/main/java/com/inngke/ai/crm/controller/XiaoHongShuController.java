package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.api.xhs.dto.XhsQrCodeLoginState;
import com.inngke.ai.crm.api.xhs.dto.XiaoHongShuLoginDto;
import com.inngke.ai.crm.api.xhs.dto.XiaoHongShuPublishNotifyRequest;
import com.inngke.ai.crm.db.crm.entity.AiGenerateTask;
import com.inngke.ai.crm.db.crm.manager.AiGenerateTaskManager;
import com.inngke.ai.crm.db.crm.manager.UserManager;
import com.inngke.ai.crm.dto.request.*;
import com.inngke.ai.crm.dto.response.CrmXiaoHongShuListDto;
import com.inngke.ai.crm.dto.response.CrmXiaoHongShuPublishDto;
import com.inngke.ai.crm.dto.response.GetUserXiaoHongShuInfoDto;
import com.inngke.ai.crm.dto.response.PublishInfoDto;
import com.inngke.ai.crm.service.XiaoHongShuService;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * <AUTHOR>
 * @chapter AI
 * @section 小红书
 * @since 2023-12-20 15:18
 **/
@RestController
@RequestMapping("/api/ai/xiaoHongShu")
public class XiaoHongShuController {


    @Autowired
    private XiaoHongShuService xiaoHongShuService;

    @Autowired
    private UserManager userManager;

    @Autowired
    private AiGenerateTaskManager aiGenerateTaskManager;

    /**
     * 用户小红书信息
     */
    @GetMapping("info")
    public BaseResponse<GetUserXiaoHongShuInfoDto> getUserXiaoHongShuInfo(@RequestAttribute JwtPayload jwtPayload,
                                                                          GetUserXiaoHongShuInfoRequest request,
                                                                          @RequestHeader(value = "x-forwarded-for", required = false) String ip) {
        request.setUserId(jwtPayload.getCid());
        request.setIp(ip);
        return xiaoHongShuService.getUserXiaoHongShuInfo(request);
    }

    /**
     * 获取小红书登录二维码
     */
    @GetMapping("login/qr-code")
    public BaseResponse<XhsQrCodeLoginState> getLoginQrCode(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestParam(value = "taskId", required = false) Long taskId ,
            @RequestHeader(value = "x-forwarded-for", required = false) String ip) {
        return xiaoHongShuService.getLoginQrCode(jwtPayload, taskId, ip);
    }

    /**
     * 获取小红书登录二维码状态
     */
    @GetMapping("login/qr-code/state")
    public BaseResponse<XhsQrCodeLoginState> getLoginQrCode(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestHeader(value = "x-forwarded-for", required = false) String ip) {
        return xiaoHongShuService.getLoginQrCodeState(jwtPayload,ip);
    }

    /**
     * 小红书扫码登录回调
     */
    @PostMapping("/login/qr-code/callback/{userId:\\d+}")
    public BaseResponse<Boolean> qrCodeLoginCallback(
            @PathVariable Long userId, @RequestBody XiaoHongShuLoginDto request) {
        return BaseResponse.success(xiaoHongShuService.qrCodeLoginCallback(userId, request));
    }

    /**
     * 小红书登录获取手机号验证码
     */
    @PostMapping("mobile-code")
    public BaseResponse<Boolean> sendMobileCode(@RequestAttribute JwtPayload jwtPayload,
                                                @RequestBody CrmSendMobileCodeRequest request,
                                                @RequestHeader(value = "x-forwarded-for", required = false) String ip) {
        request.setUserId(jwtPayload.getCid());
        request.setIp(ip);
        return xiaoHongShuService.sendMobileCode(request);
    }

    /**
     * 小红书登录
     */
    @PostMapping("login")
    public BaseResponse<GetUserXiaoHongShuInfoDto> xiaoHongShuLogin(@RequestAttribute JwtPayload jwtPayload,
                                                                    @RequestBody CrmMobileLoginRequest request,
                                                                    @RequestHeader(value = "x-forwarded-for", required = false) String ip) {
        request.setUserId(jwtPayload.getCid());
        request.setIp(ip);
        return xiaoHongShuService.xiaoHongShuLogin(request);
    }

    /**
     * 发布笔记
     */
    @PostMapping("publish")
    public BaseResponse<CrmXiaoHongShuPublishDto> publish(@RequestAttribute JwtPayload jwtPayload,
                                                          @RequestBody CrmXiaoHongShuPublishRequest request,
                                                          @RequestHeader(value = "x-forwarded-for", required = false) String ip) {
        request.setUserId(jwtPayload.getCid());
        request.setIp(ip);
        return xiaoHongShuService.publish(request);
    }

    /**
     * 发布笔记-测试方法
     */
    @PostMapping("publish-test")
    public BaseResponse<CrmXiaoHongShuPublishDto> publish(@RequestBody CrmXiaoHongShuPublishRequest request) {
        AiGenerateTask byId = aiGenerateTaskManager.getById(request.getAiGenerateTaskId());
        if (Objects.isNull(byId)) {
            return BaseResponse.error("文章不存在");
        }
        request.setUserId(byId.getUserId());
        return xiaoHongShuService.publish(request);
    }

    /**
     * 小红书查询
     */
    @GetMapping("publish")
    public BaseResponse<CrmXiaoHongShuPublishDto> getPublish(@RequestAttribute JwtPayload jwtPayload,
                                                             CrmXiaoHongShuPublishRequest request,
                                                             @RequestHeader(value = "x-forwarded-for", required = false) String ip) {
        request.setIp(ip);
        request.setUserId(jwtPayload.getCid());
        return xiaoHongShuService.getPublish(request);
    }


    /**
     * 小红书发布结果通知
     */
    @PostMapping("notify")
    public BaseResponse<Boolean> publishNotify(@RequestBody XiaoHongShuPublishNotifyRequest request) {
        return xiaoHongShuService.publishNotify(request);
    }


    /**
     * 发布信息
     */
    @GetMapping("publish-info")
    public BaseResponse<PublishInfoDto> publishInfo(@RequestAttribute JwtPayload jwtPayload,
                                                    PublishInfoRequest request,
                                                    @RequestHeader(value = "x-forwarded-for", required = false) String ip) {
        request.setUserId(jwtPayload.getCid());
        request.setIp(ip);
        return xiaoHongShuService.publishInfo(request);
    }

    /**
     * 小红书笔记列表
     */
    @GetMapping("list")
    public BaseResponse<BasePaginationResponse<CrmXiaoHongShuListDto>> getList(@RequestAttribute JwtPayload jwtPayload,
                                                                               XhsListRequest xhsListRequest) {
        return BaseResponse.success(xiaoHongShuService.getList(jwtPayload.getCid(), xhsListRequest));
    }

}
