package com.inngke.ai.crm.service;

import com.inngke.ai.crm.dto.request.video.ScriptCategorySetRequest;
import com.inngke.ai.crm.dto.request.video.VideoCreateWithMaterialRequest;
import com.inngke.ai.crm.dto.request.video.VideoScriptRequest;
import com.inngke.ai.crm.dto.request.video.VideoScriptSaveRequest;
import com.inngke.ai.crm.dto.response.video.VideoScriptDetailDto;
import com.inngke.ai.crm.dto.response.video.VideoScriptDto;
import com.inngke.ip.ai.dify.app.dto.VideoScriptItem;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.request.BaseIdsRequest;
import com.inngke.common.dto.response.BasePaginationResponse;

import java.util.List;

public interface VideoScriptService {
    Boolean saveVideoScripts(JwtPayload jwtPayload, VideoScriptSaveRequest request);

    BasePaginationResponse<VideoScriptDto> list(JwtPayload jwtPayload, VideoScriptRequest request);

    List<VideoScriptItem> create(JwtPayload jwtPayload, VideoCreateWithMaterialRequest request);

    Boolean categorySet(JwtPayload jwtPayload, ScriptCategorySetRequest request);

    Boolean deleteBatch(JwtPayload jwtPayload, BaseIdsRequest request);

    VideoScriptDetailDto getDetail(JwtPayload jwtPayload, Long id);

    Long createDraft(JwtPayload jwtPayload, long scriptId);
}
