/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.dao;

import com.inngke.ai.crm.db.crm.entity.DouYinReleaseLog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.inngke.ai.crm.dto.response.publish.PublishTaskDto;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 抖音发布记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-23
 */
public interface DouYinReleaseLogDao extends BaseMapper<DouYinReleaseLog> {

    @Select("<script>" +
            "select publish_task_id as id, sum(view_count) as view_count, sum(like_count) as like_count, count(*) as published_count," +
            "sum(collection_count) as collection_count, sum(comment_count) as comment_count " +
            "from dou_yin_release_log " +
            "where publish_task_id in " +
            "  <foreach collection=\"publishTaskIds\" item=\"taskId\" separator=\",\" open=\"(\" close=\")\">" +
            "  #{taskId}" +
            "  </foreach> " +
            "group by publish_task_id" +
            "</script>")
    List<PublishTaskDto> getPublishTaskStatistic(@Param("publishTaskIds") List<Long> publishTaskIds);

    @Select("<script>" +
            " select gen_task_id,count(*) video_id from dou_yin_release_log where out_video_id != ''" +
            "  and gen_task_id in " +
            "  <foreach collection=\"genTaskIds\" item=\"taskId\" separator=\",\" open=\"(\" close=\")\">" +
            "  #{taskId}" +
            "  </foreach> " +
            "group by gen_task_id" +
            "</script>")
    List<DouYinReleaseLog> getGenTaskIdCount(@Param("genTaskIds") List<Long> genTaskIds);
}
