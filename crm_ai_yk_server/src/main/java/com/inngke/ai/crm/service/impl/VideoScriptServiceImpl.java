package com.inngke.ai.crm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.inngke.ai.crm.converter.VideoScriptsConverter;
import com.inngke.ai.crm.core.util.DifyUtils;
import com.inngke.ai.crm.core.util.RetryTaskUtils;
import com.inngke.ai.crm.db.crm.entity.DifyAppConf;
import com.inngke.ai.crm.db.crm.entity.Staff;
import com.inngke.ai.crm.db.crm.entity.VideoProjectDraft;
import com.inngke.ai.crm.db.crm.entity.VideoScript;
import com.inngke.ai.crm.db.crm.manager.CategoryManager;
import com.inngke.ai.crm.db.crm.manager.StaffManager;
import com.inngke.ai.crm.db.crm.manager.VideoProjectDraftManager;
import com.inngke.ai.crm.db.crm.manager.VideoScriptManager;
import com.inngke.ai.crm.dto.request.video.ScriptCategorySetRequest;
import com.inngke.ai.crm.dto.request.video.VideoCreateWithMaterialRequest;
import com.inngke.ai.crm.dto.request.video.VideoScriptRequest;
import com.inngke.ai.crm.dto.request.video.VideoScriptSaveRequest;
import com.inngke.ai.crm.dto.response.video.PromptDataDto;
import com.inngke.ai.crm.dto.response.video.VideoProjectDraftDetail;
import com.inngke.ai.crm.dto.response.video.VideoScriptDetailDto;
import com.inngke.ai.crm.dto.response.video.VideoScriptDto;
import com.inngke.ai.crm.service.*;
import com.inngke.ai.crm.service.cache.FreeFormConfigCache;
import com.inngke.ai.dto.utils.FormDataUtils;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.request.BaseIdsRequest;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.JsonUtil;
import com.inngke.ip.ai.dify.app.VideoScriptCreateApp;
import com.inngke.ip.ai.dify.app.dto.VideoScriptItem;
import com.inngke.ip.ai.dify.utils.DifyRequestInputsBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class VideoScriptServiceImpl implements VideoScriptService {
    @Autowired
    private VideoScriptManager videoScriptManager;

    @Autowired
    private StaffManager staffManager;

    @Autowired
    private VideoScriptCreateApp videoScriptCreateApp;

    @Autowired
    private StaffService staffService;

    @Autowired
    private CategoryService categoryService;

    @Autowired
    private CategoryManager categoryManager;

    @Autowired
    private DifyService difyService;

    @Autowired
    private DifyAppConfService difyAppConfService;

    @Autowired
    private FreeFormConfigCache freeFormConfigCache;

    @Autowired
    private VideoProjectDraftManager videoProjectDraftManager;

    private VideoProjectDraftService videoProjectDraftService;

    @Autowired
    private DepartmentCacheFactory departmentCacheFactory;

    @Override
    public Boolean saveVideoScripts(JwtPayload jwtPayload, VideoScriptSaveRequest request) {
        Staff staff = staffService.getStaffByUserIdNotExistThrow(jwtPayload);

        List<Long> scriptIds = request.getScripts().stream().map(VideoScriptItem::getId).collect(Collectors.toList());

        Map<Long, VideoScript> existMap = videoScriptManager.getExistScriptMap(scriptIds);

//        if (Objects.isNull(categoryManager.getById(request.getCategoryId()))) {
//            throw new InngkeServiceException("分类不存在");
//        }

        String allParentStringIds = categoryService.getAllParentStringIds(staff.getOrganizeId(), request.getCategoryId());

        List<VideoScript> existScripts = Lists.newArrayList();
        List<VideoScript> newScripts = Lists.newArrayList();

        request.getScripts().forEach(script -> {
            VideoScript videoScript = VideoScriptsConverter.toVideoScript(script, staff, request, allParentStringIds);

            if (existMap.containsKey(script.getId())) {
                VideoScript existVideoScript = existMap.get(script.getId());
                if (!existVideoScript.getStaffId().equals(staff.getId()) && existVideoScript.getOrganizeId().equals(0L)) {
                    throw new InngkeServiceException("此脚本非当前员工所属");
                }

                videoScript.setCreateTime(null);
                existScripts.add(videoScript);
            } else {
                newScripts.add(videoScript);
            }
        });

        existScripts.forEach(script -> script.setPrompts(JsonUtil.toJsonString(request.getPromptMap())));
        newScripts.forEach(script -> script.setPrompts(JsonUtil.toJsonString(request.getPromptMap())));

        return videoScriptManager.saveScripts(existScripts, newScripts);
    }

    @Override
    public BasePaginationResponse<VideoScriptDto> list(JwtPayload jwtPayload, VideoScriptRequest request) {
        Staff staff = staffService.getStaffByUserId(jwtPayload.getCid());
        if (Objects.isNull(staff)) {
            throw new InngkeServiceException("获取员工信息失败");
        }

        Set<Long> staffDepartmentIds = Sets.newConcurrentHashSet();
        if (!staff.getTester() && Objects.isNull(request.getCreatorId())){
            DepartmentCacheFactory.DepartmentCache cache = departmentCacheFactory.getCache(staff.getOrganizeId().intValue());
            staffDepartmentIds = cache.getAllChildrenIds(Lists.newArrayList(staff.getDepartmentId()));
            staffDepartmentIds.add(staff.getDepartmentId());
        }

        BasePaginationResponse<VideoScriptDto> response = new BasePaginationResponse<>();
        response.setList(Lists.newArrayList());
        response.setTotal(0);

        QueryWrapper<VideoScript> queryWrapper = Wrappers.<VideoScript>query()
                .eq(Objects.nonNull(request.getCreatorId()), VideoScript.STAFF_ID, request.getCreatorId())
                .in(VideoScript.ORGANIZE_ID, Lists.newArrayList(staff.getOrganizeId(), 0L))
                .in(!CollectionUtils.isEmpty(staffDepartmentIds), VideoScript.DEPARTMENT_ID, staffDepartmentIds)
                .eq(Objects.nonNull(request.getDifyAppId()), VideoScript.DIFY_APP_ID, request.getDifyAppId())
                .gt(StringUtils.isNotEmpty(request.getCreateTimeStart()), VideoScript.CREATE_TIME, DateTimeUtils.toLocalDateTime(request.getCreateTimeStart()))
                .lt(StringUtils.isNotEmpty(request.getCreateTimeEnd()), VideoScript.CREATE_TIME, DateTimeUtils.toLocalDateTime(request.getCreateTimeEnd()))
                .and(StringUtils.isNotEmpty(request.getKeyword()), query -> query
                        .like(VideoScript.SCRIPT_CONTENT, request.getKeyword()).or()
                        .like(VideoScript.TITLE, request.getKeyword())
                ).like(Objects.nonNull(request.getCateId()),
                        VideoScript.CATE_IDS, InngkeAppConst.COMMA_STR + request.getCateId() + InngkeAppConst.COMMA_STR
                ).orderByDesc(VideoScript.ID);

        response.setTotal(videoScriptManager.count(queryWrapper));
        if (response.getTotal() == 0) {
            return response;
        }

        List<VideoScript> scriptList = videoScriptManager.list(
                queryWrapper
                        .lt(VideoScript.ID, Optional.ofNullable(request.getLastId()).orElse(Long.MAX_VALUE))
                        .last("limit " + request.getPageSize())
        );
        List<Long> staffIds = scriptList.stream().map(VideoScript::getStaffId).collect(Collectors.toList());

        Map<Long, String> staffNameMap = staffManager.getByIds(staffIds).stream().collect(Collectors.toMap(Staff::getId, Staff::getName));

        response.setList(scriptList.stream().map(script -> {
            VideoScriptDetailDto videoScriptDto = VideoScriptsConverter.toVideoScriptDto(script);
            videoScriptDto.setCreatorName(staffNameMap.get(videoScriptDto.getStaffId()));
            return videoScriptDto;
        }).collect(Collectors.toList()));

        return response;
    }

    @Override
    public List<VideoScriptItem> create(JwtPayload jwtPayload, VideoCreateWithMaterialRequest request) {
        Map<String, Object> formQuery = request.getPromptMap();
        Integer appId = Optional.ofNullable(formQuery.get(FormDataUtils.FORM_KEY_APP_ID)).map(Object::toString).map(Integer::valueOf).orElse(0);
        DifyAppConf difyApp = difyAppConfService.getVideoAppConf(appId);
        if (difyApp == null) {
            throw new InngkeServiceException("无法找到视频类型");
        }

        Set<String> excludeKeys = freeFormConfigCache.get();
        String query = DifyUtils.getDifyQuery(difyApp.getName(), difyApp.getFormColumnConfig(), formQuery, excludeKeys);
        if (StringUtils.isEmpty(query)) {
            throw new InngkeServiceException("脚本生成请求为空！");
        }
//        if (formQuery.get("scriptType") == null) {
//            throw new InngkeServiceException("脚本类型不能为空！");
//        }

        DifyRequestInputsBuilder builder = DifyRequestInputsBuilder.newBuilder();

        formQuery.forEach((key, value) -> {
            if (value == null) {
                value = InngkeAppConst.EMPTY_STR;
            }
            builder.set(key, value.toString());
        });
        builder.set("query", query);
        String difyUserId = difyService.getUser(jwtPayload.getCid());
        return RetryTaskUtils.process(o -> videoScriptCreateApp.execute(difyUserId, builder.build()));
    }

    @Override
    public Boolean categorySet(JwtPayload jwtPayload, ScriptCategorySetRequest request) {
        Staff staff = staffService.getStaffByUserIdNotExistThrow(jwtPayload);

        String cateIds = categoryService.getAllParentStringIds(staff.getOrganizeId(), request.getCategoryId());

        return videoScriptManager.update(Wrappers.<VideoScript>update()
                .eq(VideoScript.ORGANIZE_ID, staff.getOrganizeId())
                .in(VideoScript.ID, request.getScriptIds())
                .set(VideoScript.CATE_ID, request.getCategoryId())
                .set(VideoScript.CATE_IDS, cateIds)
        );
    }

    @Override
    public Boolean deleteBatch(JwtPayload jwtPayload, BaseIdsRequest request) {
        Staff staff = staffService.getStaffByUserIdNotExistThrow(jwtPayload);
        if (CollectionUtils.isEmpty(request.getIds())) {
            return true;
        }
        return videoScriptManager.remove(Wrappers.<VideoScript>query()
                .in(VideoScript.ID, request.getIds())
        );
    }

    @Override
    public VideoScriptDetailDto getDetail(JwtPayload jwtPayload, Long id) {
        VideoScript videoScript = videoScriptManager.getById(id);
        if (Objects.isNull(videoScript)) {
            throw new InngkeServiceException("找不到脚本");
        }

        DifyAppConf difyApp = difyAppConfService.getVideoAppConf(videoScript.getDifyAppId());

        Set<String> excludeKeys = freeFormConfigCache.get();
        Map inputs = JsonUtil.jsonToObject(videoScript.getPrompts(), Map.class);
        List<PromptDataDto> promptDataList = DifyUtils.getPromptDataList(difyApp.getFormColumnConfig(), inputs, excludeKeys);

        Staff staff = staffManager.getById(videoScript.getStaffId());

        VideoScriptDetailDto videoScriptDto = VideoScriptsConverter.toVideoScriptDto(videoScript);
        videoScriptDto.setPromptData(promptDataList);
        videoScriptDto.setPromptMap(inputs);
        Optional.ofNullable(staff).map(Staff::getName).ifPresent(videoScriptDto::setCreatorName);

        return videoScriptDto;
    }

    @Override
    public Long createDraft(JwtPayload jwtPayload, long scriptId) {
        Staff staff = staffService.getStaffByUserIdNotExistThrow(jwtPayload);
        VideoScript videoScript = videoScriptManager.getById(scriptId);
        if (Objects.isNull(videoScript)) {
            throw new InngkeServiceException("找不到脚本");
        }

        VideoProjectDraftDetail draftDetail = videoProjectDraftService.getDefaultDraft(jwtPayload.getCid());
        VideoCreateWithMaterialRequest request = draftDetail.getProjectContent();
        request.getPromptMap().put(FormDataUtils.FORM_KEY_ASIDES, videoScript.getScriptContent());

        Long draftId = draftDetail.getId();
        LocalDateTime now = LocalDateTime.now();
        VideoProjectDraft draft = new VideoProjectDraft()
                .setId(draftId)
                .setStaffId(staff.getId())
                .setOrganizeId(staff.getOrganizeId())
                .setType(1)
                .setTitle(null)
                .setCoverImage(null)
                .setProjectContext(JsonUtil.toJsonString(request))
                .setCreateType(20)
                .setCreateFromId(scriptId)
                .setVideoScriptId(scriptId)
                .setDeleted(false)
                .setCreateTime(now)
                .setUpdateTime(now);
        videoProjectDraftManager.save(draft);
        return draftId;
    }
}
