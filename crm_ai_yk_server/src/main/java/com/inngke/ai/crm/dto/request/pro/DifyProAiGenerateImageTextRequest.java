package com.inngke.ai.crm.dto.request.pro;

import com.inngke.ai.crm.dto.request.AiGenerateImageTextRequest;

public class DifyProAiGenerateImageTextRequest extends AiGenerateImageTextRequest{

    /**
     * 企业id
     */
    private Long organizeId;
    private String appKey;

    public Long getOrganizeId() {
        return organizeId;
    }

    public DifyProAiGenerateImageTextRequest setOrganizeId(Long organizeId) {
        this.organizeId = organizeId;
        return this;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getAppKey() {
        return appKey;
    }
}
