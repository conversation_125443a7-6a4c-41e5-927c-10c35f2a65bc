package com.inngke.ai.crm.service.material;

import com.google.common.collect.Maps;
import com.inngke.ai.crm.dto.request.material.*;
import com.inngke.ai.crm.dto.response.material.MaterialDto;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BasePaginationResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class MaterialServiceProxy implements MaterialService {

    @Autowired
    @Qualifier("imageMaterialServiceImpl")
    private MaterialService imageMaterialService;
    @Autowired
    @Qualifier("videoMaterial2ServiceImpl")
    private MaterialService videoMaterialService;

    private final Map<String, MaterialService> materialServiceMap = Maps.newHashMap();

    @PostConstruct
    public void init() {
        materialServiceMap.put("image", imageMaterialService);
        materialServiceMap.put("video", videoMaterialService);
    }


    @Override
    public BasePaginationResponse<MaterialDto> pagingMaterials(JwtPayload jwtPayload, PagingMaterialRequest request) {
        return getMaterialService(request).pagingMaterials(jwtPayload, request);
    }

    @Override
    public boolean addMaterials(JwtPayload jwtPayload, AddMaterialRequest request) {
        request.setUrls(request.getUrls().stream().filter(StringUtils::isNotBlank).collect(Collectors.toList()));
        return getMaterialService(request).addMaterials(jwtPayload, request);
    }

    @Override
    public boolean deleteMaterials(JwtPayload jwtPayload, DeleteMaterialRequest request) {
        return getMaterialService(request).deleteMaterials(jwtPayload, request);
    }

    @Override
    public boolean batchSetMaterialCategory(JwtPayload jwtPayload, BatchSetMaterialCategoryRequest request) {
        return getMaterialService(request).batchSetMaterialCategory(jwtPayload, request);
    }

    @Override
    public List<MaterialDto> listMaterials(JwtPayload jwtPayload, ListMaterialRequest request) {
        return getMaterialService(request).listMaterials(jwtPayload, request);
    }

    @Override
    public Integer countMaterials(JwtPayload jwtPayload, ListMaterialRequest request) {
        return getMaterialService(request).countMaterials(jwtPayload, request);
    }

    @Override
    public Boolean rotate(JwtPayload jwtPayload, RotateMaterialRequest request) {
        return getMaterialService(request).rotate(jwtPayload, request);
    }

    @Override
    public Boolean rotate2(JwtPayload jwtPayload, RotateMaterialRequest request) {
        return getMaterialService(request).rotate2(jwtPayload, request);
    }

    private MaterialService getMaterialService(BaseMaterialRequest request) {
        MaterialService materialService = materialServiceMap.get(request.getType());
        if (materialService == null) {
            throw new RuntimeException("素材类型错误");
        }

        return materialService;
    }
}
