package com.inngke.ai.crm.service.message.sender;

import com.inngke.ai.crm.service.message.CrmMessageTypeEnum;
import com.inngke.ai.crm.service.message.content.CrmDouYinOAuthSmsContext;
import com.inngke.ai.crm.service.message.content.CrmMessageContext;
import com.inngke.ip.reach.dto.request.TemplateMessageSendRequest;
import org.springframework.stereotype.Component;

@Component
public class CrmDouYinOAuthMsgSender extends CrmMessageSenderServiceAbs{
    @Override
    public CrmMessageTypeEnum getMessageType() {
        return CrmMessageTypeEnum.DOU_YIN_OAUTH_SMS;
    }

    @Override
    public void init(CrmMessageContext ctx) {

    }

    @Override
    public TemplateMessageSendRequest sendMessage(CrmMessageContext context) {
        CrmDouYinOAuthSmsContext ctx = (CrmDouYinOAuthSmsContext)context;

        return getTemplateRequestBuilder()
                .setMobile(ctx.getMobile())
                .setVar("code",ctx.getLink())
                .build();
    }
}
