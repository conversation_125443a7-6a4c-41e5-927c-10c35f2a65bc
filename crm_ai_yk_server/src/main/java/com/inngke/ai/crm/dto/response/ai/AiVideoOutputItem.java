package com.inngke.ai.crm.dto.response.ai;

import com.inngke.ai.crm.dto.response.video.DouYinDataDto;
import lombok.Data;

import java.io.Serializable;

@Data
public class AiVideoOutputItem implements Serializable {

    /**
     * 视频id
     */
    private Long id;

    /**
     * 视频内容（用于发小红书、抖音）
     */
    private String videoContent;

    /**
     * 生成的视频地址
     */
    private String videoUrl;

    /**
     * 1080P视频地址
     */
    private String video1080Url;

    /**
     * 主批次序号，从1开始递增
     */
    private Integer batchNo;

    /**
     * 子批次序号，从1开始递增
     */
    private Integer subBatchNo;

    /**
     * 抖音数据
     */
    private DouYinDataDto douYinDataDto;

    /**
     * 发布状态
     */
    private Integer releaseStat;

    /**
     * 发布时间
     */
    private Long releaseTime;
}
