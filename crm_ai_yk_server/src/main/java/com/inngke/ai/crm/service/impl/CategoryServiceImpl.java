package com.inngke.ai.crm.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.ai.crm.converter.CategoryConverter;
import com.inngke.ai.crm.db.crm.entity.Category;
import com.inngke.ai.crm.db.crm.entity.Staff;
import com.inngke.ai.crm.db.crm.manager.CategoryManager;
import com.inngke.ai.crm.dto.request.base.CategoryQuery;
import com.inngke.ai.crm.dto.request.base.CategorySaveRequest;
import com.inngke.ai.crm.dto.response.CategoryNode;
import com.inngke.ai.crm.dto.response.common.CategoryDto;
import com.inngke.ai.crm.service.CategoryService;
import com.inngke.ai.crm.service.StaffService;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.SnowflakeIdService;
import com.inngke.common.utils.DateTimeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.time.LocalDateTime;

@Service
public class CategoryServiceImpl implements CategoryService {

    @Autowired
    private CategoryManager categoryManager;
    @Autowired
    private StaffService staffService;
    @Autowired
    private SnowflakeIdService snowflakeIdService;

    @Override
    public List<CategoryNode> getTree(JwtPayload jwtPayload, CategoryQuery request) {
        Staff staff = staffService.getStaffByUserId(jwtPayload.getCid());
        if (Objects.isNull(staff)) {
            throw new InngkeServiceException("获取员工信息失败");
        }

        Category category = new Category();
        category.setId(0L);
        category.setName("默认目录");
        category.setParentId(-1L);
        List<Category> categoryList = categoryManager.getStaffListIfNotExistInit(request.getType(), staff);
        categoryList.add(category);

        return buildCategoryTree(categoryList);
    }

    @Override
    public List<CategoryDto> getList(JwtPayload jwtPayload, String type) {
        Staff staff = staffService.getStaffByUserId(jwtPayload.getCid());
        if (Objects.isNull(staff)) {
            throw new InngkeServiceException("获取员工信息失败");
        }

        return categoryManager.getStaffListIfNotExistInit(type, staff).stream().map(this::toCategoryDto).collect(Collectors.toList());
    }

    private CategoryDto toCategoryDto(Category category){
        CategoryDto categoryDto = new CategoryDto();
        categoryDto.setId(category.getId());
        categoryDto.setOrganizeId(category.getOrganizeId());
        categoryDto.setStaffId(category.getStaffId());
        categoryDto.setType(category.getType());
        categoryDto.setName(category.getName());
        categoryDto.setIcon(category.getIcon());
        categoryDto.setParentId(category.getParentId());
        categoryDto.setSort(category.getSort());
        categoryDto.setCreateTime(DateTimeUtils.getMilli(category.getCreateTime()));
        return categoryDto;
    }

    @Override
    public Long save(JwtPayload jwtPayload, CategorySaveRequest request) {
        Staff staff = staffService.getStaffByUserId(jwtPayload.getCid());
        if (Objects.isNull(staff)) {
            throw new InngkeServiceException("获取员工信息失败");
        }

        Category category;
        if (request.getId() == null) {
            category = new Category();
            category.setId(snowflakeIdService.getId());
            category.setCreateTime(LocalDateTime.now());
        } else {
            category = categoryManager.getById(request.getId());
            if (category == null) {
                throw new InngkeServiceException("分类不存在");
            }
        }

        if (Objects.nonNull(request.getParentId()) && request.getParentId() > 0L) {
            if (Objects.isNull(categoryManager.getById(request.getParentId()))) {
                throw new InngkeServiceException("父级不存在");
            }
        }

        CategoryConverter.fillCategory(category, staff, request);

        categoryManager.saveOrUpdate(category);

        return category.getId();
    }

    @Override
    public String getAllParentStringIds(Long organizeId, Long categoryId) {
        if (Objects.isNull(categoryId) || categoryId == 0L){
            return ",0,";
        }
        Category category = categoryManager.getOne(
                Wrappers.<Category>query()
                        .eq(Category.ID, categoryId)
                        .eq(Category.ORGANIZE_ID, organizeId)
        );

        if (category == null) {
            throw new InngkeServiceException("目录错误");
        }

        List<Long> allCategoryIds = categoryManager.getAllParentIds(categoryId);
        allCategoryIds.add(categoryId);

        return  InngkeAppConst.COMMA_STR + allCategoryIds.stream() .map(Object::toString)
                .collect(Collectors.joining(InngkeAppConst.COMMA_STR)) + InngkeAppConst.COMMA_STR;
    }

    @Override
    public Boolean delete(JwtPayload jwtPayload, String type, long categoryId) {
        Staff staff = staffService.getStaffByUserId(jwtPayload.getCid());
        if (Objects.isNull(staff)) {
            throw new InngkeServiceException("获取员工信息失败");
        }


        Category category = categoryManager.getOne(
                Wrappers.<Category>query()
                        .eq(Category.ID, categoryId)
                        .eq(Category.ORGANIZE_ID, staff.getOrganizeId())
                        .eq(Category.TYPE, type)
                        .select(Category.ID)
        );
        if (category == null) {
            throw new InngkeServiceException("要删除的分类不存在或已经删除！");
        }
        categoryManager.removeById(categoryId);
        return true;
    }

    private List<CategoryNode> buildCategoryTree(List<Category> categories) {
        List<CategoryNode> roots = new ArrayList<>();

        Map<Long, CategoryNode> map = categories.stream().map(CategoryConverter::toCategoryNode)
                .collect(Collectors.toMap(CategoryNode::getId, Function.identity()));

        for (Category category : categories) {
            CategoryNode node = map.get(category.getId());
            if (category.getParentId() == null || category.getParentId() == -1L) {
                roots.add(node);
            } else {
                CategoryNode parent = map.get(category.getParentId());
                if (parent != null) {
                    parent.getChildren().add(node);
                }
            }
        }

        return roots;
    }

}
