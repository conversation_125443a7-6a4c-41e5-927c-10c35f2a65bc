package com.inngke.ai.crm.controller.devops;

import com.inngke.ai.crm.dto.request.devops.UpdateMaterialDirTreeRequest;
import com.inngke.ai.crm.dto.request.material.GetMaterialDirTreeRequest;
import com.inngke.ai.crm.dto.response.material.MaterialDirTreeDto;
import com.inngke.ai.crm.service.devops.DevopsMaterialDirService;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @chapter DevOps
 * @section 素材
 */
@RestController
@RequestMapping("/api/ai/devops/material/dir")
public class DevOpsDirController {

    @Autowired
    private DevopsMaterialDirService devopsMaterialDirService;

    @GetMapping("/tree")
    public BaseResponse<List<MaterialDirTreeDto>> getMaterialDirTree() {
        return BaseResponse.success(devopsMaterialDirService.getMaterialDirTree());
    }

    @PutMapping
    public BaseResponse<Boolean> updateMaterialDirTree(@RequestBody UpdateMaterialDirTreeRequest request) {
        return devopsMaterialDirService.updateMaterialDirTree(request);
    }
}
