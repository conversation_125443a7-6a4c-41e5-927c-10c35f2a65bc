package com.inngke.ai.crm.service.material.task;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.ai.crm.api.video.dto.FfmpegVideoInfoDto;
import com.inngke.ai.crm.db.crm.entity.UserMaterial;
import com.inngke.ai.crm.db.crm.manager.UserMaterialManager;
import com.inngke.ai.crm.dto.VideoMaterialTask;
import com.inngke.common.utils.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component(UserMaterialRotateTaskHandler.TASK_TYPE)
public class UserMaterialRotateTaskHandler extends BaseMaterialRotateTaskHandler {
    private static final Logger logger = LoggerFactory.getLogger(UserMaterialRotateTaskHandler.class);
    public static final String TASK_TYPE = "userVideoMaterialRotate";

    @Autowired
    private UserMaterialManager userMaterialManager;

    @Override
    public String taskType() {
        return TASK_TYPE;
    }

    @Override
    public void doSuccess(VideoMaterialTask<Integer> task, FfmpegVideoInfoDto videoInfo) {
        logger.info("[materialId={}]旋转成功：{}", task.getId(), JsonUtil.toJsonString(videoInfo));
        //替换长宽
        userMaterialManager.update(Wrappers.<UserMaterial>update()
                .eq(UserMaterial.ID, task.getId())
                .set(UserMaterial.WIDTH, videoInfo.getWidth())
                .set(UserMaterial.HEIGHT, videoInfo.getHeight())
                .set(UserMaterial.ROTATE, videoInfo.getRotate())
        );
    }
}
