package com.inngke.ai.crm.service;

import com.inngke.ai.crm.dto.request.GroupChatIdRequest;
import com.inngke.ai.crm.dto.request.groupchat.GroupChatChangeRequest;
import com.inngke.common.dto.response.BaseResponse;

public interface GroupChatService {

    /**
     * 群解散
     * @param request
     * @return
     */
    BaseResponse dismiss(GroupChatIdRequest request);

    /**
     * 添加群
     * @param request
     * @return
     */
    BaseResponse add(GroupChatIdRequest request);

    /**
     * 群变更
     * @param request
     * @return
     */
    BaseResponse change(GroupChatChangeRequest request);
}
