/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager.impl;

import com.inngke.ai.crm.db.crm.entity.VideoWidget;
import com.inngke.ai.crm.db.crm.dao.VideoWidgetDao;
import com.inngke.ai.crm.db.crm.manager.VideoWidgetManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
@Service
public class VideoWidgetManagerImpl extends ServiceImpl<VideoWidgetDao, VideoWidget> implements VideoWidgetManager {

    @Override
    public VideoWidget random(long organizeId, int type) {
        return getBaseMapper().random(organizeId, type);
    }
}
