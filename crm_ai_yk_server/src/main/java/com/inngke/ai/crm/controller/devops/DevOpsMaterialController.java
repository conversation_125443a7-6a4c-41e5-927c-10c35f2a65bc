package com.inngke.ai.crm.controller.devops;

import com.inngke.ai.crm.dto.request.material.*;
import com.inngke.ai.crm.dto.response.material.MaterialDto;
import com.inngke.ai.crm.service.material.MaterialService;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @chapter 素材模块
 * @section 企业素材
 */
@RequestMapping("/api/ai/material")
@RestController
public class DevOpsMaterialController {

    @Autowired
    @Qualifier("materialServiceProxy")
    private MaterialService materialService;

    /**
     * 查询素材列表(分页)
     */
    @GetMapping("/paging")
    public BaseResponse<BasePaginationResponse<MaterialDto>> pagingMaterials(
            @RequestAttribute JwtPayload jwtPayload,
            PagingMaterialRequest request) {
        return BaseResponse.success(materialService.pagingMaterials(jwtPayload, request));
    }

    /**
     * 查询素材列表(lastId)
     */
    @GetMapping("/list")
    public BaseResponse<List<MaterialDto>> listMaterials(
            @RequestAttribute JwtPayload jwtPayload,
            ListMaterialRequest request) {
        return BaseResponse.success(materialService.listMaterials(jwtPayload, request));
    }

    /**
     * 添加素材
     */
    @PostMapping
    public BaseResponse<Boolean> addMaterials(
            @RequestAttribute JwtPayload jwtPayload,
            @Validated @RequestBody AddMaterialRequest request) {
        return BaseResponse.success(materialService.addMaterials(jwtPayload, request));
    }

    /**
     * 删除素材
     */
    @DeleteMapping("/batch")
    public BaseResponse<Boolean> deleteMaterials(
            @RequestAttribute JwtPayload jwtPayload,
            @Validated @RequestBody DeleteMaterialRequest request) {
        return BaseResponse.success(materialService.deleteMaterials(jwtPayload, request));
    }

    /**
     * 批量设置素材分类
     */
    @PutMapping("/batch/category")
    public BaseResponse<Boolean> batchSetImageMaterialCategory(
            @RequestAttribute JwtPayload jwtPayload,
            @Validated @RequestBody BatchSetMaterialCategoryRequest request) {
        return BaseResponse.success(materialService.batchSetMaterialCategory(jwtPayload, request));
    }

    /**
     * 旋转素材
     */
    @PutMapping("/rotate")
    public BaseResponse<Boolean> rotate(
            @RequestAttribute JwtPayload jwtPayload,
            @Validated @RequestBody RotateMaterialRequest request) {
        return BaseResponse.success(materialService.rotate(jwtPayload, request));
    }

    /**
     * 旋转素材2
     */
    @PutMapping("/rotate2")
    public BaseResponse<Boolean> rotate2(
            @RequestAttribute JwtPayload jwtPayload,
            @Validated @RequestBody RotateMaterialRequest request) {
        return BaseResponse.success(materialService.rotate2(jwtPayload, request));
    }
}
