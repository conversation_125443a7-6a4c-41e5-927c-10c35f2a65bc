package com.inngke.ai.crm.controller.devops;

import com.inngke.ai.crm.api.video.dto.RotateMaterialRequest;
import com.inngke.ai.crm.db.crm.entity.MaterialCategory;
import com.inngke.ai.crm.dto.IdPageDto;
import com.inngke.ai.crm.dto.request.IdPageRequest;
import com.inngke.ai.crm.dto.request.devops.*;
import com.inngke.ai.crm.dto.request.material.PagingMaterialRequest;
import com.inngke.ai.crm.dto.response.devops.VideoInfoDto;
import com.inngke.ai.crm.dto.response.material.MaterialCategoryDto;
import com.inngke.ai.crm.dto.response.material.MaterialCategoryItemDto;
import com.inngke.ai.crm.dto.response.material.MaterialDto;
import com.inngke.ai.crm.dto.response.video.VideoMaterialDto;
import com.inngke.ai.crm.service.MaterialCategoryService;
import com.inngke.ai.crm.service.devops.VideoMaterialService;
import com.inngke.ai.crm.service.material.impl.MaterialCategoryCountScheduler;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @chapter DevOps
 * @section 素材管理
 */
@RestController
@RequestMapping("/api/ai/devops/video-material")
public class DevOpsVideoMaterialController {

    @Autowired
    private VideoMaterialService videoMaterialService;
    @Autowired
    private MaterialCategoryService materialCategoryService;
    @Autowired
    private MaterialCategoryCountScheduler materialCategoryCountScheduler;

    /**
     * 查看素材列表
     */
    @GetMapping("/list")
    public BaseResponse<IdPageDto<MaterialDto>> getVideoMaterialList(GetVideoMaterialRequest request) {
        return BaseResponse.success(videoMaterialService.getVideoMaterialList(request));
    }


    /**
     * 查看素材基本信息
     */
    @GetMapping("/{materialId:\\d+}")
    public BaseResponse<VideoMaterialDto> getVideoMaterialInfo(VideoMaterialIdRequest request) {
        return BaseResponse.success(videoMaterialService.getVideoMaterialInfo(request));
    }


    /**
     * 查看素材文件信息
     */
    @GetMapping("/{materialId:\\d+}/file-info")
    public BaseResponse<VideoInfoDto> getVideoMaterialFileInfo(VideoMaterialIdRequest request) {
        return BaseResponse.success(videoMaterialService.getVideoMaterialFileInfo(request));
    }


    /**
     * 删除素材
     */
    @DeleteMapping("/{materialId:\\d+}")
    public BaseResponse<Boolean> deleteMaterial(DeleteVideoMaterialRequest request) {
        return BaseResponse.success(videoMaterialService.deleteMaterial(request));
    }

    /**
     * 旋转视频
     *
     * @deprecated
     */
    @PutMapping("/rotate")
    public BaseResponse<Boolean> rotateVideoMaterial(@RequestBody RotateMaterialRequest request) {
        return BaseResponse.success(videoMaterialService.rotateVideoMaterial(request) != null);
    }

    /**
     * 编辑视频素材信息
     * materialManagement
     */
    @PutMapping("/{id:\\d+}/info")
    public BaseResponse<VideoMaterialDto> updateVideoMaterial(@PathVariable("id") Long id, @RequestBody UpdateVideoMaterialRequest request) {
        request.setId(id);
        return BaseResponse.success(videoMaterialService.updateVideoMaterial(request));
    }

    /**
     * 编辑视频素材分类
     * materialManagement
     */
    @PutMapping("/{id:\\d+}/classify")
    public BaseResponse<VideoMaterialDto> updateVideoClassify(@PathVariable("id") Long id, @RequestBody UpdateVideoClassifyRequest request) {
        request.setId(id);
        return BaseResponse.success(videoMaterialService.updateVideoClassify(request));
    }

    /**
     * 保存错误信息
     */
    @PutMapping("/save/error")
    public BaseResponse<VideoMaterialDto> saveErrorContent(@RequestBody SaveErrorContentRequest request) {
        return BaseResponse.success(videoMaterialService.saveErrorContent(request));
    }

    /**
     * 素材分类 top
     */
    @GetMapping("/category/top/list")
    public BaseResponse<List<MaterialCategoryDto>> getVideoMaterialTopCategoryList(PagingMaterialRequest request) {
        return BaseResponse.success(videoMaterialService.getVideoMaterialTopCategoryList(request));
    }

    /**
     * 企业素材分类树
     *
     * @param organizeId 企业ID
     */
    @GetMapping("/category/tree/{organizeId:\\d+}")
    public BaseResponse<List<MaterialCategoryItemDto>> getVideoMaterialCategoryTree(@PathVariable Long organizeId) {
        return BaseResponse.success(materialCategoryService.listCategory(organizeId, MaterialCategory.TYPE_VIDEO));
    }

    /**
     * 添加素材(devops)
     */
    @PostMapping
    public BaseResponse<VideoMaterialDto> addVideoMaterial(@RequestBody AddVideoMaterialRequest request) {
        return BaseResponse.success(videoMaterialService.addVideoMaterial(request));
    }

    /**
     * 判断素材是否存在
     */
    @GetMapping("/is/{md5}/exist")
    public BaseResponse<Boolean> isExist(@PathVariable String md5) {
        return BaseResponse.success(videoMaterialService.isExist(md5));
    }

    /**
     * 通过id获取素材列表
     */
    @GetMapping("/list-last-id")
    public BaseResponse<List<VideoMaterialDto>> getVideoMaterialListByLastId(IdPageRequest request) {
        return BaseResponse.success(videoMaterialService.getVideoMaterialListByLastId(request));
    }

    @GetMapping("/count-quantity")
    public void countQuantity() {
        AsyncUtils.runAsync(() -> materialCategoryCountScheduler.updateMaterialCategoryCount());
    }
}
