package com.inngke.ai.crm.service.schedule;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.ai.crm.core.CrmServiceConsts;
import com.inngke.ai.crm.db.crm.entity.DigitalPersonVideo;
import com.inngke.ai.crm.db.crm.manager.DigitalPersonVideoManager;
import com.inngke.ai.crm.service.ChanJingService;
import com.inngke.ai.crm.service.dp.DigitalPersonProducerService;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.dto.Lock;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.LockService;
import com.inngke.common.utils.JsonUtil;
import com.inngke.ip.ai.chanjing.dto.response.VideoInfoDto;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

@Component
public class DigitalPersonSchedule {

    private static final Logger logger = LoggerFactory.getLogger(DigitalPersonSchedule.class);

    @Autowired
    private DigitalPersonVideoManager digitalPersonVideoManager;
    @Autowired
    private ChanJingService chanJingService;
    @Autowired
    private DigitalPersonProducerService digitalPersonProducerService;
    @Autowired
    private LockService lockService;
    @Value("${spring.profiles.active:dev}")
    private String profileActive;

    @Autowired
    @Qualifier("aiThreadPool")
    private Executor executor;

    @Scheduled(fixedDelay = 5000)
    public void check() {
        if (profileActive.equals("dev")){
            return;
        }
        Lock lock = lockService.getLock(CrmServiceConsts.CACHE_LOCK_KEY_PRE + "digital-check", 60);
        if (Objects.isNull(lock)) {
            return;
        }
        try {
            List<DigitalPersonVideo> genProgressList = getGenProgressList();
            if (CollectionUtils.isEmpty(genProgressList)) {
                return;
            }
            CountDownLatch cd = new CountDownLatch(genProgressList.size());
            for (DigitalPersonVideo digitalPersonVideo : genProgressList) {
                AsyncUtils.runAsync(() -> {
                    try {
                        digitalPersonProducerService.checkState(digitalPersonVideo);
                    }catch (Exception e){
                        logger.error("同步数字人状态发生错误", e);
                    } finally {
                        cd.countDown();
                    }
                }, executor, false);
            }
            cd.await(5, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            throw new InngkeServiceException(e);
        }
    }

    private List<DigitalPersonVideo> getGenProgressList() {
        return digitalPersonVideoManager.list(
                Wrappers.<DigitalPersonVideo>query()
                        .gt(DigitalPersonVideo.CREATE_TIME, LocalDateTime.now().minusHours(1))
                        .eq(DigitalPersonVideo.STATE, 0)
        );
    }
}
