package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.dto.request.creation.task.GetStaffCreationTaskListRequest;
import com.inngke.ai.crm.dto.response.creation.task.StaffCreationTaskDto;
import com.inngke.ai.crm.service.creation.task.CreationTaskService;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @chapter 企业模块
 * @section 员工创作任务
 **/
@RestController
@RequestMapping("/api/ai/creation-task/staff")
public class StaffCreationTaskController {

    @Resource
    private CreationTaskService creationTaskService;

    /**
     * 获取员工任务列表
     */
    @GetMapping("/list")
    public BaseResponse<BasePaginationResponse<StaffCreationTaskDto>> getStaffCreationTaskList(
            @RequestAttribute JwtPayload jwtPayload,
            GetStaffCreationTaskListRequest request) {
        return creationTaskService.getStaffCreationTaskList(jwtPayload, request);
    }
}
