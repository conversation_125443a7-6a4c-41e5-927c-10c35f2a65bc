package com.inngke.ai.crm.service.devops.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.ai.crm.db.crm.entity.TtsConfig;
import com.inngke.ai.crm.db.crm.manager.TtsConfigManager;
import com.inngke.ai.crm.dto.request.devops.AddTtsConfigRequest;
import com.inngke.ai.crm.dto.request.devops.GetTtsConfigListRequest;
import com.inngke.ai.crm.dto.request.devops.UpdateTtsConfigRequest;
import com.inngke.ai.crm.dto.response.devops.TtsConfigListDto;
import com.inngke.ai.crm.service.devops.DevOpsTtsService;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * TTS配置管理服务实现
 */
@Service
public class DevOpsTtsServiceImpl implements DevOpsTtsService {

    @Autowired
    private TtsConfigManager ttsConfigManager;

    @Override
    public BaseResponse<BasePaginationResponse<TtsConfigListDto>> getTtsConfigList(GetTtsConfigListRequest request) {
        // 构建查询条件
        QueryWrapper<TtsConfig> wrapper = Wrappers.<TtsConfig>query();
        
        // 筛选条件
        if (Objects.nonNull(request.getOrganizeId())) {
            wrapper.eq(TtsConfig.ORGANIZE_ID, request.getOrganizeId());
        }
        if (Objects.nonNull(request.getPlatform())) {
            wrapper.eq(TtsConfig.PLATFORM, request.getPlatform());
        }
        if (Objects.nonNull(request.getGender())) {
            wrapper.eq(TtsConfig.GENDER, request.getGender());
        }
        if (Objects.nonNull(request.getStatus())){
            wrapper.eq(TtsConfig.STATUS, request.getStatus());
        }
        
        // 模糊搜索
        if (StringUtils.isNotBlank(request.getKeyword())) {
            wrapper.and(w -> w
                    .like(TtsConfig.VOICE_TYPE, request.getKeyword())
                    .or()
                    .like(TtsConfig.TITLE, request.getKeyword())
                    .or()
                    .eq(TtsConfig.ID, request.getKeyword())
            );
        }
        
        // 排序
        wrapper.orderByDesc(TtsConfig.ID);
        
        // 查询总数
        int count = ttsConfigManager.count(wrapper);
        
        // 分页查询
        int offset = (request.getPageNo() - 1) * request.getPageSize();
        wrapper.last("limit " + offset + "," + request.getPageSize());
        
        List<TtsConfig> records = ttsConfigManager.list(wrapper);
        
        // 转换为DTO
        List<TtsConfigListDto> dtoList = records.stream()
                .map(this::convertToListDto)
                .collect(Collectors.toList());
        
        // 构建分页响应
        BasePaginationResponse<TtsConfigListDto> response = new BasePaginationResponse<>();
        response.setList(dtoList);
        response.setTotal(count);
        
        return BaseResponse.success(response);
    }

    @Override
    public BaseResponse<Boolean> addTtsConfig(AddTtsConfigRequest request) {
        // 检查是否存在相同的配置
        int count = ttsConfigManager.count(
                Wrappers.<TtsConfig>query()
                        .eq(TtsConfig.ORGANIZE_ID, request.getOrganizeId())
                        .eq(TtsConfig.VOICE_TYPE, request.getVoiceType())
                        .eq(TtsConfig.PLATFORM, request.getPlatform())
        );
        
        if (count > 0) {
            throw new InngkeServiceException("该企业下已存在相同的音色配置");
        }
        
        // 创建实体
        TtsConfig ttsConfig = new TtsConfig();
        BeanUtils.copyProperties(request, ttsConfig);
        if (request.getPlatform() == 3){
            ttsConfig.setCluster("volcano_icl");
        }
        
        // 保存
        boolean success = ttsConfigManager.save(ttsConfig);
        
        return BaseResponse.success(success);
    }

    @Override
    public BaseResponse<Boolean> updateTtsConfig(UpdateTtsConfigRequest request) {
        // 检查记录是否存在
        TtsConfig existConfig = ttsConfigManager.getById(request.getId());
        if (existConfig == null) {
            throw new InngkeServiceException("TTS配置不存在");
        }
        
        // 检查是否存在相同的配置（排除自己）
        int count = ttsConfigManager.count(
                Wrappers.<TtsConfig>query()
                        .ne(TtsConfig.ID, request.getId())
                        .eq(TtsConfig.ORGANIZE_ID, request.getOrganizeId())
                        .eq(TtsConfig.VOICE_TYPE, request.getVoiceType())
                        .eq(TtsConfig.PLATFORM, request.getPlatform())
        );
        
        if (count > 0) {
            throw new InngkeServiceException("该企业下已存在相同的音色配置");
        }
        
        // 更新实体
        TtsConfig ttsConfig = new TtsConfig();
        BeanUtils.copyProperties(request, ttsConfig);
        
        // 更新
        boolean success = ttsConfigManager.updateById(ttsConfig);
        
        return BaseResponse.success(success);
    }

    /**
     * 转换为列表DTO
     */
    private TtsConfigListDto convertToListDto(TtsConfig ttsConfig) {
        TtsConfigListDto dto = new TtsConfigListDto();
        BeanUtils.copyProperties(ttsConfig, dto);
        
        // 设置平台名称
        switch (ttsConfig.getPlatform()) {
            case 1:
                dto.setPlatformName("Azure");
                break;
            case 2:
                dto.setPlatformName("火山云");
                break;
            case 4:
                dto.setPlatformName("即创");
                break;
            default:
                dto.setPlatformName("未知");
        }
        
        // 设置性别名称
        if (ttsConfig.getGender() != null) {
            dto.setGenderName(ttsConfig.getGender() == 1 ? "男性" : "女性");
        }
        
        // 设置状态名称
        if (ttsConfig.getStatus() != null) {
            dto.setStatusName(ttsConfig.getStatus() == 1 ? "启用" : "停用");
        }
        
        return dto;
    }
} 