package com.inngke.ai.crm.service;

import com.inngke.ai.crm.db.crm.entity.AiGenerateVideoOutput;
import com.inngke.ai.crm.db.crm.entity.VideoQualityAudit;
import com.inngke.ai.crm.dto.request.video.VideoQualityAuditRequest;
import com.inngke.ai.crm.dto.request.video.VideoQuery4DevOpsRequest;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BasePaginationResponse;

import java.util.List;

public interface VideoDevopsService {
    List<AiGenerateVideoOutput> videoList4Devops(VideoQuery4DevOpsRequest request);

    Boolean qualityAudit(JwtPayload jwtPayload, VideoQualityAuditRequest request);

    List<VideoQualityAudit> getQualityAuditList(Long videoGenerateId, boolean includeDeleted);

    Boolean fixCount();
}
