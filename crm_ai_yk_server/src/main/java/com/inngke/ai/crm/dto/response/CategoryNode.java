package com.inngke.ai.crm.dto.response;

import com.google.common.collect.Lists;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CategoryNode implements Serializable {

    /**
     * 分类ID
     */
    private Long id;

    /**
     * 父级分类ID
     */
    private Long parentId;

    /**
     * 分类名称
     */
    private String name;

    /**
     * 分类类型
     */
    private String type;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 子节点
     */
    private List<CategoryNode> children = Lists.newArrayList();
}
