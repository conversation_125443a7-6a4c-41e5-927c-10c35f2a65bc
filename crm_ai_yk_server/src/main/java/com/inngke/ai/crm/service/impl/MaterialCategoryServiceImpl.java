package com.inngke.ai.crm.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.inngke.ai.crm.core.CrmServiceConsts;
import com.inngke.ai.crm.db.crm.entity.MaterialCategory;
import com.inngke.ai.crm.db.crm.manager.MaterialCategoryManager;
import com.inngke.ai.crm.db.crm.manager.UserManager;
import com.inngke.ai.crm.dto.request.material.AddCategoryRequest;
import com.inngke.ai.crm.dto.request.material.EditCategoryRequest;
import com.inngke.ai.crm.dto.request.material.ListMaterialRequest;
import com.inngke.ai.crm.dto.response.material.MaterialCategoryDto;
import com.inngke.ai.crm.dto.response.material.MaterialCategoryItemDto;
import com.inngke.ai.crm.service.MaterialCategoryService;
import com.inngke.ai.crm.service.material.MaterialService;
import com.inngke.common.core.utils.SnowflakeHelper;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.exception.InngkeServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class MaterialCategoryServiceImpl implements MaterialCategoryService {

    private static final String CATEGORY_CACHE_KEY = CrmServiceConsts.CACHE_KEY_PRE + "material:category:";

    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private MaterialCategoryManager materialCategoryManager;
    @Autowired
    private UserManager userManager;
    @Autowired
    @Qualifier("materialServiceProxy")
    private MaterialService materialService;

    @Override
    public Long addCategory(JwtPayload jwtPayload, AddCategoryRequest request) {
        Long organizeId = userManager.getUserOrganizeId(jwtPayload.getCid());
        if (materialCategoryManager.exist(organizeId, null, request.getPid(), request.getName(), request.getType())) {
            throw new InngkeServiceException("分类已经存在");
        }

        MaterialCategory materialCategory = new MaterialCategory();
        materialCategory.setId(SnowflakeHelper.getId());
        materialCategory.setName(request.getName());
        materialCategory.setType(request.getType());
        materialCategory.setSort(request.getSort());
        materialCategory.setOrganizeId(userManager.getUserOrganizeId(jwtPayload.getCid()));
        materialCategory.setCreateTime(LocalDateTime.now());
        materialCategory.setParentId(request.getPid());

        materialCategoryManager.save(materialCategory);
        delCache(organizeId, request.getType());
        return materialCategory.getId();
    }

    @Override
    public Long editCategory(JwtPayload jwtPayload, EditCategoryRequest request) {
        Long organizeId = userManager.getUserOrganizeId(jwtPayload.getCid());

        MaterialCategory materialCategory = materialCategoryManager.getById(request.getId());
        if (!materialCategory.getOrganizeId().equals(organizeId)) {
            throw new InngkeServiceException("分类不存在");
        }

        if (materialCategoryManager.exist(materialCategory.getOrganizeId(), request.getId(), materialCategory.getParentId(), request.getName(), request.getType())) {
            throw new InngkeServiceException("分类已经存在");
        }

        materialCategoryManager.update(Wrappers.<MaterialCategory>update()
                .eq(MaterialCategory.ID, request.getId())
                .set(MaterialCategory.NAME, request.getName())
                .set(MaterialCategory.SORT, request.getSort())
        );
        delCache(organizeId, request.getType());
        return request.getId();
    }

    @Override
    public Boolean deleteCategory(JwtPayload jwtPayload, Long id) {
        MaterialCategory materialCategory = materialCategoryManager.getById(id);
        if (Objects.isNull(materialCategory)){
            return true;
        }

        if (materialCategoryManager.count(Wrappers.<MaterialCategory>query().eq(MaterialCategory.PARENT_ID,id)) > 0){
            throw new InngkeServiceException("当前类别下存在子类，不允许删除！");
        }

        ListMaterialRequest request = new ListMaterialRequest();
        request.setCategoryIds(Lists.newArrayList(id));
        request.setType(materialCategory.getType());

        if (materialService.countMaterials(jwtPayload,request) > 0){
            throw new InngkeServiceException("当前类别有关联素材，不允许删除！");
        }

        return materialCategoryManager.removeById(id);
    }

    @Override
    public List<MaterialCategoryItemDto> listCategory(JwtPayload jwtPayload, String type) {
        Long userOrganizeId = userManager.getUserOrganizeId(jwtPayload.getCid());

        String cacheKey = CATEGORY_CACHE_KEY + userOrganizeId + ":" + type;
        ValueOperations operations = redisTemplate.opsForValue();
        List<MaterialCategoryItemDto> categoryList = (List<MaterialCategoryItemDto>) operations.get(cacheKey);
        if(Objects.nonNull(categoryList)){
            return categoryList;
        }else {
            categoryList = listCategory(userOrganizeId, type);
            operations.set(cacheKey, categoryList, 60, TimeUnit.SECONDS);
        }

        return categoryList;
    }

    @Override
    public List<MaterialCategoryItemDto> listCategory(Long organizeId, String type) {
        List<MaterialCategory> materialCategoryList = materialCategoryManager.getByOrganizeIdType(organizeId, type);

        List<MaterialCategoryItemDto> categoryTree = new ArrayList<>();
        Map<Long, MaterialCategoryItemDto> categoryMap = materialCategoryList.stream()
                .map(this::convertToDto)
                .collect(Collectors.toMap(
                        MaterialCategoryDto::getId,
                        Function.identity(),
                        (existing, replacement) -> existing,
                        LinkedHashMap::new
                ));

        for (MaterialCategoryItemDto category : categoryMap.values()) {
            if (Objects.isNull(category.getPid()) || category.getPid() == 0) {
                categoryTree.add(category);
            } else {
                MaterialCategoryItemDto parent = categoryMap.get(category.getPid());
                if (Objects.isNull(parent.getChildren())) {
                    parent.setChildren(Lists.newArrayList());
                }
                parent.getChildren().add(category);
            }
        }

        return categoryTree;
    }

    @Override
    public List<MaterialCategoryItemDto> getFullPath(Long id) {
        List<MaterialCategoryItemDto> path = new ArrayList<>();
        if (Objects.isNull(id)) {
            return path;
        }

        MaterialCategory category = materialCategoryManager.getById(id);
        if (Objects.isNull(category)) {
            return path;
        }

        // Add current category
        path.add(convertToDto(category));

        // Recursively add all parent categories
        Long parentId = category.getParentId();
        while (Objects.nonNull(parentId) && parentId != 0) {
            MaterialCategory parentCategory = materialCategoryManager.getById(parentId);
            if (Objects.isNull(parentCategory)) {
                break;
            }
            path.add(0, convertToDto(parentCategory));
            parentId = parentCategory.getParentId();
        }

        return path;
    }

    @Override
    public List<MaterialCategory> getCategoryList(JwtPayload jwtPayload, String videoType) {
        Long userOrganizeId = userManager.getUserOrganizeId(jwtPayload.getCid());
        return materialCategoryManager.getByOrganizeIdType(userOrganizeId, videoType);
    }

    private MaterialCategoryItemDto convertToDto(MaterialCategory category) {
        MaterialCategoryItemDto dto = new MaterialCategoryItemDto();
        dto.setId(category.getId());
        dto.setPid(category.getParentId());
        dto.setName(category.getName());
        dto.setSort(category.getSort());
        return dto;
    }

    private void delCache(Long organizeId, String type) {
        redisTemplate.delete(CATEGORY_CACHE_KEY + organizeId + ":" + type);
    }
}
