/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager;

import com.inngke.ai.crm.db.crm.entity.ContentModeration;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 内容安全检测记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-04
 */
public interface ContentModerationManager extends IService<ContentModeration> {

    void saveLog(Long id, String content, String jsonString);
}
