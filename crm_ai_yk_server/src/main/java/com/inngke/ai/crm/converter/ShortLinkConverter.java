package com.inngke.ai.crm.converter;

import com.inngke.ai.crm.db.crm.entity.ShortLink;
import com.inngke.ai.crm.dto.response.ShortLinkDto;

/**
 * <AUTHOR>
 * @Date 2024/3/21 19:13
 */
public class ShortLinkConverter {

    public static ShortLinkDto toDto(ShortLink shortLink) {
        ShortLinkDto shortLinkDto = new ShortLinkDto();
        shortLinkDto.setLink(shortLink.getLink());
        shortLinkDto.setUserId(shortLink.getUserId());
        return shortLinkDto;
    }
}
