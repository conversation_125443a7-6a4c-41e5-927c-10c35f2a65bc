/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 字体配置
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class TextFont implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 字体名称
     */
    private String name;

    /**
     * 所属企业，0表示通用
     */
    private Long organizeId;

    /**
     * 图标地址
     */
    private String iconUrl;

    /**
     * 字体文件URL
     */
    private String fontPath;

    /**
     * 应用类型：0=字幕 1=大字报 2=封面图
     */
    private Integer type;

    /**
     * 是否已删除： 0=否 1=是
     */
    private Boolean deleted;

    /**
     * 排序值，越大越前
     */
    private Integer sortOrder;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    public static final String ID = "id";

    public static final String NAME = "name";

    public static final String ORGANIZE_ID = "organize_id";

    public static final String ICON_URL = "icon_url";

    public static final String FONT_PATH = "font_path";

    public static final String TYPE = "type";

    public static final String DELETED = "deleted";

    public static final String SORT_ORDER = "sort_order";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

}
