package com.inngke.ai.crm.service;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.ai.crm.db.crm.entity.DouYinAccount;
import com.inngke.ai.crm.db.crm.manager.DouYinAccountManager;
import com.inngke.ip.ai.douyin.dto.response.DouYinTokenDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Objects;

@Service
public class TpAccountService {

    @Resource
    private DouYinAccountManager douYinAccountManager;
    @Resource
    private DouYinService douYinService;

    public Boolean refreshDouYinAccount(String openId) {
        DouYinAccount douYinAccount = douYinAccountManager.getByOpenId(openId);

        return refreshDouYinAccount(douYinAccount);
    }

    public Boolean refreshDouYinAccount(DouYinAccount douYinAccount) {
        if (Objects.isNull(douYinAccount)){
            return false;
        }
        UpdateWrapper<DouYinAccount> updateWrapper = Wrappers.update();
        updateWrapper.eq(DouYinAccount.ID, douYinAccount.getId()).set(DouYinAccount.UPDATE_TIME, LocalDateTime.now());
        LocalDateTime today = LocalDate.now().atTime(LocalTime.MAX);

        String refreshTokenStr = douYinAccount.getRefreshToken();

        //提前一天刷新token
        LocalDateTime refreshTokenExpireAt = douYinAccount.getRefreshTokenExpireAt();
        if (refreshTokenExpireAt.minusDays(1L).isBefore(today)){
            DouYinTokenDto refreshToken = douYinService.refreshToken(DouYinService.RENEW_REFRESH_TOKEN, refreshTokenStr);
            if (Objects.nonNull(refreshToken)){
                updateWrapper.set(DouYinAccount.REFRESH_TOKEN,refreshToken.getRefreshToken());
                updateWrapper.set(DouYinAccount.REFRESH_TOKEN_EXPIRE_AT,LocalDateTime.now().plusSeconds(refreshToken.getExpiresIn()));
                refreshTokenStr = refreshToken.getRefreshToken();
            }
        }

        LocalDateTime accessTokenExpireAt = douYinAccount.getAccessTokenExpireAt();
        if (accessTokenExpireAt.minusDays(1L).isBefore(today)){
            DouYinTokenDto accessToken = douYinService.refreshToken(DouYinService.REFRESH_TOKEN, refreshTokenStr);
            if (Objects.nonNull(accessToken)){
                updateWrapper.set(DouYinAccount.ACCESS_TOKEN, accessToken.getAccessToken());
                updateWrapper.set(DouYinAccount.ACCESS_TOKEN_EXPIRE_AT, LocalDateTime.now().plusSeconds(accessToken.getExpiresIn()));
            }
        }

        return douYinAccountManager.update(updateWrapper);
    }
}
