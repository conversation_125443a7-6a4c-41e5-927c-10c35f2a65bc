package com.inngke.ai.crm.dto.response.point;

import java.io.Serializable;
import java.time.LocalDateTime;

public class PointGoodsDto implements Serializable {
    /**
     * 积分商品ID
     *
     * @demo 1234342
     */
    private Long id;

    /**
     * 积分商品名称
     *
     * @demo 积分商品
     */
    private String name;

    /**
     * 积分商品封面URL
     *
     * @demo https://www.inngke.com/1.jpg
     */
    private String image;

    /**
     * 积分商品描述
     *
     * @demo 这是一个积分商品
     */
    private String description;

    /**
     * 创建时间
     *
     * @demo 1514736000000
     */
    private Long createTime;

    /**
     * 更新时间
     *
     * @demo 1514736000000
     */
    private Long updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }
}
