package com.inngke.ai.crm.service.video.creator;

import com.google.common.collect.Maps;
import com.inngke.ai.crm.dto.enums.VideoDraftTypeEnum;
import com.inngke.ai.crm.service.VideoCreateService;
import com.inngke.ai.dto.enums.VideoTypeEnum;
import com.inngke.common.exception.InngkeServiceException;
import com.tencentcloudapi.cii.v20210408.models.MachinePredict;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class VideoCreatorFactory {

    private final List<VideoCreatorService> videoCreatorServices;

    private final Map<VideoDraftTypeEnum, VideoCreatorService> videoCreatorServiceMap;

    public VideoCreatorFactory(List<VideoCreatorService> videoCreatorServices) {
        this.videoCreatorServices = videoCreatorServices;
        this.videoCreatorServiceMap = Maps.newHashMap();
        videoCreatorServices.forEach(videoCreatorService -> {
            for (VideoDraftTypeEnum videoDraftTypeEnum : videoCreatorService.getCreateType()) {
                this.videoCreatorServiceMap.put(videoDraftTypeEnum, videoCreatorService);
            }
        });
    }

    public VideoCreatorService get(VideoDraftTypeEnum draftType) {
        return Optional.ofNullable(videoCreatorServiceMap.get(draftType)).orElseThrow(() -> new InngkeServiceException("视频类型不存在"));
    }

    public VideoCreatorService getByVideoType(Integer videoType) {
        if (videoType == VideoTypeEnum.MUSIC_BEAT_VIDEO.getVideoType()) {
            return videoCreatorServiceMap.get(VideoDraftTypeEnum.MUSIC_BEAT);
        }

        return videoCreatorServiceMap.get(VideoDraftTypeEnum.STORYBOARD);
    }
}
