package com.inngke.ai.crm.core.util;

import com.inngke.ai.crm.dto.response.FfmpegVideoDetailDto;
import com.inngke.common.service.JsonService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-10-16 14:25
 **/
public class ProcessBuilderUtils {

    private static final Logger logger = LoggerFactory.getLogger(ProcessBuilderUtils.class);


    public static String execProcess(List<String> commands) throws IOException, InterruptedException {
        StringBuilder output = new StringBuilder();
        ProcessBuilder processBuilder = new ProcessBuilder();
        processBuilder.command(commands);
        processBuilder.redirectErrorStream(true);
        Process process = processBuilder.start();
        InputStream inputStream = process.getInputStream();
        BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
        String line;
        while ((line = reader.readLine()) != null) {
            logger.info(line);
            output.append(line).append("\n");
        }
        int exitCode = process.waitFor();
        if (exitCode != 0) {
            throw new IOException("Command execution failed with exit code " + exitCode);
        }
        return output.toString();
    }

    public static String ffmpegVideoDetail(String file) throws IOException, InterruptedException {
        List<String> list = new ArrayList<>();
        return execProcess(list);
    }

    public static FfmpegVideoDetailDto ffmpegVideoDetail(String file, JsonService service) throws IOException, InterruptedException {
        // ffprobe -i "a.mp4" -v quiet -print_format json -show_format -show_streams
        List<String> list = new ArrayList<>();
        String json = execProcess(list);
        return service.toObject(json, FfmpegVideoDetailDto.class);
    }


}
