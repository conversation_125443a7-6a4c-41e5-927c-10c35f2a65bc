package com.inngke.ai.crm.controller.devops;

import com.inngke.ai.crm.dto.request.devops.GetTemplateRequest;
import com.inngke.ai.crm.dto.request.digital.person.AddDistalPersonTagRequest;
import com.inngke.ai.crm.dto.request.digital.person.AddDpTemplateRequest;
import com.inngke.ai.crm.dto.request.digital.person.UpdateDistalPersonTagRequest;
import com.inngke.ai.crm.dto.request.digital.person.UpdateDpTemplateRequest;
import com.inngke.ai.crm.dto.response.DpTagDto;
import com.inngke.ai.crm.dto.response.DpTemplateDto;
import com.inngke.ai.crm.service.devops.DevOpsDigitalPersonService;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @chapter 数字人
 * @section 数字人接口(DevOps)
 */
@RestController
@RequestMapping("/api/ai/devops/digital-person")
@Validated
public class DevopsDigitalPersonController {

    @Autowired
    private DevOpsDigitalPersonService devOpsDigitalPersonService;

    /**
     * 添加数字人模板
     */
    @PostMapping("/template")
    public BaseResponse<Boolean> addTemplate(@Validated @RequestBody AddDpTemplateRequest request) {
        return devOpsDigitalPersonService.addTemplate(request);
    }

    /**
     * 修改数字人
     */
    @PutMapping("/template")
    public BaseResponse<Boolean> updateTemplate(@Validated @RequestBody UpdateDpTemplateRequest request) {
        return devOpsDigitalPersonService.updateTemplate(request);
    }

    /**
     * 获取数字人模板列表
     */
    @GetMapping("/template")
    public BaseResponse<List<DpTemplateDto>> getTemplateList(GetTemplateRequest request) {
        return devOpsDigitalPersonService.getTemplateList(request);
    }

    /**
     * 删除模版配置
     */
    @DeleteMapping("/template/{id:\\d+}")
    public BaseResponse<Boolean> deleteTemplate(@PathVariable Long id) {
        return devOpsDigitalPersonService.deleteTemplate(id);
    }

    /**
     * 获取数字人标签列表
     */
    @GetMapping("/tag")
    public BaseResponse<List<DpTagDto>> getDpTagList() {
        return devOpsDigitalPersonService.getDpTagList();
    }

    @PostMapping("/tag")
    public BaseResponse<Boolean> addDpTag(@RequestBody AddDistalPersonTagRequest request) {
        return devOpsDigitalPersonService.addDpTag(request);
    }

    @PutMapping("/tag")
    public BaseResponse<Boolean> updateDpTag(@RequestBody UpdateDistalPersonTagRequest request) {
        return devOpsDigitalPersonService.updateDpTag(request);
    }

    @DeleteMapping("/tag/{id:\\d+}")
    public BaseResponse<Boolean> deleteDpTag(@PathVariable Long id){
        return devOpsDigitalPersonService.deleteDpTag(id);
    }

}
