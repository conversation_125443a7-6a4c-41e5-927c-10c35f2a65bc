package com.inngke.ai.crm.controller;

import com.google.common.collect.Lists;
import com.inngke.ai.crm.core.config.AiGcIndexConfig;
import com.inngke.ai.crm.db.crm.entity.DifyAppConf;
import com.inngke.ai.crm.db.crm.manager.AppConfigManager;
import com.inngke.ai.crm.db.crm.manager.DifyAppConfManager;
import com.inngke.ai.crm.db.crm.manager.UserManager;
import com.inngke.ai.crm.dto.enums.AppConfigCodeEnum;
import com.inngke.ai.crm.dto.request.CrmArticleListRequest;
import com.inngke.ai.crm.dto.response.*;
import com.inngke.ai.crm.dto.response.pro.DifyAppConfDto;
import com.inngke.ai.crm.service.CrmArticleService;
import com.inngke.ai.crm.service.DifyService;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @chapter 小程序
 * @section 首页
 * <AUTHOR>
 * @since 2023-09-06 09:56
 **/
@RestController
@RequestMapping("/api/ai/index")
public class CrmIndexController {

    @Autowired
    private AiGcIndexConfig aiGcIndexConfig;

    @Autowired
    private DifyAppConfManager difyAppConfManager;

    @Autowired
    private UserManager userManager;

    @Autowired
    private AppConfigManager appConfigManager;

    @Autowired
    private CrmArticleService articleService;

    /**
     * 首页
     */
    @GetMapping
    public BaseResponse<AiGcIndexDto> index(@RequestAttribute JwtPayload jwtPayload) {
        AiGcIndexDto result = new AiGcIndexDto();
        result.setBanner(aiGcIndexConfig.getList());
        return BaseResponse.success(result);
    }


    /**
     * 首页配置
     */
    @GetMapping("index-conf")
    public BaseResponse<CrmIndexProductConfigDto> indexProductConfig(@RequestAttribute JwtPayload jwtPayload){
        CrmIndexProductConfigDto result = new CrmIndexProductConfigDto();
        Long userOrganizeId = userManager.getUserOrganizeId(jwtPayload.getCid());

        String banner = appConfigManager.getValueByCode(AppConfigCodeEnum.MP_INDEX_BANNER.getCode() + InngkeAppConst.DOT_STR + userOrganizeId);
        String module = appConfigManager.getValueByCode(AppConfigCodeEnum.MP_INDEX_MODULE.getCode() + InngkeAppConst.DOT_STR + userOrganizeId);
        List<String> moduleList = null;
        if (StringUtils.isNotBlank(module)) {
            moduleList = Arrays.asList(StringUtils.split(module, ","));
        }

        result.setBanner(new BannerDto().setUrl(Optional.ofNullable(banner).orElse(appConfigManager.getValueByCode(AppConfigCodeEnum.MP_INDEX_BANNER.getCode()))));
        result.setModuleList(Optional.ofNullable(moduleList).orElse(Arrays.asList(StringUtils.split(appConfigManager.getValueByCode(AppConfigCodeEnum.MP_INDEX_MODULE.getCode()), ","))));

        CrmArticleListRequest crmArticleListRequest = new CrmArticleListRequest();
        crmArticleListRequest.setPageNo(1);
        crmArticleListRequest.setPageSize(3);
        List<CrmArticleListDto> crmArticleList = articleService.list(crmArticleListRequest).getData().getList();
        result.setLeranList(crmArticleList);

        Map<Long, List<DifyAppConf>> organizeArticleTypeGroup = difyAppConfManager.organizeIdGroupMap(userOrganizeId, 2);

        List<DifyAppConfDto> articleTypeList = Optional.ofNullable(organizeArticleTypeGroup.get(userOrganizeId))
                .orElse(Optional.ofNullable(organizeArticleTypeGroup.get(0L)).orElse(Lists.newArrayList()))
                .stream().map(this::toDifyAppConfDto).collect(Collectors.toList());

        articleTypeList.sort(Comparator.comparing(DifyAppConfDto::getSort));
        result.setArticleTypeList(articleTypeList);

        Map<Long, List<DifyAppConf>> organizeVideoTypeGroup = difyAppConfManager.organizeIdGroupMap(userOrganizeId, 10);
        List<DifyAppConfDto> videoTypeList = Optional.ofNullable(organizeVideoTypeGroup.get(userOrganizeId))
                .orElse(Optional.ofNullable(organizeVideoTypeGroup.get(0L)).orElse(Lists.newArrayList()))
                .stream().map(this::toDifyAppConfDto).collect(Collectors.toList());
        videoTypeList.sort(Comparator.comparing(DifyAppConfDto::getSort));
        result.setVideoTypeList(videoTypeList);

        return BaseResponse.success(result);
    }

    private DifyAppConfDto toDifyAppConfDto(DifyAppConf difyAppConf) {
        DifyAppConfDto difyAppConfDto = new DifyAppConfDto();
        difyAppConfDto.setValue(difyAppConf.getId());
        difyAppConfDto.setTitle(difyAppConf.getName());
        difyAppConfDto.setVipProduct(difyAppConf.getVipProduct());
        difyAppConfDto.setDescription(difyAppConf.getDescription());
        difyAppConfDto.setImageUrl(difyAppConf.getImageUrl());
        difyAppConfDto.setBgColor(difyAppConf.getBgColor());
        difyAppConfDto.setSort(difyAppConf.getSort());
        difyAppConfDto.setImageMark(difyAppConf.getImageMark());
        if(Boolean.FALSE.equals(difyAppConf.getVipProduct())){
            difyAppConfDto.setCornerMark("试用");
        }
        return difyAppConfDto;
    }


}
