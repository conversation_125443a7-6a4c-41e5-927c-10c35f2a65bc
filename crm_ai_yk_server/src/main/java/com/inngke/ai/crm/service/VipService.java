package com.inngke.ai.crm.service;

import com.inngke.ai.crm.dto.request.ActivationVipRequest;
import com.inngke.ai.crm.dto.request.BaseUserId;
import com.inngke.ai.crm.dto.request.VipInfoListDto;
import com.inngke.ai.crm.dto.request.base.UserIdRequest;
import com.inngke.ai.crm.dto.response.vip.UserVipInfoDto;
import com.inngke.ai.crm.dto.response.vip.VipInfoDto;
import com.inngke.common.dto.response.BaseResponse;

import java.util.List;

public interface VipService {

    BaseResponse<List<VipInfoListDto>> getVipTypeListMp(BaseUserId request);

    BaseResponse<List<VipInfoListDto>> getVipTypeList(UserIdRequest request);

    BaseResponse<UserVipInfoDto> getUserVipInfo(String activityCode);

    BaseResponse<String> activationVip(ActivationVipRequest request);
}
