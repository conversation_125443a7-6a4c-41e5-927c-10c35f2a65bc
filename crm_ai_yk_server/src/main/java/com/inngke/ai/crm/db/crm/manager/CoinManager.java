package com.inngke.ai.crm.db.crm.manager;

import com.baomidou.mybatisplus.extension.service.IService;
import com.inngke.ai.crm.db.crm.entity.*;
import com.inngke.ai.crm.dto.enums.CoinDispatchTypeEnum;
import com.inngke.ai.crm.dto.request.devops.TransferUserRequest;
import com.inngke.ai.crm.dto.response.ai.GetConsumeCoinDto;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 虚拟币分发记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-29
 */
public interface CoinManager extends IService<Coin> {

    /**
     * 获取用户可用积分总数
     */
    Integer getUserCoin(Long userId);


    /**
     * 判断用户积分是否足够
     *
     * @param consumeCoin 要消耗的积分
     * @param userId      用户Id
     * @return true足够
     */
    boolean checkUserCoin(Integer consumeCoin, Long userId);

    /**
     * 获取可消耗积分的数据库实体
     *
     * @param consumeCoin 要消耗的积分
     * @param userId      用户Id
     * @return 消耗了积分的实体剩余积分，用于更新即可
     */
    List<GetConsumeCoinDto> getConsumeCoin(Integer consumeCoin, Long userId);

    /**
     * 授权手机号获取积分
     */
    void authMobile(Long userId, UserInviteLog inviteLog,String mobile);

    void manualAddCoin(List<Coin> coinList,List<CoinLog> coinLogList);

    Map<Long, Integer> getUserCoinMap(List<Long> userIds);

    LocalDateTime notExpireTime();

    Coin createCoin(Integer coinNum, Long userId, Long dispatchSrcId,
                    CoinDispatchTypeEnum typeEnum,LocalDateTime expireTime);

    /**
     * 获取CoinDispatchTypeEnum枚举类型
     */
    CoinDispatchTypeEnum getTypeByVipType(Integer vipType);

    void coinExpire(List<CoinLog> coinLogList, List<Coin> coinUpdateList);

    void transfer(TransferUserRequest request);

    void collectUserPoints(Long userId);
}
