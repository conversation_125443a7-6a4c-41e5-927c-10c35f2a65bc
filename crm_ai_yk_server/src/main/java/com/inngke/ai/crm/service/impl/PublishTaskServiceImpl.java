package com.inngke.ai.crm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.inngke.ai.crm.converter.PublishTaskConverter;
import com.inngke.ai.crm.core.CrmServiceConsts;
import com.inngke.ai.crm.db.crm.entity.*;
import com.inngke.ai.crm.db.crm.manager.*;
import com.inngke.ai.crm.dto.IdPageDto;
import com.inngke.ai.crm.dto.enums.AiGenerateTaskStatusEnum;
import com.inngke.ai.crm.dto.request.AiGenerateHistoryRequest;
import com.inngke.ai.crm.dto.request.IdPageRequest;
import com.inngke.ai.crm.dto.request.publish.*;
import com.inngke.ai.crm.dto.request.video.GetVideoDetailRequest;
import com.inngke.ai.crm.dto.response.GetHistoryVideoListDto;
import com.inngke.ai.crm.dto.response.publish.*;
import com.inngke.ai.crm.service.*;
import com.inngke.ai.crm.service.es.AiGenerateTaskEsService;
import com.inngke.ai.crm.service.message.CrmMessageManagerService;
import com.inngke.ai.crm.service.message.CrmMessageTypeEnum;
import com.inngke.ai.crm.service.message.content.CrmPublishTaskSmsContext;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.core.utils.EnvUtils;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.Lock;
import com.inngke.common.dto.request.BaseIdRequest;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.enums.EnvEnum;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.LockService;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.ip.common.dto.request.GenMpQrRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.client.RestHighLevelClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class PublishTaskServiceImpl implements PublishTaskService {

    public static final Logger logger = LoggerFactory.getLogger(PublishTaskServiceImpl.class);
    private static final String RE_SELECT_VIDEO_LOCK_KEY = CrmServiceConsts.CACHE_LOCK_KEY_PRE + "reSelectVideo:";

    @Autowired
    private PublishTaskManager publishTaskManager;
    @Autowired
    private PublishVideoManager publishVideoManager;
    @Autowired
    private AiGenerateVideoOutputManager aiGenerateVideoOutputManager;
    @Autowired
    private StaffService staffService;
    @Autowired
    private StaffManager staffManager;
    @Autowired
    private LockService lockService;
    @Autowired
    private AiGenerateTaskEsService aiGenerateTaskEsService;
    @Autowired
    private RestHighLevelClient restHighLevelClient;
    @Autowired
    private AiGenerateTaskManager aiGenerateTaskManager;
    @Autowired
    private AiGenerateTaskReleaseManager aiGenerateTaskReleaseManager;
    @Autowired
    private CommonAuthService commonAuthService;
    @Autowired
    private DouYinReleaseLogManager douYinReleaseLogManager;
    @Autowired
    private VideoService videoService;
    @Autowired
    private DepartmentCacheFactory departmentCacheFactory;
    @Autowired
    private CrmMessageManagerService crmMessageManagerService;
    @Autowired
    private CrmAiGeneratorService crmAiGeneratorService;

    @Override
    public BasePaginationResponse<PublishTaskDto> list(JwtPayload jwtPayload, GetPublishTaskListRequest request) {
        Staff staff = staffService.getStaffByUserIdNotExistThrow(jwtPayload);
        BasePaginationResponse<PublishTaskDto> response = new BasePaginationResponse<>();
        response.setTotal(0);
        response.setList(Lists.newArrayList());

        BasePaginationResponse<PublishTask> basePublishTask = getBasePublishTask(staff, request);
        if (basePublishTask.getTotal() == 0 || basePublishTask.getList().isEmpty()) {
            return response;
        }

        List<Long> publishTaskIds = basePublishTask.getList().stream().map(PublishTask::getId).collect(Collectors.toList());

        Map<Long, PublishTaskDto> publishStatisticsData = getPublishStatisticsData(publishTaskIds);

        Map<Long, Integer> publishVideoCount = publishVideoManager.getCountByPublishTaskIds(publishTaskIds);

        response.setList(
                basePublishTask.getList().stream().map(publishTask -> PublishTaskConverter.toPublishTaskDto(
                        publishTask, publishStatisticsData.get(publishTask.getId()), publishVideoCount.get(publishTask.getId())
                )).collect(Collectors.toList())
        );

        return response;
    }

    @Override
    public PublishTaskDto create(JwtPayload jwtPayload, CreatePublishTaskRequest request) {
        Staff staff = staffService.getStaffByUserIdNotExistThrow(jwtPayload);

        PublishTask publishTask = PublishTaskConverter.toPublishTask(staff, request);

        if (CollectionUtils.isNotEmpty(request.getDepartmentIds())) {
            DepartmentCacheFactory.DepartmentCache departmentCache = departmentCacheFactory.getCache(staff.getOrganizeId().intValue());
            Set<Long> departmentIds = departmentCache.filterDepartmentTopIds(request.getDepartmentIds());
            publishTask.setDepartmentIds(Joiner.on(InngkeAppConst.COMMA_STR).join(departmentIds));
        }

        List<Long> videoIds = checkPublishVideoIds(request.getVideoIds());

        List<PublishVideo> videoList = videoIds.stream().map(videoId ->
                PublishTaskConverter.toPublishTaskVideo(publishTask.getId(), videoId)
        ).collect(Collectors.toList());

        String qrCode = genQrCode(publishTask.getId());
        if (StringUtils.isEmpty(qrCode)) {
            throw new InngkeServiceException("生成任务二维码失败");
        }

        publishTask.setPublishQr(qrCode);

        publishTaskManager.create(publishTask, videoList);
        aiGenerateTaskEsService.updateEsDocByIds(videoIds);

        // 发送短信
        AsyncUtils.runAsync(() -> sendSmsToStaff(publishTask));

        return PublishTaskConverter.toPublishTaskDto(publishTask);
    }

    private void sendSmsToStaff(PublishTask publishTask) {
        //未指定范围不发送短信
        if (StringUtils.isBlank(publishTask.getDepartmentIds())) {
            return;
        }

        List<Long> departmentIds = Splitter.on(InngkeAppConst.COMMA_STR).splitToList(publishTask.getDepartmentIds())
                .stream().map(Long::parseLong).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(departmentIds)) {
            return;
        }

        DepartmentCacheFactory.DepartmentCache departmentCache = departmentCacheFactory.getCache(publishTask.getOrganizeId().intValue());

        Set<Long> allChildrenIds = departmentCache.getAllChildrenIds(departmentIds);

        List<Staff> staffList = staffManager.getOpenedByDepartmentIds(departmentIds);

        staffList.forEach(staff -> {
            CrmPublishTaskSmsContext smsContext = new CrmPublishTaskSmsContext();
            smsContext.setPublishTask(publishTask);
            smsContext.setMessageType(CrmMessageTypeEnum.PUBLISH_TASK_MSG);
            smsContext.setMobile(staff.getMobile());
            crmMessageManagerService.send(smsContext);
        });
    }

    @Override
    public boolean update(JwtPayload jwtPayload, UpdatePublishTaskRequest request) {
        Staff staff = staffService.getStaffByUserIdNotExistThrow(jwtPayload);

        PublishTask publishTask = publishTaskManager.getOrganizeTask(request.getTaskId(), staff.getOrganizeId(), PublishTask.ID);

        publishTask.setName(request.getName());
        publishTask.setDescription(request.getDescription());
        publishTask.setExpirationTime(DateTimeUtils.toLocalDateTime(request.getExpirationTime()));

        return publishTaskManager.updateById(publishTask);
    }

    @Override
    public boolean upAndDownShelves(JwtPayload jwtPayload, Long id) {
        Staff staff = staffService.getStaffByUserIdNotExistThrow(jwtPayload);

        PublishTask publishTask = publishTaskManager.getOrganizeTask(id, staff.getOrganizeId(), PublishTask.ID, PublishTask.STATE);

        if (Objects.isNull(publishTask)) {
            throw new InngkeServiceException("获取任务信息失败");
        }

        publishTask.setState(publishTask.getState() == 0 ? 1 : 0);
        return publishTaskManager.updateById(publishTask);
    }

    @Override
    public boolean addVideo(AddPublishTaskVideoRequest request) {
        //todo 检查视频 2. 更新数据库
        return false;
    }

    @Override
    public PublishVideoDto receiveVideo(JwtPayload jwtPayload, Long id) {
        PublishVideoDto receivedVideo = new PublishVideoDto();
        receivedVideo.setReceiveState(0);

        PublishTask publishTask = publishTaskManager.getById(id);
        if (Objects.isNull(publishTask)) {
            receivedVideo.setTaskState(3);
            return receivedVideo;
        }

        String departmentIds = publishTask.getDepartmentIds();
        if (StringUtils.isNotBlank(departmentIds)) {
            Staff staff = staffService.getStaffByUserId(jwtPayload.getCid());
            if (Objects.isNull(staff)){
                receivedVideo.setReceiveState(4);
                return receivedVideo;
            }

            DepartmentCacheFactory.DepartmentCache cache = departmentCacheFactory.getCache(staff.getOrganizeId().intValue());
            Set<Long> allChildrenIdsAndSelfIds = cache.getAllChildrenIdsAndSelfIds(
                    Splitter.on(InngkeAppConst.COMMA_STR).splitToList(departmentIds).stream().map(Long::valueOf).collect(Collectors.toList())
            );

            if (!allChildrenIdsAndSelfIds.contains(staff.getDepartmentId())){
                receivedVideo.setReceiveState(4);
                return receivedVideo;
            }
        }

        //下架
        if (publishTask.getState().equals(0)) {
            receivedVideo.setReceiveState(2);
            return receivedVideo;
        }

        //获取已经领取的任务
        receivedVideo = getReceivedVideo(jwtPayload, id);
        if (Objects.nonNull(receivedVideo.getVideoDetail())){
            receivedVideo.setReceiveState(0);
            return receivedVideo;
        }

        //过期
        if (LocalDateTime.now().isAfter(publishTask.getExpirationTime())){
            receivedVideo.setReceiveState(1);
            return receivedVideo;
        }

        //随机获取一个视频
        PublishVideo publishVideo = publishVideoManager.randomReceivePublishTaskVideo(id, jwtPayload.getCid());
        //获取视频失败
        if (Objects.isNull(publishVideo)) {
            receivedVideo.setReceiveState(3);
            return receivedVideo;
        }


        GetVideoDetailRequest getVideoDetailRequest = new GetVideoDetailRequest();
        getVideoDetailRequest.setId(publishVideo.getGenTaskId());
        getVideoDetailRequest.setPublishTaskId(publishTask.getId());

        receivedVideo.setVideoDetail(videoService.detail(jwtPayload, getVideoDetailRequest).getData());
        receivedVideo.setPublishVideoState(publishVideo.getPublishState());
        receivedVideo.setLockTime(DateTimeUtils.getMilli(publishVideo.getLockTime()));
        return receivedVideo;
    }

    @Override
    public PublishVideoDto getReceivedVideo(JwtPayload jwtPayload, Long taskId) {
        PublishVideoDto publishVideoDto = new PublishVideoDto();

        PublishTask publishTask = publishTaskManager.getById(taskId);
        if (Objects.isNull(publishTask)) {
            throw new InngkeServiceException("来晚了～！很遗憾，您错过视频发布截止日期，下次再来吧！");
        }
        publishVideoDto.setTaskState(publishTask.getState());
        publishVideoDto.setExpirationTime(DateTimeUtils.getMilli(publishTask.getExpirationTime()));

        //已经领取
        PublishVideo receivedVideo = publishVideoManager.userReceived(jwtPayload.getCid(), publishTask.getId());
        if (Objects.nonNull(receivedVideo)) {
            GetVideoDetailRequest request = new GetVideoDetailRequest();
            request.setId(receivedVideo.getGenTaskId());
            request.setPublishTaskId(receivedVideo.getPublishTaskId());
            publishVideoDto.setVideoDetail(videoService.detail(jwtPayload, request).getData());
            publishVideoDto.setPublishVideoState(receivedVideo.getPublishState());
            publishVideoDto.setLockTime(DateTimeUtils.getMilli(receivedVideo.getLockTime()));
            return publishVideoDto;
        }

        return publishVideoDto;
    }

    @Override
    public BasePaginationResponse<PendingVideoDto> getPendingVideo(JwtPayload jwtPayload, GetPendingVideoListRequest request) {
        Staff staff = staffService.getStaffByUserIdNotExistThrow(jwtPayload);

        BasePaginationResponse<PendingVideoDto> response = new BasePaginationResponse<>();
        response.setList(Lists.newArrayList());
        response.setTotal(0);

        int publishTaskPendingVideoCount = aiGenerateTaskManager.getPublishTaskPendingVideoCount(staff.getOrganizeId(), request);
        if (publishTaskPendingVideoCount == 0) {
            return response;
        }

        List<PendingVideoDto> publishTaskPendingVideo = aiGenerateTaskManager.getPublishTaskPendingVideo(staff.getOrganizeId(), request);
        Map<Integer, List<Long>> taskDirIdGroup = publishTaskPendingVideo.stream().collect(
                Collectors.groupingBy(PendingVideoDto::getType, Collectors.mapping(PendingVideoDto::getId, Collectors.toList()))
        );

        List<Long> genTaskIds = taskDirIdGroup.getOrDefault(2, Lists.newArrayList());

        Map<Long, Integer> genTaskIdCount = douYinReleaseLogManager.getGenTaskIdCount(genTaskIds);

        //获取已选中的视频ids
        List<Long> taskSelectedGenTaskIds = publishVideoManager.getTaskSelectedGenTaskIds(request.getTaskId());

        publishTaskPendingVideo.forEach(pendingVideo -> {
            pendingVideo.setState(0);
            if (taskSelectedGenTaskIds.contains(pendingVideo.getId())) {
                pendingVideo.setState(2);
            }
            pendingVideo.setPublishedCount(genTaskIdCount.getOrDefault(pendingVideo.getId(), 0));
        });
        response.setList(publishTaskPendingVideo);
        response.setTotal(0);
        return response;
    }

    @Override
    public BasePaginationResponse<SelectedVideoDto> getSelectedVideo(JwtPayload jwtPayload, @Validated GetSelectedVideoListRequest request) {
        Staff staff = staffService.getStaffByUserIdNotExistThrow(jwtPayload);

        BasePaginationResponse<SelectedVideoDto> response = new BasePaginationResponse<>();
        response.setTotal(publishVideoManager.getSelectedVideoListCount(request.getTaskId(), request.getPublishState()));
        response.setList(Lists.newArrayList());
        if (response.getTotal() > 0) {
            response.setList(
                    publishVideoManager.getSelectedVideoList(
                            request.getTaskId(), request.getPublishState(), request.getPageNo(), request.getPageSize()
                    )
            );
        }

        return response;
    }

    /**
     * 删除已选的视频
     */
    @Override
    public boolean unSelectVideo(JwtPayload jwtPayload, UnSelectVideoRequest request) {
        Staff staff = staffService.getStaffByUserIdNotExistThrow(jwtPayload);

        List<PublishVideo> publishVideos = publishVideoManager.getByIds(request.getTaskId(), request.getVideoIds());
        if (publishVideos.size() != request.getVideoIds().size()) {
            String notExistVideoIds = publishVideos.stream().map(PublishVideo::getId).filter(id -> !request.getVideoIds().contains(id))
                    .map(Object::toString).collect(Collectors.joining(InngkeAppConst.COMMA_STR));
            throw new InngkeServiceException("视频「" + notExistVideoIds + "」不存在");
        }

        List<PublishVideo> stateErrorList = publishVideos.stream().filter(publishVideo -> publishVideo.getPublishState() != 0).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(stateErrorList)) {
            throw new InngkeServiceException("视频「" + stateErrorList.stream().map(PublishVideo::getId)
                    .map(Object::toString).collect(Collectors.joining(InngkeAppConst.COMMA_STR)) + "」状态异常");
        }

        return publishVideoManager.unSelectVideo(request.getTaskId(), request.getVideoIds());
    }

    @Override
    public boolean reSelectVideo(JwtPayload jwtPayload, ReSelectVideoRequest request) {
        Staff staff = staffService.getStaffByUserIdNotExistThrow(jwtPayload);

        Lock lock = lockService.getLock(RE_SELECT_VIDEO_LOCK_KEY + request.getTaskId(), 5);
        if (Objects.isNull(lock)) {
            throw new InngkeServiceException("请勿重复操作");
        }
        try {
            List<Long> selectedVideoIds = publishVideoManager.getListByPublishTaskIds(request.getTaskId(), PublishVideo.GEN_TASK_ID)
                    .stream().map(PublishVideo::getGenTaskId).collect(Collectors.toList());

            List<Long> deletedVideoIds = Lists.newArrayList(selectedVideoIds);
            deletedVideoIds.removeAll(request.getVideoIds());

            List<Long> addVideoIds = Lists.newArrayList(request.getVideoIds());
            addVideoIds.removeAll(selectedVideoIds);

            List<PublishVideo> videoList = addVideoIds.stream().map(videoId ->
                    PublishTaskConverter.toPublishTaskVideo(request.getTaskId(), videoId)
            ).collect(Collectors.toList());

            return publishVideoManager.saveSelectedVideo(videoList, deletedVideoIds);
        } finally {
            lock.unlock();
        }
    }

    @Override
    public PublishTaskDto getInfo(JwtPayload jwtPayload, Long taskId) {
        Staff staff = staffService.getStaffByUserIdNotExistThrow(jwtPayload);

        PublishTask publishTask = publishTaskManager.getById(taskId);
        if (Objects.isNull(publishTask)) {
            throw new InngkeServiceException("发布任务不存在");
        }
        Map<Long, PublishTaskDto> publishStatisticsData = getPublishStatisticsData(Lists.newArrayList(publishTask.getId()));

        List<PublishVideo> publishTaskVideoIds = publishVideoManager.getListByPublishTaskIds(taskId, PublishVideo.GEN_TASK_ID);

        PublishTaskDto publishTaskDto = PublishTaskConverter.toPublishTaskDto(publishTask, publishStatisticsData.get(taskId), publishTaskVideoIds.size());

        publishTaskDto.setSelectedVideoIds(publishTaskVideoIds);

        return publishTaskDto;
    }

    @Override
    public BasePaginationResponse<PublishTaskStatisticsDto> getStatistics(JwtPayload jwtPayload, GetPublishTaskStatisticsRequest request) {
        Staff staff = staffService.getStaffByUserIdNotExistThrow(jwtPayload);

        PublishTask publishTask = publishTaskManager.getById(request.getTaskId());
        if (Objects.isNull(publishTask)) {
            throw new InngkeServiceException("任务不存在");
        }

        BasePaginationResponse<PublishTaskStatisticsDto> response = new BasePaginationResponse<>();
        response.setList(
                aiGenerateTaskReleaseManager
                        .getPublishStatisticsList(request.getTaskId(), request.getPageNo(), request.getPageSize())
        );
        response.setTotal(
                aiGenerateTaskReleaseManager.count(Wrappers.<AiGenerateTaskRelease>query()
                        .eq(AiGenerateTaskRelease.PUBLISH_TASK_ID, request.getTaskId())
                )
        );

        return response;
    }

    @Override
    public IdPageDto<ReceivedPublishTaskVideoDto> getReceivedVideoList(JwtPayload jwtPayload, IdPageRequest request) {

        List<PublishVideo> publishVideoList = publishVideoManager.getReceivedVideoList(
                jwtPayload.getCid(),request.getLastId(),request.getPageSize()
        );

        Set<Long> genTaskIds = publishVideoList.stream().map(PublishVideo::getGenTaskId).collect(Collectors.toSet());
        AiGenerateHistoryRequest getVideoListRequest = new AiGenerateHistoryRequest();
        getVideoListRequest.setIds(genTaskIds);
        List<GetHistoryVideoListDto> videoList = Optional.ofNullable(
                crmAiGeneratorService.getHistoryVideoList(getVideoListRequest, false)
        ).map(BaseResponse::getData).map(IdPageDto::getList).orElse(Lists.newArrayList());

        Map<Long, GetHistoryVideoListDto> videoMap = videoList.stream().collect(Collectors.toMap(GetHistoryVideoListDto::getId, Function.identity()));

        IdPageDto<ReceivedPublishTaskVideoDto> response = new IdPageDto<>();

        response.setList(
                publishVideoList.stream().map(publishVideo -> {
                    ReceivedPublishTaskVideoDto receivedPublishTaskVideoDto = new ReceivedPublishTaskVideoDto();
                    if (!videoMap.containsKey(publishVideo.getGenTaskId())) {
                        return null;
                    }
                    BeanUtils.copyProperties(videoMap.get(publishVideo.getGenTaskId()), receivedPublishTaskVideoDto);
                    receivedPublishTaskVideoDto.setPublishVideoId(publishVideo.getId());
                    receivedPublishTaskVideoDto.setPublishTaskId(publishVideo.getPublishTaskId());
                    return receivedPublishTaskVideoDto;
                }).filter(Objects::nonNull).collect(Collectors.toList())
        );

        response.setTotal(publishVideoManager.getUserTaskCount(jwtPayload.getCid()));

        return response;
    }

    @Override
    public Boolean downloadVideo(JwtPayload jwtPayload, Long taskId) {
        // 1. 验证参数
        if (Objects.isNull(taskId)) {
            throw new InngkeServiceException("任务ID不能为空");
        }

        // 2. 获取用户已领取的视频
        PublishVideo publishVideo = publishVideoManager.userReceived(jwtPayload.getCid(), taskId);
        if (Objects.isNull(publishVideo)) {
            throw new InngkeServiceException("您还未领取该任务视频");
        }

        // 3. 检查视频状态，只有状态为1（待发布/已领取）的视频才能下载
        if (!publishVideo.getPublishState().equals(1)) {
            if (publishVideo.getPublishState().equals(0)) {
                throw new InngkeServiceException("视频未领取，无法下载");
            } else if (publishVideo.getPublishState().equals(2)) {
                throw new InngkeServiceException("视频已发布，无需重复下载");
            } else if (publishVideo.getPublishState().equals(10)) {
                throw new InngkeServiceException("视频已下载");
            } else {
                throw new InngkeServiceException("视频状态异常，无法下载");
            }
        }

        // 4. 更新视频状态为已下载(10)，并记录下载时间
        publishVideo.setPublishState(10);
        publishVideo.setDownloadTime(LocalDateTime.now());
        publishVideo.setUpdateTime(null); // 让MyBatis-Plus自动设置更新时间

        boolean updateResult = publishVideoManager.updateById(publishVideo);
        if (!updateResult) {
            throw new InngkeServiceException("更新视频下载状态失败");
        }

        logger.info("用户[{}]成功下载任务[{}]的视频[{}]", jwtPayload.getCid(), taskId, publishVideo.getId());
        return true;
    }

    private BasePaginationResponse<PublishTask> getBasePublishTask(Staff staff, GetPublishTaskListRequest request) {
        QueryWrapper<PublishTask> queryWrapper = Wrappers.<PublishTask>query()
                .orderByDesc(PublishTask.ID)
                .eq(PublishTask.ORGANIZE_ID, staff.getOrganizeId())
                .like(Objects.nonNull(request.getKeyword()), PublishTask.NAME, request.getKeyword());

        BasePaginationResponse<PublishTask> response = new BasePaginationResponse<>();
        response.setTotal(publishTaskManager.count(queryWrapper));
        response.setList(Lists.newArrayList());

        if (response.getTotal() == 0) {
            return response;
        }

        String last = "limit " + ((request.getPageNo() - 1) * request.getPageSize()) + ", " + request.getPageSize();

        response.setList(publishTaskManager.list(queryWrapper.last(last)));

        return response;
    }

    private Map<Long, PublishTaskDto> getPublishStatisticsData(List<Long> publishTaskIds) {
        return aiGenerateTaskReleaseManager.getPublishStatisticsDataMap(publishTaskIds);
    }


    private List<Long> checkPublishVideoIds(List<Long> videoIds) {
        if (CollectionUtils.isEmpty(videoIds)) {
            throw new InngkeServiceException("请选择任务视频");
        }
        return aiGenerateTaskManager.list(Wrappers.<AiGenerateTask>query()
                .in(AiGenerateTask.ID, videoIds)
                .eq(AiGenerateTask.STATUS, AiGenerateTaskStatusEnum.SUCCESS.getCode())
                .select(AiGenerateTask.ID)
        ).stream().map(AiGenerateTask::getId).collect(Collectors.toList());
    }

    private String genQrCode(Long publishTaskId) {
        GenMpQrRequest request = new GenMpQrRequest();
        request.setScene("publishTaskId=" + publishTaskId);
        request.setPage("subpackages/generator/video/detail");
        request.setEnv("trial");
        request.setHandleType(0);

        if (EnvEnum.PROD.equals(EnvUtils.getEnv())) {
            request.setEnv("release");
        }
        return Optional.ofNullable(commonAuthService.genQrCode(request)).map(BaseResponse::getData).orElse(null);
    }
}
