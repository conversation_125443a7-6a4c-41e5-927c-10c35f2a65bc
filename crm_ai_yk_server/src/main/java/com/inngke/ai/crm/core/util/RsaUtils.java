package com.inngke.ai.crm.core.util;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

/**
 * <AUTHOR>
 * @date 2021/7/5 16:15
 */
public class RsaUtils {
    private RsaUtils() {
    }

    /**
     * 加密算法
     */
    public static final String KEY_ALGORITHM = "RSA";
    /**
     * 验签算法
     */
    public static final String SIGNATURE_ALGORITHM = "SHA256withRSA";

    /**
     * 字符集
     */
    private static final Charset charset = StandardCharsets.UTF_8;


    /**
     * 根据类型获取公钥或者私钥
     *
     * @param key 密钥
     * @return 实体密钥
     */
    private static PrivateKey getPrivateKey(String key) throws NoSuchAlgorithmException, InvalidKeySpecException {
        byte[] bytes = Base64.getDecoder().decode(key);
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
        PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(bytes);
        return keyFactory.generatePrivate(pkcs8EncodedKeySpec);
    }

    /**
     * 私钥进行签名
     *
     * @param content    待签名内容
     * @param privateKey 私钥
     * @return 签名后字符串
     */
    public static String sign(String content, String privateKey) throws NoSuchAlgorithmException, InvalidKeySpecException, InvalidKeyException, SignatureException {
        PrivateKey key = getPrivateKey(privateKey);
        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
        signature.initSign(key);
        signature.update(content.getBytes(charset));
        return new String(Base64.getEncoder().encode(signature.sign()));
    }
}