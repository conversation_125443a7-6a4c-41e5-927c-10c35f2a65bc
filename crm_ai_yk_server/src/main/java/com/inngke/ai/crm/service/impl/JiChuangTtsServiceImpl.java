package com.inngke.ai.crm.service.impl;

import com.google.common.collect.Lists;
import com.inngke.ai.crm.db.crm.entity.TtsConfig;
import com.inngke.ai.crm.db.crm.manager.AppConfigManager;
import com.inngke.ai.crm.dto.enums.AppConfigCodeEnum;
import com.inngke.ai.crm.dto.jc.Cookies;
import com.inngke.ai.crm.dto.jc.PreviewTtsRequest;
import com.inngke.ai.crm.dto.jc.PreviewTtsResponse;
import com.inngke.ai.crm.dto.request.video.VideoTtsRequest;
import com.inngke.ai.crm.service.DownloadService;
import com.inngke.ai.crm.service.JiChuangTtsService;
import com.inngke.ai.crm.service.UploadService;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.TraceUtils;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.JsonService;
import com.inngke.common.service.SnowflakeIdService;
import com.inngke.common.utils.JsonUtil;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

@Service
public class JiChuangTtsServiceImpl implements JiChuangTtsService {
    private static final Logger logger = LoggerFactory.getLogger(JiChuangTtsServiceImpl.class);

    @Autowired
    private DownloadService downloadService;
    @Autowired
    private SnowflakeIdService snowflakeIdService;
    @Autowired
    private AppConfigManager appConfigManager;
    @Autowired
    private JsonService jsonService;
    @Autowired
    private UploadService uploadService;

    @Override
    public String createAudio(TtsConfig config, VideoTtsRequest request) {
        File tmpDir = new File("tmp");
        if (!tmpDir.exists()) {
            tmpDir.mkdirs();
        }
        File asideFile = new File(tmpDir, TraceUtils.getNewTraceId() + InngkeAppConst.MIDDLE_LINE_STR
                + Optional.ofNullable(config.getSpeedRatio()).orElse(0.0) + ".mp3");
        if (asideFile.exists()) {
            //已经存在
            return null;
        }

        String asideText = request.getText();
        if (StringUtils.isBlank(asideText)) {
            return asideText;
        }
        Map<String, String> configMap = appConfigManager.getValueByCodeList(Lists.newArrayList(
                AppConfigCodeEnum.JI_CHUANG_COOKIES.getCode(), AppConfigCodeEnum.JI_CHUANG_BP_ID.getCode())
        );


        List<Cookies> cookieList = JsonUtil.jsonToList(configMap.get(AppConfigCodeEnum.JI_CHUANG_COOKIES.getCode()), Cookies.class);

        String bpId = configMap.get(AppConfigCodeEnum.JI_CHUANG_BP_ID.getCode());

        int retryTime = 0;

        while (retryTime < 3) {
            try {
                PreviewTtsResponse previewTtsResponse = previewTts(config, asideText, cookieList, bpId);
                logger.info("生成语音返回:{}", jsonService.toJson(previewTtsResponse));
                if (Objects.isNull(previewTtsResponse)) {
                    throw new InngkeServiceException("语音生成失败:返回为null");
                }

                String ttsUrl = Optional.ofNullable(previewTtsResponse.getData()).map(PreviewTtsResponse.Data::getNormalizeTts)
                        .map(PreviewTtsResponse.NormalizeTts::getTtsUrl).orElse(null);

                if (StringUtils.isBlank(ttsUrl)) {
                    throw new InngkeServiceException("语音生成失败" + jsonService.toJson(previewTtsResponse));
                }
                long taskId = snowflakeIdService.getId();
                downloadService.addDownload(taskId, ttsUrl, asideFile.getAbsolutePath());

                downloadService.waitDownload(taskId);

                if (asideFile.exists()) {
                    String dataType = request.getDataType();
                    if (dataType == null) {
                        dataType = "base64";
                    }
                    if (dataType.equals("base64")) {
                        byte[] b = Files.readAllBytes(Paths.get(asideFile.getAbsolutePath()));
                        return Base64.getEncoder().encodeToString(b);
                    }
                    return uploadService.uploadFile(asideFile, "tts/" + asideFile.getName(), null);
                }

                retryTime++;
            } catch (Exception e) {
                retryTime++;
                logger.info("生成语音失败", e);
            } finally {
                asideFile.deleteOnExit();
            }
        }
        throw new InngkeServiceException("语音生成失败");
    }

    public PreviewTtsResponse previewTts(TtsConfig ttsConfig, String asideText, List<Cookies> cookies, String bpId) {

        PreviewTtsRequest request = buildRequest(ttsConfig, asideText);

        Headers headers = buildHeaders(cookies);

        // 转换为 JSON
        String jsonBody = jsonService.toJson(request);

        // OkHttp 请求部分
        OkHttpClient client = new OkHttpClient();
        RequestBody body = RequestBody.create(MediaType.parse("application/json"), jsonBody);

        Request httpRequest = new Request.Builder()
                .url("https://aic.oceanengine.com/api/smart_clip/preview_tts?companyId=" + bpId)
                .post(body).headers(headers).build();

        try {
            okhttp3.Response response = client.newCall(httpRequest).execute();
            if (!response.isSuccessful()) {
                throw new InngkeServiceException("即创HTTP 请求失败");
            }
            if (Objects.isNull(response.body())) {
                logger.info("即创HTTP 请求失败 body为空");
                throw new InngkeServiceException("即创HTTP 请求失败 body为空");
            }
            ResponseBody responseBody = response.body();
            return jsonService.toObject(responseBody.string(), PreviewTtsResponse.class);
        } catch (IOException e) {
            throw new InngkeServiceException(e);
        }
    }

    private Headers buildHeaders(List<Cookies> cookies) {
        StringBuilder cookieBuilder = new StringBuilder();
        for (Cookies cookie : cookies) {
            if (cookieBuilder.length() > 0) {
                cookieBuilder.append("; ");
            }
            cookieBuilder.append(cookie.getName())
                    .append("=")
                    .append(cookie.getValue());
        }

        return new Headers.Builder()
                .add("accept", "application/json, text/plain, */*")
                .add("accept-language", "zh-CN,zh;q=0.9")
                .add("cookie", cookieBuilder.toString()) // 建议从配置文件读取
                .add("origin", "https://aic.oceanengine.com")
                .add("referer", "https://aic.oceanengine.com/tools/smart_clip/mixed/common?bpId=1802983699261475")
                .add("sec-ch-ua", "\"Chromium\";v=\"130\", \"Google Chrome\";v=\"130\", \"Not?A_Brand\";v=\"99\"")
                .add("sec-ch-ua-mobile", "?0")
                .add("sec-ch-ua-platform", "\"macOS\"")
                .add("sec-fetch-dest", "empty")
                .add("sec-fetch-mode", "cors")
                .add("sec-fetch-site", "same-origin")
                .add("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36")
                .add("Content-Type", "application/json")
                .build();
    }

    private PreviewTtsRequest buildRequest(TtsConfig ttsConfig, String asideText) {
        // 构建请求对象
        PreviewTtsRequest request = new PreviewTtsRequest();
        request.setScript(asideText);

        PreviewTtsRequest.Option option = new PreviewTtsRequest.Option();
        option.setVoiceId(ttsConfig.getVoiceType());
        option.setWithWordPreview(true);
        option.setWithNormalizeTts(true);
        option.setWithSplitTts(false);

        // 设置 AudioOption
        PreviewTtsRequest.AudioOption audioOption = new PreviewTtsRequest.AudioOption();
        audioOption.setBgmVolume(0.8);
        audioOption.setVoiceVolume(Optional.ofNullable(ttsConfig.getVolumeRatio()).orElse(1.0));
        audioOption.setVoiceSpeed(Optional.ofNullable(ttsConfig.getSpeedRatio()).orElse(1.0));
        option.setAudioOption(audioOption);

        // 设置 WordArtOption
        PreviewTtsRequest.WordArtOption wordArtOption = new PreviewTtsRequest.WordArtOption();
        PreviewTtsRequest.RenderOption renderOption = new PreviewTtsRequest.RenderOption();
        renderOption.setTargetHeight(1280);
        renderOption.setTargetWidth(720);
        wordArtOption.setRenderOption(renderOption);

        // 设置 Style
        PreviewTtsRequest.Style style = new PreviewTtsRequest.Style();
        style.setFontUri("icc/smartClip/fonts/SourceHanSansCN-Regular.ttf");
        style.setName("SourceHanSansCN-Regular");
        style.setCname("思源黑体");
        style.setWordArtId("219555850242");
        style.setWordArtElementId("219555850242");
        style.setFontElementId("18124800514");
        style.setFontSize(43);
        style.setAlignType(1);
        style.setTypesetting(0);
        style.setFormat(1);

        PreviewTtsRequest.Position pos = new PreviewTtsRequest.Position();
        pos.setPosY(853);
        style.setPos(pos);

        wordArtOption.setStyle(style);
        option.setWordArtOption(wordArtOption);

        request.setOption(option);

        return request;
    }
}

