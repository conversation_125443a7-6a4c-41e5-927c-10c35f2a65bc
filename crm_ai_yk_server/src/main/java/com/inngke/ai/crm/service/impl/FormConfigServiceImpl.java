package com.inngke.ai.crm.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.inngke.ai.crm.converter.CategoryConverter;
import com.inngke.ai.crm.converter.TtsConfigConverter;
import com.inngke.ai.crm.db.crm.entity.*;
import com.inngke.ai.crm.db.crm.manager.*;
import com.inngke.ai.crm.dto.SimpleFormDto;
import com.inngke.ai.crm.dto.enums.AppConfigCodeEnum;
import com.inngke.ai.crm.dto.form.SelectOption;
import com.inngke.ai.crm.dto.response.common.PcFormConfigDto;
import com.inngke.ai.crm.dto.response.common.SubtitlesStyleDto;
import com.inngke.ai.crm.dto.response.common.TextFontDto;
import com.inngke.ai.crm.dto.response.digital.person.DigitalPersonTagDto;
import com.inngke.ai.crm.dto.response.material.MaterialCategorySelectOption;
import com.inngke.ai.crm.service.DigitalPersonService;
import com.inngke.ai.crm.service.FormConfigService;
import com.inngke.ai.crm.service.MaterialCategoryService;
import com.inngke.ai.crm.service.StaffService;
import com.inngke.ai.crm.service.cache.BgmTypeCache;
import com.inngke.ai.crm.service.material.MaterialCountService;
import com.inngke.ai.crm.service.material.impl.MaterialServiceAbs;
import com.inngke.ai.dto.utils.FormDataUtils;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.utils.JsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class FormConfigServiceImpl implements FormConfigService {

    public static final String TEXT_MATERIAL_TYPE = "text";
    public static final Integer VIDEO_PRODUCT_ID = 10;
    public static final String CATEGORY_TYPE_SUBTITLE_STYLE = "subtitle_style";
    public static final Logger logger = LoggerFactory.getLogger(FormConfigServiceImpl.class);

    @Autowired
    private MaterialCategoryService materialCategoryService;
    @Autowired
    private JianyingResourceManager jianyingResourceManager;
    @Autowired
    private DigitalPersonService digitalPersonService;
    @Autowired
    private TextFontManager textFontManager;
    @Autowired
    private TtsConfigManager ttsConfigManager;
    @Autowired
    private StaffService staffService;
    @Autowired
    private BgmTypeCache bgmTypeCache;
    @Autowired
    private AiProductManager aiProductManager;
    @Autowired
    private VideoLutManager videoLutManager;
    @Autowired
    private AppConfigManager appConfigManager;
    @Autowired
    private UserManager userManager;
    @Autowired
    private CategoryManager categoryManager;
    @Autowired
    private MaterialCountService materialCountService;

    @Override
    public PcFormConfigDto getPcFormConfig(JwtPayload jwtPayload) {
        User user = userManager.getById(jwtPayload.getCid());

        Staff staff = staffService.getStaffByUserIdNotExistThrow(jwtPayload);

        PcFormConfigDto pcFormConfigDto = new PcFormConfigDto();
//        TimeInterval timer = DateUtil.timer();

        List<MaterialCategory> materialCategory = Optional.ofNullable(materialCategoryService.getCategoryList(jwtPayload, MaterialServiceAbs.VIDEO_TYPE))
                .orElse(Lists.newArrayList());
//        logger.info("获取分类耗时:{}", timer.intervalRestart());
        // 素材分类
        pcFormConfigDto.setMaterialCategoryTree(toMaterialCategorySelectOption(materialCategory));
//        logger.info("构建分类树耗时:{}", timer.intervalRestart());

        // 字幕样式
        pcFormConfigDto.setSubtitlesStyleList(jianyingResourceManager.list(
                Wrappers.<JianyingResource>query()
                        .eq(JianyingResource.STATUS, 1)
                        .eq(JianyingResource.MATERIAL_TYPE, TEXT_MATERIAL_TYPE)
        ).stream().map(jianyingResource -> {
            SubtitlesStyleDto subtitlesStyleDto = new SubtitlesStyleDto();
            subtitlesStyleDto.setId(jianyingResource.getId());
            subtitlesStyleDto.setIcon(jianyingResource.getDemoUrl());
            subtitlesStyleDto.setName(jianyingResource.getName());
            return subtitlesStyleDto;
        }).collect(Collectors.toList()));
//        logger.info("构建字幕样式耗时:{}", timer.intervalRestart());

        List<String> appConfigKeys = Lists.newArrayList(
                AppConfigCodeEnum.DIGITAL_PERSON_CLOSE.getOrganizeCode(staff.getOrganizeId()),
                AppConfigCodeEnum.AI_VIDEO_LUT_CONF.getCode(),
                AppConfigCodeEnum.SUBTITLE_REPAIR_CONFIG_KEY.getOrganizeCode(staff.getOrganizeId()),
                AppConfigCodeEnum.SUBTITLE_REPAIR_CONFIG_KEY.getCode(),
                AppConfigCodeEnum.VIDEO_MASHUP_IDEAS_KEY.getOrganizeCode(staff.getOrganizeId()),
                AppConfigCodeEnum.VIDEO_MASHUP_IDEAS_KEY.getCode(),
                AppConfigCodeEnum.VIDEO_MUSIC_BEAT_THEME.getOrganizeCode(staff.getOrganizeId()),
                AppConfigCodeEnum.VIDEO_MUSIC_BEAT_THEME.getCode(),
                AppConfigCodeEnum.VIDEO_MUSIC_BEAT.getOrganizeCode(staff.getOrganizeId()),
                AppConfigCodeEnum.VIDEO_MUSIC_BEAT.getCode()
        );
        Map<String, String> configMap = appConfigManager.getValueByCodeList(appConfigKeys);
//        logger.info("获取配置耗时:{}", timer.intervalRestart());

        // 数字人是否启用
        String dpClose = configMap.get(AppConfigCodeEnum.DIGITAL_PERSON_CLOSE.getCode() + InngkeAppConst.DOT_STR + staff.getOrganizeId());
        if (StringUtils.isNotBlank(dpClose)) {
            pcFormConfigDto.setDpClose(Integer.parseInt(dpClose) == 1);
        }

        // 字幕修复配置
        pcFormConfigDto.setSubtitleRepairTags(getConfigSelectOption(AppConfigCodeEnum.SUBTITLE_REPAIR_CONFIG_KEY, staff.getOrganizeId(), configMap));

        // 成片混剪 混编思路
        pcFormConfigDto.setVideoIdeasTags(getConfigSelectOption(AppConfigCodeEnum.VIDEO_MASHUP_IDEAS_KEY, staff.getOrganizeId(), configMap));

        // 音乐踩点 视频主题
        pcFormConfigDto.setVideoThemeTags(getConfigSelectOption(AppConfigCodeEnum.VIDEO_MUSIC_BEAT_THEME, staff.getOrganizeId(), configMap));

        // 音乐踩点 踩点节奏
        pcFormConfigDto.setBeatTags(getConfigSelectOption(AppConfigCodeEnum.VIDEO_MUSIC_BEAT, staff.getOrganizeId(), configMap));

        // 数字人标签
        pcFormConfigDto.setDigitalPersonTagList(
                Optional.ofNullable(digitalPersonService.getDigitalPersonTagList()).map(BaseResponse::getData).orElse(Lists.newArrayList())
        );
        pcFormConfigDto.getDigitalPersonTagList().add(0, new DigitalPersonTagDto(null, "全部"));
//        logger.info("获取数字人标签耗时:{}", timer.intervalRestart());

        // 字幕字体
        pcFormConfigDto.setTextFontList(
                textFontManager.getByOrganizeId(staff.getOrganizeId(), Lists.newArrayList(0)).stream().map(textFont -> {
                    TextFontDto textFontDto = new TextFontDto();
                    textFontDto.setId(textFont.getId());
                    textFontDto.setName(textFont.getName());
                    return textFontDto;
                }).collect(Collectors.toList())
        );
//        logger.info("获取字幕字体耗时:{}", timer.intervalRestart());

        // 音色
        pcFormConfigDto.setVideoAudioConfigList(
                ttsConfigManager.getAll(staff.getOrganizeId()).stream().map(TtsConfigConverter::toTtsConfigDto).collect(Collectors.toList())
        );
//        logger.info("获取音色耗时:{}", timer.intervalRestart());

        // bgm分类
        List<SimpleFormDto> items = new ArrayList<>();
        BgmTypeCache.BgmTypeCacheData data = bgmTypeCache.get();
        data.foreach(0, (typeId, typeName) -> items.add(new SimpleFormDto(typeId.toString(), typeName)));
        pcFormConfigDto.setBgmTypeList(items);
//        logger.info("获取bgm分类耗时:{}", timer.intervalRestart());

        // 积分消耗
        pcFormConfigDto.setCoin(aiProductManager.getById(VIDEO_PRODUCT_ID).getCoin());
//        logger.info("获取积分消耗耗时:{}", timer.intervalRestart());

        //lut配置
        pcFormConfigDto.setLutList(getLutOptions(staff));
//        logger.info("获取LUT配置耗时:{}", timer.intervalRestart());

        // 转场特效
        pcFormConfigDto.setTransitionList(getJianyingResourceOptions(staff, "transition"));
//        logger.info("获取转场特效配置耗时:{}", timer.intervalRestart());

        // BGM结尾音效
        pcFormConfigDto.setBgmEndEffects(getJianyingResourceOptions(staff, "audio_effect_1"));

        pcFormConfigDto.setSubtitleTagStyleList(
                categoryManager.getByType(CATEGORY_TYPE_SUBTITLE_STYLE).stream()
                        .map(CategoryConverter::toCategorySelectOption).filter(Objects::nonNull).collect(Collectors.toList())
        );
//        logger.info("获取字幕标签耗时:{}", timer.intervalRestart());

        return pcFormConfigDto;
    }

    private List<SelectOption> getJianyingResourceOptions(Staff staff, String type) {
        return jianyingResourceManager.list(
                        Wrappers.<JianyingResource>query()
                                .in(JianyingResource.MATERIAL_TYPE, type)
                                .eq(JianyingResource.STATUS, 1)
                                .orderByDesc(JianyingResource.ORDER_SCORE)
                                .select(JianyingResource.ID, JianyingResource.DEMO_URL, JianyingResource.NAME)
                )
                .stream()
                .map(r -> new SelectOption()
                        .setValue(r.getId())
                        .setTitle(r.getName())
                        .setIcon(r.getDemoUrl())
                )
                .collect(Collectors.toList());
    }

    private List<SelectOption> getLutOptions(Staff staff) {
        Map<Long, List<VideoLut>> lutGroup = videoLutManager.getAll(staff.getOrganizeId()).stream().collect(Collectors.groupingBy(VideoLut::getOrganizeId));
        List<VideoLut> allLut = Optional.ofNullable(lutGroup.get(staff.getOrganizeId())).orElse(lutGroup.get(0L));
        allLut = Optional.ofNullable(allLut).orElse(Lists.newArrayList());

        List<SelectOption> videoLutList = allLut.stream().map(videoLut -> {
            SelectOption selectOption = new SelectOption();
            selectOption.setTitle(videoLut.getName());
            selectOption.setValue(videoLut.getUrl());
            return selectOption;
        }).collect(Collectors.toList());

        SelectOption noneLut = new SelectOption();
        noneLut.setTitle("无LUT");
        noneLut.setValue(InngkeAppConst.EMPTY_STR);
        videoLutList.add(noneLut);

        SelectOption smartLut = new SelectOption();
        smartLut.setTitle("智能调色");
        smartLut.setValue(FormDataUtils.LUT_SMART);
        videoLutList.add(videoLutList.size() - 1, smartLut);

        return videoLutList;
    }

    private List<MaterialCategorySelectOption> toMaterialCategorySelectOption(List<MaterialCategory> materialCategory) {
        List<Long> cateIds = materialCategory.stream().map(MaterialCategory::getId).collect(Collectors.toList());
        Map<Long, Integer> cateNormalMaterialCount = materialCountService.getCateMaterialCount(cateIds, 1);
        Map<Long, Integer> cateSceneMaterialCount = materialCountService.getCateMaterialCount(cateIds, 2);

        Map<Long, MaterialCategorySelectOption> cateOptionMap = materialCategory.stream().collect(Collectors.toMap(MaterialCategory::getId,
                cate -> toMaterialCategorySelectOption(cate, cateNormalMaterialCount, cateSceneMaterialCount))
        );

        List<MaterialCategorySelectOption> result = new ArrayList<>();
        for (MaterialCategorySelectOption cate : cateOptionMap.values()) {
            if (Objects.isNull(cate.getPid()) || cate.getPid() == 0) {
                result.add(cate);
            } else {
                MaterialCategorySelectOption parent = cateOptionMap.get(cate.getPid());
                if (Objects.isNull(parent.getChildren())) {
                    parent.setChildren(Lists.newArrayList());
                }
                parent.getChildren().add(cate);
                parent.setNormalTotal(parent.getNormalTotal() + cate.getNormalTotal());
                parent.setSceneTotal(parent.getSceneTotal() + cate.getSceneTotal());
            }
        }
        return result;
    }

    private MaterialCategorySelectOption toMaterialCategorySelectOption(MaterialCategory category, Map<Long, Integer> cateNormalMaterialCount, Map<Long, Integer> cateSceneMaterialCount) {
        MaterialCategorySelectOption option = new MaterialCategorySelectOption();
        option.setPid(category.getParentId());
        option.setValue(category.getId());
        option.setTitle(category.getName());
        option.setSceneTotal(Optional.ofNullable(cateSceneMaterialCount.get(category.getId())).orElse(0));
        option.setNormalTotal(Optional.ofNullable(cateNormalMaterialCount.get(category.getId())).orElse(0));

        return option;
    }

    private List<SelectOption> getConfigSelectOption(AppConfigCodeEnum configEnum, Long organizeId, Map<String, String> configMap) {
        String configStr = Optional.ofNullable(configMap.get(configEnum.getOrganizeCode(organizeId)))
                .orElse(configMap.get(configEnum.getCode()));
        if (StringUtils.isNotBlank(configStr)) {
            try {
                return JsonUtil.jsonToList(configStr, SelectOption.class);
            } catch (Exception e) {
                logger.info("「{}」配置解析失败:{}", configEnum.getMsg(), configStr);
            }
        }
        return Lists.newArrayList();
    }
}
