package com.inngke.ai.crm.converter;

import com.inngke.ai.crm.dto.response.AiGenerateTaskStatisticResponse;
import com.inngke.ai.crm.dto.response.DouYinListDto;

/**
 * <AUTHOR>
 * @Date 2024/3/22 15:24
 */
public class DouYinDataConverter {

    public static DouYinListDto toDouYinDetailDto(AiGenerateTaskStatisticResponse aiGenerateTaskStatisticResponse) {
        DouYinListDto douYinListDto = new DouYinListDto();
        douYinListDto.setReleaseTime(aiGenerateTaskStatisticResponse.getReleaseTime());
        douYinListDto.setTitle(aiGenerateTaskStatisticResponse.getTitle());
        douYinListDto.setStaffId(aiGenerateTaskStatisticResponse.getUserId());
        douYinListDto.setStaffName(aiGenerateTaskStatisticResponse.getUserName());
        douYinListDto.setDepartmentId(aiGenerateTaskStatisticResponse.getDepartmentId());
        douYinListDto.setDepartmentName(aiGenerateTaskStatisticResponse.getDepartmentName());
        douYinListDto.setViewCount(aiGenerateTaskStatisticResponse.getViewCount());
        douYinListDto.setLikeCount(aiGenerateTaskStatisticResponse.getLikeCount());
        douYinListDto.setCollectionCount(aiGenerateTaskStatisticResponse.getCollectionCount());
        douYinListDto.setCommentCount(aiGenerateTaskStatisticResponse.getCommentCount());
        douYinListDto.setForwardCount(aiGenerateTaskStatisticResponse.getForwardCount());
        douYinListDto.setReleaseExternalId(aiGenerateTaskStatisticResponse.getReleaseExternalId());
        douYinListDto.setTaskCreateTime(aiGenerateTaskStatisticResponse.getTaskCreateTime());
        douYinListDto.setReleaseStatus(aiGenerateTaskStatisticResponse.getReleaseStatus());
        douYinListDto.setAppId(aiGenerateTaskStatisticResponse.getAppId());
        douYinListDto.setAppName(aiGenerateTaskStatisticResponse.getAppName());
        douYinListDto.setStaffMobile(aiGenerateTaskStatisticResponse.getStaffMobile());

        return douYinListDto;
    }
}
