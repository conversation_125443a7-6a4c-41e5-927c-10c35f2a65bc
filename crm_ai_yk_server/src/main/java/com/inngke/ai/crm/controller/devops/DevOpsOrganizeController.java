package com.inngke.ai.crm.controller.devops;

import com.inngke.ai.crm.dto.request.devops.CreateOrganizeRequest;
import com.inngke.ai.crm.dto.response.devops.OrganizeDto;
import com.inngke.ai.crm.service.devops.OrganizeService;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * @chapter DevOps
 * @section 企业模块
 */
@RestController
@RequestMapping("/api/ai/devops/organize")
public class DevOpsOrganizeController {

    @Autowired
    private OrganizeService organizeService;

    /**
     * 获取企业列表
     */
    @GetMapping("/list")
    public BaseResponse<BasePaginationResponse<OrganizeDto>> getOrganizeList() {
        return BaseResponse.success(organizeService.getOrganizeList());
    }

    /**
     * 获取企业配置
     */
    @GetMapping("/{organizeId:\\d+}/config")
    public BaseResponse<Map<String, String>> getOrganizeConfigMap(@PathVariable Long organizeId) {
        return BaseResponse.success(organizeService.getOrganizeConfig(organizeId));
    }

    /**
     * 获取企业配置-指定code
     */
    @GetMapping("/{organizeId:\\d+}/config/{code}")
    public BaseResponse<String> getOrganizeConfigMap(@PathVariable Long organizeId,@PathVariable String code) {
        return BaseResponse.success(organizeService.getOrganizeConfig(organizeId,code));
    }

    /**
     * 保存企业配置信息
     */
    @PutMapping("/{organizeId:\\d+}/config")
    public BaseResponse<Map<String, String>> saveOrganizeConfig(
            @PathVariable Long organizeId, @RequestBody Map<String, String> config) {
        return BaseResponse.success(organizeService.saveOrganizeConfig(organizeId, config));
    }

    /**
     * 创建企业
     */
    @PostMapping
    public BaseResponse<OrganizeDto> saveOrganize(@RequestBody CreateOrganizeRequest request){
        return BaseResponse.success(organizeService.createOrganize(request));
    }

    /**
     * 删除企业
     */
    @DeleteMapping("/id/{id:\\d+}")
    public BaseResponse<Boolean> delOrganize(@PathVariable Long id){
        return BaseResponse.success(organizeService.delOrganize(id));
    }
}
