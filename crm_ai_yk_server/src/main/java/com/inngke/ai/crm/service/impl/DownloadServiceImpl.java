package com.inngke.ai.crm.service.impl;

import com.inngke.ai.crm.service.DownloadService;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.exception.InngkeServiceException;
import org.apache.commons.compress.utils.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;

@Service
public class DownloadServiceImpl implements DownloadService {
    private static final Logger logger = LoggerFactory.getLogger(DownloadServiceImpl.class);
    private static final Map<Long, BatchDownloadTask> TASK_MAP = new ConcurrentHashMap<>();

    private static final Executor EXECUTOR = Executors.newFixedThreadPool(20, new ThreadFactory() {
        private final AtomicInteger seq = new AtomicInteger(0);

        @Override
        public Thread newThread(@NotNull Runnable r) {
            Thread t = new Thread(r);
            t.setName("download#" + seq.getAndAdd(1));
            return t;
        }
    });

    /**
     * 下载文件
     *
     * @param taskId   任务ID
     * @param url      文件URL
     * @param filePath 文件路径
     */
    @Override
    public void addDownload(long taskId, String url, String filePath) {
        BatchDownloadTask batchDownloadTask = TASK_MAP.computeIfAbsent(taskId, k -> new BatchDownloadTask());
        DownloadTask task = batchDownloadTask.addTask(url, filePath);

        //发起任务
        EXECUTOR.execute(() -> startDownload(batchDownloadTask, task));
    }

    /**
     * 等待下载完成
     *
     * @param taskId 任务ID
     */
    @Override
    public boolean waitDownload(long taskId) {
        BatchDownloadTask batchDownloadTask = TASK_MAP.get(taskId);
        if (batchDownloadTask == null) {
            throw new InngkeServiceException("任务不存在：" + taskId);
        }

        while (true) {
            int status = batchDownloadTask.getDownloadStatus();
            if (status == 0) {
                //不没有下载完成，等待100ms
                AsyncUtils.sleep(100);
            } else {
                return status == 1;
            }
        }
    }

    /**
     * 直接下载文件
     *
     * @param url      文件URL
     * @param distFile 文件路径
     */
    @Override
    public void directDownload(String url, File distFile) {
        directDownload(url, distFile, 0);
    }

    private void directDownload(String url, File distFile, int retryCount) {
        if (distFile.exists()) {
            distFile.delete();
        }

        try {
            URLConnection connection = new URL(url).openConnection();
            try (InputStream in = connection.getInputStream();
                 FileOutputStream out = new FileOutputStream(distFile)) {

                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = in.read(buffer)) != -1) {
                    out.write(buffer, 0, bytesRead);
                }
            }
            logger.info("下载成功：{}", url);
        } catch (IOException e) {
            logger.error("下载失败：{}", url, e);
            if (distFile.exists()) {
                distFile.delete();
            }

            //重试
            if (retryCount < 3) {
                directDownload(url, distFile, retryCount + 1);
            } else {
                throw new InngkeServiceException("下载失败：" + url, e);
            }
        }
    }

    /**
     * 清理下载文件
     *
     * @param taskId 任务ID
     */
    @Override
    public void clear(long taskId) {
        TASK_MAP.remove(taskId);
    }

    private void startDownload(BatchDownloadTask batchTask, DownloadTask task) {
        File file = new File(task.filePath);
        if (file.exists()) {
            task.downloadStatus = 1;
            return;
        }

        try {
            URLConnection connection = new URL(task.url).openConnection();
            try (InputStream in = connection.getInputStream();
                 FileOutputStream out = new FileOutputStream(file)) {

                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = in.read(buffer)) != -1) {
                    out.write(buffer, 0, bytesRead);
                }
            }
            logger.info("下载成功：{}", task.url);
            task.downloadStatus = 1;
        } catch (IOException e) {
            logger.error("下载失败：{}", task.url, e);
            if (file.exists()) {
                file.delete();
            }

            //重试
            if (task.retryCount < 3) {
                task.retryCount++;
                startDownload(batchTask, task);
            } else {
                task.downloadStatus = -1;
            }
        }
    }

    private static class BatchDownloadTask {
        private List<DownloadTask> tasks;

        public BatchDownloadTask() {
            this.tasks = Lists.newArrayList();
        }

        public DownloadTask addTask(String url, String filePath) {
            DownloadTask task = new DownloadTask(url, filePath);
            tasks.add(task);
            return task;
        }

        public int getDownloadStatus() {
            for (DownloadTask task : tasks) {
                if (task.downloadStatus == -1) {
                    return -1;
                } else if (task.downloadStatus == 0) {
                    return 0;
                }
            }
            return 1;
        }
    }

    private static class DownloadTask {
        private final String url;

        private final String filePath;

        private int retryCount = 0;

        /**
         * 下载状态：-1=下载失败 0=初始化 1=下载成功
         */
        private Integer downloadStatus;

        public DownloadTask(String url, String filePath) {
            this.url = url;
            this.filePath = filePath;
            downloadStatus = 0;
        }
    }
}
