package com.inngke.ai.crm.service.impl;

import com.google.common.collect.Lists;
import com.inngke.ai.crm.db.crm.entity.Staff;
import com.inngke.ai.crm.dto.request.bi.BiUrlRequest;
import com.inngke.ai.crm.service.QuickBiService;
import com.inngke.ai.crm.service.StaffService;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.quickbi.api.BiResourceManagerApi;
import com.inngke.quickbi.dto.request.QuickBiGlobalParam;
import com.inngke.quickbi.dto.request.QuickBiVisitTicketRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class QuickBiServiceImpl implements QuickBiService {
    @Autowired
    private BiResourceManagerApi biResourceManagerApi;

    @Autowired
    private StaffService staffService;

    /**
     * 获取报表链接
     * 获取到的链接只可打开2次，10分钟内有效
     */
    @Override
    public String createBiUrl(JwtPayload jwtPayload, BiUrlRequest request) {
        Long userId = jwtPayload.getCid();

        Staff staff = staffService.getStaffByUserId(userId);
        if (staff == null) {
            throw new InngkeServiceException("用户不存在");
        }
        if (staff.getOrganizeId() == null || staff.getOrganizeId() == 0) {
            throw new InngkeServiceException("用户不属于任何企业");
        }

        QuickBiVisitTicketRequest quickBiVisitTicketRequest = new QuickBiVisitTicketRequest();
        String worksId = request.getPageId();
        quickBiVisitTicketRequest.setWorksId(worksId);
        quickBiVisitTicketRequest.setTicketNum(2);
        quickBiVisitTicketRequest.setExpireMinute(10);
        quickBiVisitTicketRequest.setWatermarkText("云店AI");
        List<QuickBiGlobalParam> params = Lists.newArrayList(
                QuickBiGlobalParam.and("organize_id", staff.getOrganizeId().toString())
        );
        if (request.getDepartmentId() != null) {
            params.add(QuickBiGlobalParam.and("department_id", request.getDepartmentId().toString()));
        }
        quickBiVisitTicketRequest.setGlobalParamList(params);

        String ticket = biResourceManagerApi.createTicketWithOptions(quickBiVisitTicketRequest);
        return "https://bi.aliyuncs.com/token3rd/dashboard/view/pc.htm?pageId=" + worksId + "&accessTicket=" + ticket;
    }
}
