package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.dto.request.point.PointExchangeRequest;
import com.inngke.ai.crm.dto.request.point.PointGoodsExchangeQuery;
import com.inngke.ai.crm.dto.request.point.PointGoodsQuery;
import com.inngke.ai.crm.dto.request.point.PointGoodsSaveRequest;
import com.inngke.ai.crm.dto.response.point.PointGoodsDto;
import com.inngke.ai.crm.dto.response.point.PointGoodsExchangeDto;
import com.inngke.ai.crm.service.PointService;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @chapter 积分模块
 * @section 积分管理
 */
@RestController
@RequestMapping("/api/ai/point")
public class PointApiController {
    @Autowired
    private PointService pointService;

    /**
     * 积分商品列表
     */
    @GetMapping("/goods-list")
    public BaseResponse<BasePaginationResponse<PointGoodsDto>> goodsList(
            @RequestAttribute("jwtPayload") JwtPayload user,
            PointGoodsQuery query
    ) {

        return BaseResponse.success(pointService.goodsList(user, query));
    }

    /**
     * 保存积分商品
     */
    @PostMapping("/goods")
    public BaseResponse<Boolean> savePointGoods(
            @RequestAttribute("jwtPayload") JwtPayload user,
            @RequestBody PointGoodsSaveRequest pointGoodsDto
    ) {
        return BaseResponse.success(pointService.savePointGoods(user, pointGoodsDto));
    }

    /**
     * 删除积分商品
     */
    @DeleteMapping("/goods/{goodsId}")
    public BaseResponse<Boolean> deletePointGoods(
            @RequestAttribute("jwtPayload") JwtPayload user,
            @PathVariable Long goodsId
    ) {
        return BaseResponse.success(pointService.deletePointGoods(user, goodsId));
    }

    /**
     * 兑换积分商品
     */
    @PostMapping("/exchange")
    public BaseResponse<Boolean> exchange(
            @RequestAttribute("jwtPayload") JwtPayload user,
            @RequestBody PointExchangeRequest request
    ) {
        return BaseResponse.success(pointService.exchange(user, request));
    }

    /**
     * 积分商品兑换列表
     */
    @GetMapping("/exchange-list")
    public BaseResponse<BasePaginationResponse<PointGoodsExchangeDto>> exchangeList(
            @RequestAttribute("jwtPayload") JwtPayload user,
            PointGoodsExchangeQuery query
    ) {
        return BaseResponse.success(pointService.exchangeList(user, query));
    }
}
