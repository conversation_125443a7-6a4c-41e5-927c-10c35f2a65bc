package com.inngke.ai.crm.service.impl;

import com.inngke.ai.crm.client.RbacUserServiceForAi;
import com.inngke.ai.crm.client.WxMpQrServiceForAi;
import com.inngke.ai.crm.client.common.WxMpQrServiceForClient;
import com.inngke.ai.crm.core.config.AiGcConfig;
import com.inngke.ai.crm.db.crm.entity.Staff;
import com.inngke.ai.crm.dto.UserPermissionDto;
import com.inngke.ai.crm.dto.request.CrmMiniAppLoginResponse;
import com.inngke.ai.crm.dto.request.CrmMpMiniAppLoginRequest;
import com.inngke.ai.crm.dto.request.CrmWebMiniAppAuthRequest;
import com.inngke.ai.crm.dto.request.CrmWebScanQrcodeRequest;
import com.inngke.ai.crm.dto.response.CrmScanQrCodeResponse;
import com.inngke.ai.crm.dto.response.CrmWebMiniAppAuthDto;
import com.inngke.ai.crm.service.CrmLoginService;
import com.inngke.ai.crm.service.StaffService;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.TraceUtils;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.service.JsonService;
import com.inngke.common.service.JwtService;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.ip.auth.rbac.dto.request.UserRolesRequest;
import com.inngke.ip.common.dto.request.GenMpQrRequest;
import com.inngke.ip.common.dto.request.GetMpQrSceneRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static com.inngke.ip.common.consts.GenQrCodeHandleTypeConsts.BASE64_HANDLE_TYPE;

/**
 * <AUTHOR>
 * @since 2023-10-17 10:41
 **/
@Service
public class CrmLoginServiceImpl implements CrmLoginService {

    public static final Integer SOURCE_PC_CLIENT = 2;

    @Autowired
    private WxMpQrServiceForAi wxMpQrServiceForAi;

    @Autowired
    private AiGcConfig aiGcConfig;


    @Autowired
    private JsonService jsonService;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private JwtService jwtService;

    @Autowired
    private RbacUserServiceForAi rbacUserServiceForAi;

    @Autowired
    private StaffService staffService;

    @Autowired
    private WxMpQrServiceForClient wxMpQrServiceForClient;

    private static String LOGIN_KEY = "crm_ai_yk:login:";

    @Override
    public BaseResponse<CrmWebMiniAppAuthDto> miniAppAuth(CrmWebMiniAppAuthRequest request) {
        Integer bid = aiGcConfig.getBid();
        String requestId = TraceUtils.getNewTraceId();

        String qrCode = getQrCode(bid, request.getEnv(), requestId);

        CrmWebMiniAppAuthDto result = new CrmWebMiniAppAuthDto();
        result.setRequestId(requestId);
        result.setQrUrl(qrCode);

        CrmScanQrCodeResponse login = new CrmScanQrCodeResponse();
        login.setRequestId(requestId);
        login.setStatus(0);
        login.setSource(request.getSource());
        redisTemplate.opsForValue().set(getLoginKey(requestId), jsonService.toJson(login), 10, TimeUnit.MINUTES);

        return BaseResponse.success(result);
    }

    private String getLoginKey(String requestId) {
        return LOGIN_KEY + aiGcConfig.getBid() + ":" + requestId;
    }

    private String getQrCode(int bid, String env, String requestId) {
        GenMpQrRequest genMpQrRequest = new GenMpQrRequest();
        genMpQrRequest.setBid(bid);
        genMpQrRequest.setEnv(env);
        genMpQrRequest.setPage("subpackages/login/qrCodeLogin");

        HashMap<String, String> para = new HashMap<>();
        para.put("requestId", requestId);
        genMpQrRequest.setScene(genMpQrRequest.getPage() + getParam(para));
        // 这个场景使用base64编码图片的形式返回
        genMpQrRequest.setHandleType(BASE64_HANDLE_TYPE);
        return wxMpQrServiceForAi.gen(genMpQrRequest);
    }

    private String getParam(Map<String, String> para) {
        StringBuilder result = new StringBuilder();
        result.append(InngkeAppConst.ASK_STR);
        int i = 0;
        for (Map.Entry<String, String> map : para.entrySet()) {
            if (i != 0) {
                result.append(InngkeAppConst.AND_STR);
            }
            i++;
            result.append(map.getKey()).append(InngkeAppConst.EQUAL_STR).append(map.getValue());
        }
        return result.toString();
    }

    @Override
    public BaseResponse<CrmMiniAppLoginResponse> getQrcodeResult(CrmWebScanQrcodeRequest request) {
        CrmMiniAppLoginResponse result;
        String json = redisTemplate.opsForValue().get(getLoginKey(request.getRequestId()));
        if (StringUtils.isEmpty(json)) {
            result = new CrmMiniAppLoginResponse();
            result.setRequestId(request.getRequestId());
            result.setStatus(-2);
            return BaseResponse.success(result);
        }
        return BaseResponse.success(jsonService.toObject(json, CrmMiniAppLoginResponse.class));
    }


    @Override
    public BaseResponse<CrmMiniAppLoginResponse> miniAppAuthLogin(CrmMpMiniAppLoginRequest request) {
        String requestId = request.getRequestId();
        CrmMiniAppLoginResponse result = new CrmMiniAppLoginResponse();
        result.setRequestId(requestId);
        String loginKey = getLoginKey(requestId);

        Long expire = Optional.ofNullable(redisTemplate.getExpire(loginKey)).orElse(10L);
        if (expire.equals(-1L)) {
            expire = 1000L;
        }
        String json = redisTemplate.opsForValue().get(loginKey);

        if (StringUtils.isEmpty(json)) {
            result.setRequestId(request.getRequestId());
            result.setStatus(-2);
            return BaseResponse.success(result);
        }
        CrmMiniAppLoginResponse crmScanQrCodeResponse = jsonService.toObject(json, CrmMiniAppLoginResponse.class);
        if (crmScanQrCodeResponse.getStatus().equals(2)) {
            return BaseResponse.error("已登录");
        }
        // 设置为已扫码
        crmScanQrCodeResponse.setStatus(1);
        String scanResponse = jsonService.toJson(crmScanQrCodeResponse);
        redisTemplate.opsForValue().set(loginKey, scanResponse, expire, TimeUnit.SECONDS);

        UserPermissionDto userPermission = getUserPermission(request.getUserId());
        //没有添加权限，并且不是pc客户端
        if (userPermission == null && !SOURCE_PC_CLIENT.equals(crmScanQrCodeResponse.getSource())) {
            result.setStatus(crmScanQrCodeResponse.getStatus());
            return BaseResponse.success(result);
        }

        String token = generatorPcUserToken(aiGcConfig.getBid(), request.getUserId(), 30);
        // 设置为已登录
        crmScanQrCodeResponse.setStatus(2);
        crmScanQrCodeResponse.setToken(token);

        Staff staff = staffService.getStaffByUserId(request.getUserId());
        Optional.ofNullable(staff).map(Staff::getDepartmentId).ifPresent(crmScanQrCodeResponse::setDepartmentId);
        long organizeId = staff != null ? staff.getOrganizeId() : 0L;
        if (staff != null && staff.getTester() != null && staff.getTester()) {
            //内部人员
            organizeId = 1L;
        }

        // 设置权限
        if (userPermission != null) {
            crmScanQrCodeResponse.setDepartmentId(userPermission.getDepartmentId());
            crmScanQrCodeResponse.setPermissionVal(userPermission.getPermissionVal());
        }
        crmScanQrCodeResponse.setOrganizeId(organizeId);

        scanResponse = jsonService.toJson(crmScanQrCodeResponse);
        redisTemplate.opsForValue().set(loginKey, scanResponse, 10, TimeUnit.SECONDS);
        result.setToken(token);
        result.setStatus(crmScanQrCodeResponse.getStatus());
        return BaseResponse.success(result);
    }

    @Override
    public BaseResponse<String> getScene(Long id) {
        GetMpQrSceneRequest getMpQrSceneRequest = new GetMpQrSceneRequest();
        getMpQrSceneRequest.setId(id);
        getMpQrSceneRequest.setBid(aiGcConfig.getBid());

        return BaseResponse.success(wxMpQrServiceForClient.getScene(getMpQrSceneRequest));
    }

    /**
     * 生成PC token
     */
    private String generatorPcUserToken(int bid, Long id, int plusDay) {
        JwtPayload jwtPayload = new JwtPayload();
        jwtPayload.setBid(bid);
        jwtPayload.setCid(id);
        jwtPayload.setExp(DateTimeUtils.getMilli(LocalDateTime.now().plusDays(plusDay)) / 1000);
        jwtPayload.setAppId(1);
        return jwtService.gen(jwtPayload);
    }


    private UserPermissionDto getUserPermission(Long userId) {
        Staff staff = staffService.getStaffByUserId(userId);
        if (Objects.isNull(staff) || staff.getOrganizeId().equals(0L)) {
            return null;
        }
        UserRolesRequest request = new UserRolesRequest();
        request.setBid(aiGcConfig.getBid());
        request.setUserId(userId);
        request.setAppCode("ai-pc");
        String permissions = rbacUserServiceForAi.getUserPermissions(request);
        if (StringUtils.isEmpty(permissions)) {
            return null;
        }

        UserPermissionDto dto = new UserPermissionDto();
        dto.setUserId(staff.getId());
        dto.setOrganizeId(staff.getOrganizeId());
        dto.setDepartmentId(staff.getDepartmentId());
        dto.setPermissionVal(permissions);
        dto.setNickname(staff.getName());
        return dto;
    }

}
