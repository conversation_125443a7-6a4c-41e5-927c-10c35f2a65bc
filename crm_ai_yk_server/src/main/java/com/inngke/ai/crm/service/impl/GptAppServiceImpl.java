package com.inngke.ai.crm.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.inngke.ai.crm.db.crm.entity.GptAppConf;
import com.inngke.ai.crm.db.crm.entity.GptAppDataset;
import com.inngke.ai.crm.db.crm.entity.GptAppPrePrompt;
import com.inngke.ai.crm.db.crm.manager.GptAppConfManager;
import com.inngke.ai.crm.db.crm.manager.GptAppDatasetManager;
import com.inngke.ai.crm.db.crm.manager.GptAppPrePromptManager;
import com.inngke.ai.crm.dto.enums.AppTypeEnum;
import com.inngke.ai.crm.dto.request.DifyApiRequest;
import com.inngke.ai.crm.dto.response.DifyAppDto;
import com.inngke.ai.crm.service.GptAppService;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.JsonService;
import com.inngke.ip.ai.dify.dto.request.*;
import com.inngke.ip.ai.dify.enums.DifyResponseModeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class GptAppServiceImpl implements GptAppService {

    @Autowired
    private GptAppConfManager gptAppConfManager;

    @Autowired
    private GptAppDatasetManager gptAppDatasetManager;

    @Autowired
    private GptAppPrePromptManager gptAppPrePromptManager;

    @Autowired
    private JsonService jsonService;

    @Override
    public DifyAppDto getDifyAppDto(AppTypeEnum appType, DifyApiRequest difyApiRequest) {
        GptAppConf gptAppConf = gptAppConfManager.getOne(
                Wrappers.<GptAppConf>query()
                        .eq(GptAppConf.APP_TYPE, appType.getType())
                        .eq(GptAppConf.ENABLE, true)
                        .in(GptAppConf.TENANT_ID, 0, difyApiRequest.getTenantId())
                        .last(InngkeAppConst.STR_LIMIT_1)
                        .orderByDesc(GptAppConf.TENANT_ID)
        );
        if (gptAppConf == null) {
            throw new InngkeServiceException("未配置AI话术应用");
        }

        ChatMessagesRequest request = new ChatMessagesRequest();
        request.setConversationId(null);
        request.setInputs(Maps.newHashMap());
        request.setResponseMode(gptAppConf.getResponseModel() == 1 ? DifyResponseModeEnum.STREAMING.getType() : DifyResponseModeEnum.BLOCKING.getType());
        request.setUser(difyApiRequest.getUserId());
        request.setQuery(difyApiRequest.getQuery());

        DifyModelConfig config = getDifyModelConfig(difyApiRequest, gptAppConf);
        request.setDifyModelConfig(config);

        DifyAppDto dto = new DifyAppDto();
        dto.setRequest(request);
        dto.setConf(gptAppConf);
        return dto;
    }

    @Override
    public GptAppConf getGptAppConf(AppTypeEnum appType) {
        return gptAppConfManager.getOne(
                Wrappers.<GptAppConf>query()
                        .eq(GptAppConf.ENABLE, true)
                        .eq(GptAppConf.APP_TYPE, appType.getType())
                        .last(InngkeAppConst.STR_LIMIT_1)
        );
    }

    private DifyModelConfig getDifyModelConfig(DifyApiRequest difyApiRequest, GptAppConf gptAppConf) {
        String sceneCode = difyApiRequest.getSceneCode();
        if (StringUtils.isEmpty(sceneCode)) {
            //如果为空时，则不覆盖配置
            return null;
        }
        DifyModelConfig config = new DifyModelConfig();

        //加载数据集
        List<DifyModelConfigTool> tools = gptAppDatasetManager.list(
                        Wrappers.<GptAppDataset>query()
                                .eq(GptAppDataset.GPT_APP_CONF_ID, gptAppConf.getId())
                                .eq(GptAppDataset.ENABLE, true)
                ).stream()
                .map(dataset -> {
                    DifyModelConfigTool tool = new DifyModelConfigTool();
                    tool.setDataset(new DifyModelConfigDataset());
                    tool.getDataset().setEnabled(true);
                    tool.getDataset().setId(dataset.getDatasetId());
                    return tool;
                }).collect(Collectors.toList());
        AgentMode agentMode = new AgentMode();
        agentMode.setEnabled(true);
        agentMode.setTools(tools);
        config.setAgentMode(agentMode);

        //大模型配置
        DifyModelConfigModel model;
        if (StringUtils.isEmpty(gptAppConf.getLlmModelConf())) {
            model = new DifyModelConfigModel();
            CompletionParams completionParams = new CompletionParams();
            completionParams.setFrequencyPenalty(0D);
            completionParams.setMaxTokens(512);
            completionParams.setPresencePenalty(0D);
            completionParams.setTemperature(1D);
            completionParams.setTopP(1D);
            model.setCompletionParams(completionParams);
            model.setName("gpt-3.5-turbo");
            model.setProvider("openai");
        } else {
            model = jsonService.toObject(gptAppConf.getLlmModelConf(), DifyModelConfigModel.class);
        }
        config.setModel(model);

        MoreLikeThis moreLikeThis = new MoreLikeThis();
        moreLikeThis.setEnabled(false);
        config.setMoreLikeThis(moreLikeThis);
        config.setOpeningStatement(null);

        //加载预置话术
//        String sceneCode = StringUtils.isEmpty(difyApiRequest.getQuery()) ? "normal" : "no_question";
        GptAppPrePrompt gptAppPrePrompt = gptAppPrePromptManager.getOne(
                Wrappers.<GptAppPrePrompt>query()
                        .eq(GptAppPrePrompt.GPT_APP_CONF_ID, gptAppConf.getId())
                        .eq(GptAppPrePrompt.ENABLE, true)
                        .eq(GptAppPrePrompt.SCENE_CODE, sceneCode)
                        .last(InngkeAppConst.STR_LIMIT_1)
        );
        if (gptAppPrePrompt != null) {
            config.setPrePrompt(gptAppPrePrompt.getPrePrompt());
        }

        SpeechToText speechToText = new SpeechToText();
        speechToText.setEnabled(false);
        config.setSpeechToText(speechToText);

        SuggestedQuestionsAfterAnswer suggestedQuestionsAfterAnswer = new SuggestedQuestionsAfterAnswer();
        suggestedQuestionsAfterAnswer.setEnabled(false);
        config.setSuggestedQuestionsAfterAnswer(suggestedQuestionsAfterAnswer);
        config.setUserInputForm(Lists.newArrayList());
        return config;
    }
}
