package com.inngke.ai.crm.service.qunfeng.init;

import com.inngke.ai.crm.db.crm.entity.Organize;
import com.inngke.ai.crm.db.crm.entity.QunFengProduct;
import com.inngke.ip.ai.qunfeng.dto.QunFengUserDto;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
public class OrganizeInit extends Init {

    @Override
    public Class<? extends Init> next() {
        return UserInit.class;
    }

    @Override
    public void init(InitContext ctx) {
        QunFengUserDto qunFengUser = ctx.getQunFengUser();
        QunFengProduct qunFengProduct = ctx.getQunFengProduct();

        String displayName = qunFengUser.getDisplayName();

        Organize organize = new Organize();
        organize.setName(displayName);
        organize.setStatus(1);
        organize.setBalance(qunFengProduct.getBalance());
        organize.setPlatform(1);
        organize.setUseExternalModel(1);
        organize.setCreateTime(LocalDateTime.now());

        ctx.setOrganize(organize);
    }

}
