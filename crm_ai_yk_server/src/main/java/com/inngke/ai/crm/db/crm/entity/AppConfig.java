package com.inngke.ai.crm.db.crm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2023-09-07 10:03
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AppConfig {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    /**
     * CODE
     * @see com.inngke.ai.crm.dto.enums.AppConfigCodeEnum
     */
    private String code;

    /**
     * VALUE
     */
    private String value;

    /**
     * 1:正常 0:失效
     */
    private Integer enable;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    public static final String ID = "id";


    public static final String CODE = "code";

    public static final String VALUE = "value";

    public static final String ENABLE = "enable";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";
}
