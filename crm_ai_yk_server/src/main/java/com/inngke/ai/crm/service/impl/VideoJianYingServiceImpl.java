package com.inngke.ai.crm.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.ai.crm.api.video.VideoApi;
import com.inngke.ai.crm.core.CrmServiceConsts;
import com.inngke.ai.crm.db.crm.entity.AiGenerateTask;
import com.inngke.ai.crm.db.crm.entity.AiGenerateVideoOutput;
import com.inngke.ai.crm.db.crm.entity.VideoCreateTask;
import com.inngke.ai.crm.db.crm.manager.AiGenerateTaskManager;
import com.inngke.ai.crm.db.crm.manager.AiGenerateVideoOutputManager;
import com.inngke.ai.crm.db.crm.manager.MashUpTaskManager;
import com.inngke.ai.crm.db.crm.manager.VideoCreateTaskManager;
import com.inngke.ai.crm.dto.enums.AiGenerateTaskStatusEnum;
import com.inngke.ai.crm.dto.request.video.UpdateMashUpTaskStatusRequest;
import com.inngke.ai.crm.dto.request.video.VideoJianyingTaskFetchRequest;
import com.inngke.ai.crm.service.VideoCreateService;
import com.inngke.ai.crm.service.VideoJianYingService;
import com.inngke.ai.dto.request.JianYingCreatedRequest;
import com.inngke.ai.dto.request.JianYingTaskUpdateRequest;
import com.inngke.ai.dto.request.VideoCompressRequest;
import com.inngke.ai.dto.request.VideoCreateInfoRequest;
import com.inngke.ai.dto.response.JianYingTask;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.dto.Lock;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.LockService;
import com.inngke.common.service.SnowflakeIdService;
import com.inngke.common.utils.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class VideoJianYingServiceImpl implements VideoJianYingService {
    private static final Logger logger = LoggerFactory.getLogger(VideoJianYingServiceImpl.class);
    public static final String JIAN_YING_AGENT_KEY = CrmServiceConsts.CACHE_KEY_PRE + "jianyingAgent";

    @Autowired
    private VideoCreateTaskManager videoCreateTaskManager;

    @Autowired
    private VideoCreateService videoCreateService;

    @Autowired
    private VideoApi videoApi;

    @Autowired
    private SnowflakeIdService snowflakeIdService;

    @Autowired
    private AiGenerateVideoOutputManager aiGenerateVideoOutputManager;

    @Autowired
    private AiGenerateTaskManager aiGenerateTaskManager;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private LockService lockService;

    @Autowired
    private MashUpTaskManager mashUpTaskManager;

    @Override
    public void jianyingCreate(JianYingCreatedRequest request) {
        LocalDateTime now = LocalDateTime.now();
        VideoCreateTask videoCreateTask = new VideoCreateTask();
        videoCreateTask.setId(snowflakeIdService.getId());
        videoCreateTask.setTaskId(request.getTaskId());
        videoCreateTask.setPlatformType(1);
        videoCreateTask.setStatus(0);
        videoCreateTask.setProjectZipUrl(request.getJianyingZipUrl());
        videoCreateTask.setVideoUrl(null);
        videoCreateTask.setCreateTime(now);
        videoCreateTask.setTaskDistributeTime(null);
        videoCreateTask.setRetryCount(0);
        videoCreateTask.setErrorMsg(null);
        videoCreateTask.setTaskDistributeHost(InngkeAppConst.EMPTY_STR);
        videoCreateTask.setFinishTime(null);
        videoCreateTask.setUpdateTime(now);
        videoCreateTaskManager.save(videoCreateTask);
        AsyncUtils.runAsync(() -> {
            UpdateMashUpTaskStatusRequest updateMashUpTaskStatusRequest = new UpdateMashUpTaskStatusRequest();
            updateMashUpTaskStatusRequest.setStatus(2);
            updateMashUpTaskStatusRequest.setId(request.getTaskId());
            mashUpTaskManager.updateMashUpTaskStatus(updateMashUpTaskStatusRequest);
        });
    }

    @Override
    public void jianYingTaskUpdate(JianYingTaskUpdateRequest request) {
        Long taskId = request.getTaskId();
        List<VideoCreateTask> videoCreateTaskList = videoCreateTaskManager.list(Wrappers.<VideoCreateTask>query().eq(VideoCreateTask.TASK_ID, taskId).eq(VideoCreateTask.STATUS, 1));
        if (CollectionUtils.isEmpty(videoCreateTaskList)) {
            logger.warn("无法找到任务：task_id={}, status=1", taskId);
            return;
        }
        VideoCreateTask videoCreateTask = videoCreateTaskList.get(0);
        AiGenerateTask aiGenerateTask = aiGenerateTaskManager.getOne(
                Wrappers.<AiGenerateTask>query()
                        .eq(AiGenerateTask.ID, videoCreateTask.getTaskId())
                        .select(AiGenerateTask.ORGANIZE_ID)
        );

        Integer status = request.getStatus();
        LocalDateTime now = LocalDateTime.now();
        UpdateWrapper<VideoCreateTask> updateWrapper = Wrappers.<VideoCreateTask>update().eq(VideoCreateTask.ID, videoCreateTask.getId());
        if (status < 0) {
            if (StringUtils.hasLength(request.getErrorInfo())) {
                updateWrapper.set(VideoCreateTask.ERROR_MSG, request.getErrorInfo());
            }
            if (status == -2) {
                updateWrapper.set(VideoCreateTask.RETRY_COUNT, videoCreateTask.getRetryCount() + 1);
            } else if (status == -1) {
                //不需要做任何事
            } else {
                logger.warn("未知状态，丢弃");
                return;
            }
            //尝试创建新的任务
            createRetryTask(videoCreateTask);
        }

        if (StringUtils.hasLength(request.getVideoUrl())) {
            updateWrapper.set(VideoCreateTask.VIDEO_URL, request.getVideoUrl()).set(VideoCreateTask.FINISH_TIME, now);

            //设置为视频创作成功
            VideoCreateInfoRequest updateVideoCreateStatusRequest = new VideoCreateInfoRequest();
            updateVideoCreateStatusRequest.setTaskId(taskId);
            updateVideoCreateStatusRequest.setVideoUrl(request.getVideoUrl());
            updateVideoCreateStatusRequest.setStatus(AiGenerateTaskStatusEnum.SUCCESS.getCode());
            BaseResponse<Boolean> resp = videoCreateService.updateVideoCreateStatus(updateVideoCreateStatusRequest);
            if (!BaseResponse.responseSuccessWithNonNullData(resp)) {
                logger.warn("更新视频创作状态失败：{}", JsonUtil.toJsonString(resp));
            }

            //发起压缩请求
            AsyncUtils.runAsync(() -> compressJianYingVideo(aiGenerateTask == null ? 0L : aiGenerateTask.getOrganizeId(), request), false);
        }
        updateWrapper.set(VideoCreateTask.STATUS, status).set(VideoCreateTask.UPDATE_TIME, now);
        videoCreateTaskManager.update(updateWrapper);

        if (videoCreateTaskList.size() > 1) {
            //其它的直接删掉
            for (int i = 1; i < videoCreateTaskList.size(); i++) {
                VideoCreateTask task = videoCreateTaskList.get(i);
                videoCreateTaskManager.update(
                        Wrappers.<VideoCreateTask>update()
                                .eq(VideoCreateTask.ID, task.getId())
                                .set(VideoCreateTask.STATUS, -2)
                );
            }
        }
    }

    @Override
    public boolean createRetryTask(VideoCreateTask videoCreateTask) {
        //检查是否还有其它未处理过本任务的剪映服务器可使用
        Set<String> retriedHostNames = videoCreateTaskManager.list(Wrappers.<VideoCreateTask>query().eq(VideoCreateTask.TASK_ID, videoCreateTask.getTaskId()).isNotNull(VideoCreateTask.TASK_DISTRIBUTE_HOST).select(VideoCreateTask.TASK_DISTRIBUTE_HOST)).stream().map(VideoCreateTask::getTaskDistributeHost).collect(Collectors.toSet());

        if (retriedHostNames.size() >= 3) {
            //已经重试过3次了，不再重试
            return false;
        }

        Set<ZSetOperations.TypedTuple<String>> zset = redisTemplate.opsForZSet().rangeWithScores(JIAN_YING_AGENT_KEY, 0, -1);
        if (zset == null) {
            //没有存活的
            return false;
        }
        // 30分钟未活跃过，就当成是已经失效
        boolean anyAgent = zset.stream().anyMatch((ZSetOperations.TypedTuple<String> tuple) -> {
            int second = Objects.requireNonNull(tuple.getScore()).intValue();
            if (second < System.currentTimeMillis() / 1000 - 30 * 60) {
                // 30分钟未活跃过，就当成是已经失效
                return false;
            }
            String value = tuple.getValue();
            return !retriedHostNames.contains(value);
        });
        if (!anyAgent) {
            return false;
        }

        logger.info("创建重试任务：{}", videoCreateTask.getTaskId());
        VideoCreateTask newVideoTask = new VideoCreateTask().setId(snowflakeIdService.getId()).setTaskId(videoCreateTask.getTaskId()).setCreateTime(videoCreateTask.getCreateTime()).setRetryCount(0).setPlatformType(videoCreateTask.getPlatformType()).setProjectZipUrl(videoCreateTask.getProjectZipUrl()).setStatus(0).setUpdateTime(LocalDateTime.now());
        videoCreateTaskManager.save(newVideoTask);
        return true;
    }

    private void compressJianYingVideo(Long organizeId, JianYingTaskUpdateRequest request) {
        Long taskId = request.getTaskId();
        BaseResponse<VideoCreateInfoRequest> compressResult = videoApi.compressVideo(
                new VideoCompressRequest()
                        .setTaskId(taskId)
                        .setOrganizeId(organizeId)
                        .setVideoUrl(request.getVideoUrl())
        );
        logger.info("视频压缩响应：{}", JsonUtil.toJsonString(compressResult));
        if (BaseResponse.responseSuccessWithNonNullData(compressResult)) {
            VideoCreateInfoRequest info = compressResult.getData();
            String content = info.getVideoContent();
            if (StringUtils.hasLength(info.getTags())) {
                content += InngkeAppConst.WHITE_SPACE_STR + info.getTags().trim();
            }
            aiGenerateVideoOutputManager.update(
                    Wrappers.<AiGenerateVideoOutput>update()
                            .eq(AiGenerateVideoOutput.TASK_ID, taskId)
                            .set(AiGenerateVideoOutput.VIDEO_TITLE, info.getCoverTitle())
                            .set(AiGenerateVideoOutput.VIDEO_CONTENT, content)
                            .set(AiGenerateVideoOutput.KEY_FRAME, Optional.ofNullable(info.getKeyFrames()).orElse(InngkeAppConst.EMPTY_STR))
                            .set(AiGenerateVideoOutput.VIDEO_URL, info.getVideoUrl())
            );

            if (StringUtils.hasLength(content)) {
                aiGenerateTaskManager.update(Wrappers.<AiGenerateTask>update().eq(AiGenerateTask.ID, taskId).set(AiGenerateTask.TITLE, info.getVideoContent()).set(StringUtils.hasLength(info.getTags()), AiGenerateTask.TAGS, info.getTags()));
            }
        }
    }

    @Override
    public JianYingTask getJianYingTask(VideoJianyingTaskFetchRequest request) {
        //注册剪映处理终端
        String hostName = request.getHostName();
        if (!StringUtils.hasLength(hostName)) {
            throw new InngkeServiceException("非法请求！");
        }
        long second = System.currentTimeMillis() / 1000;
        redisTemplate.opsForZSet().add(JIAN_YING_AGENT_KEY, hostName, second);
        if (second % 10 == 0) {
            redisTemplate.expire(JIAN_YING_AGENT_KEY, 1, TimeUnit.HOURS);
        }

        String lockKey = CrmServiceConsts.CACHE_LOCK_KEY_PRE + "jianyingTask";
        Lock lock = lockService.getLock(lockKey, 5);
        if (lock == null) {
            return null;
        }
        try {
            return getJianYingTask(hostName);
        } finally {
            lock.unlock();
        }
    }

    private JianYingTask getJianYingTask(String hostName) {
        VideoCreateTask videoCreateTask = videoCreateTaskManager.getTask(hostName);
        if (videoCreateTask == null) {
            return null;
        }

        videoCreateTaskManager.update(Wrappers.<VideoCreateTask>update().eq(VideoCreateTask.ID, videoCreateTask.getId()).set(VideoCreateTask.STATUS, 1).set(VideoCreateTask.TASK_DISTRIBUTE_HOST, hostName).set(VideoCreateTask.TASK_DISTRIBUTE_TIME, LocalDateTime.now()));
        AiGenerateTask aiGenerateTask = aiGenerateTaskManager.getOne(
                Wrappers.<AiGenerateTask>query()
                        .eq(AiGenerateTask.ID, videoCreateTask.getTaskId()
                        ).select(AiGenerateTask.ORGANIZE_ID)
        );
        return new JianYingTask().setTaskId(videoCreateTask.getTaskId()).setOrganizeId(Math.toIntExact(aiGenerateTask == null ? 0 : aiGenerateTask.getOrganizeId())).setTaskType(0).setJianYingZipUrl(videoCreateTask.getProjectZipUrl());
    }
}
