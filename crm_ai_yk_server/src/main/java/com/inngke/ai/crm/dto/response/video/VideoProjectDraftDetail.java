package com.inngke.ai.crm.dto.response.video;

import com.inngke.ai.crm.dto.request.video.VideoCreateWithMaterialRequest;
import lombok.Data;

@Data
public class VideoProjectDraftDetail extends VideoProjectDraftDto {
    /**
     * 视频工程配置，即 request 数据，json结构
     *
     * @demo {...}
     */
    private VideoCreateWithMaterialRequest projectContent;

    /**
     * 来源：0=新增 1=草稿裂变 2=草稿复制 10=视频二次创作
     *
     * @demo 1
     */
    private Integer createType;

    /**
     * 来源ID，比如是创作草稿裂变，则是草稿ID
     *
     * @demo 12343123232323
     */
    private Long createFromId;

    /**
     * 员工ID
     *
     * @demo 343134231321333
     */
    private Long staffId;

    /**
     * 企业ID
     *
     * @demo 343134231321333
     */
    private Long organizeId;

    /**
     * 脚本库ID
     *
     * @demo 343134231321333
     */
    private Long videoScriptId;
}
