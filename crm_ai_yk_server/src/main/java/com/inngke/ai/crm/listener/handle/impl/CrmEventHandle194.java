package com.inngke.ai.crm.listener.handle.impl;

import com.inngke.ai.crm.db.crm.entity.AiGenerateVideoOutput;
import com.inngke.ai.crm.db.crm.manager.AiGenerateVideoOutputManager;
import com.inngke.ai.crm.dto.request.CrmEventLogSaveRequest;
import com.inngke.ai.crm.listener.handle.CrmEventHandle;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024-01-25 10:16
 **/
@Component
public class CrmEventHandle194 implements CrmEventHandle {

    @Autowired
    private AiGenerateVideoOutputManager aiGenerateVideoOutputManager;


    @Override
    public Long eventId() {
        return 194L;
    }

    @Override
    public void handle(CrmEventLogSaveRequest request) {
        Map<String, Serializable> ext = request.getExt();
        if(CollectionUtils.isEmpty(ext)){
            return;
        }
        String aiGenerateVideoOutputIdStr = (String)ext.get("aiGenerateVideoOutputId");
        Long aiGenerateVideoOutputId = Long.valueOf(aiGenerateVideoOutputIdStr);
        AiGenerateVideoOutput aiGenerateVideoOutput = aiGenerateVideoOutputManager.getById(aiGenerateVideoOutputId);
        if(Objects.isNull(aiGenerateVideoOutput) || Boolean.TRUE.equals(aiGenerateVideoOutput.getUsed())){
            return;
        }
        AiGenerateVideoOutput update = new AiGenerateVideoOutput();
        update.setId(aiGenerateVideoOutput.getId());
        update.setUsed(true);
        aiGenerateVideoOutputManager.updateById(update);

    }
}
