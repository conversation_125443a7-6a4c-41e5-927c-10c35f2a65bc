package com.inngke.ai.crm.service.schedule;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.ai.crm.client.common.MqServiceClientForAi;
import com.inngke.ai.crm.core.CrmServiceConsts;
import com.inngke.ai.crm.db.crm.entity.AiGenerateTask;
import com.inngke.ai.crm.db.crm.entity.AiGenerateVideoOutput;
import com.inngke.ai.crm.db.crm.manager.AiGenerateTaskManager;
import com.inngke.ai.crm.db.crm.manager.AiGenerateVideoOutputManager;
import com.inngke.ai.crm.db.crm.manager.GptAppConfManager;
import com.inngke.ai.crm.db.crm.manager.UserManager;
import com.inngke.ai.crm.dto.enums.AiGenerateEventEnum;
import com.inngke.ai.crm.dto.enums.AiGenerateTaskStatusEnum;
import com.inngke.ai.crm.dto.enums.AiProductIdEnum;
import com.inngke.ai.crm.dto.request.AiGenerateGetRequest;
import com.inngke.ai.crm.dto.response.ai.AiGenerateMqPayload;
import com.inngke.ai.crm.service.AiLockService;
import com.inngke.ai.crm.service.XiaoHongShuGenerateService;
import com.inngke.ai.crm.service.message.CrmMessageManagerService;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.dto.Lock;
import com.inngke.common.service.LockService;
import com.inngke.ip.ai.dify.api.DifyApi;
import com.inngke.ip.ai.dify.config.DifyProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-09-06 09:36
 **/
@Component
public class AiGcSchedule {
    private static final Logger log = LoggerFactory.getLogger(AiGcSchedule.class);
    @Autowired
    private AiGenerateTaskManager aiGenerateTaskManager;

    @Autowired
    private AiGenerateVideoOutputManager aiGenerateVideoOutputManager;

    @Autowired
    private MqServiceClientForAi mqServiceClientForAi;

    @Autowired
    private AiLockService aiLockService;

    @Autowired
    private LockService lockService;
    @Autowired
    private XiaoHongShuGenerateService xiaoHongShuGenerateService;

    @Autowired
    private DifyApi difyApi;

    @Autowired
    private GptAppConfManager gptAppConfManager;

    @Autowired
    private DifyProperties difyProperties;

    @Autowired
    private CrmMessageManagerService crmMessageManagerService;

    @Autowired
    private UserManager userManager;

    @Autowired
    @Qualifier("aiThreadPool")
    private Executor executor;

    @Scheduled(fixedRate = 600000, initialDelay = 63000)
    public void markErrorVideo() {
        Lock lock = lockService.getLock(CrmServiceConsts.CACHE_LOCK_KEY_PRE + "errorMark", 60);
        if (Objects.isNull(lock)) {
            return;
        }
        aiGenerateTaskManager.list(
                new QueryWrapper<AiGenerateTask>()
                        .eq(AiGenerateTask.AI_PRODUCT_ID, 10)
                        .eq(AiGenerateTask.STATUS, AiGenerateTaskStatusEnum.PROCESSING.getCode())
                        .eq(AiGenerateTask.DELETED, 0)
                        .le(AiGenerateTask.CREATE_TIME, LocalDateTime.now().minusMinutes(15))
                        .select(AiGenerateTask.ID, AiGenerateTask.STATUS)
        ).forEach(task -> {
            //检查 ai_generate_video_output 是否有链接
            AiGenerateVideoOutput output = aiGenerateVideoOutputManager.getOne(
                    Wrappers.<AiGenerateVideoOutput>query()
                            .eq(AiGenerateVideoOutput.TASK_ID, task.getId())
                            .isNotNull(AiGenerateVideoOutput.VIDEO_URL)
                            .ne(AiGenerateVideoOutput.VIDEO_URL, InngkeAppConst.EMPTY_STR)
                            .last(InngkeAppConst.STR_LIMIT_1)
                            .select(AiGenerateVideoOutput.VIDEO_URL)
            );
            int newState = AiGenerateTaskStatusEnum.FAIL.getCode();
            if (output != null) {
                log.info("视频创作任务{}, 已经成功：{}", task.getId(), output.getVideoUrl());
                newState = AiGenerateTaskStatusEnum.SUCCESS.getCode();
            }
            aiGenerateTaskManager.update(
                    Wrappers.<AiGenerateTask>update()
                            .eq(AiGenerateTask.ID, task.getId())
                            .set(AiGenerateTask.STATUS, newState)
            );
        });
    }

    /**
     * 60秒执行一次,查询ai_generate_task表一小时未生成成功的任务
     */
//    @Scheduled(fixedRate = 60000)
    public void aiGcGeneratorTimeOut() {
        Lock aiGcGeneratorTimeOutLock = aiLockService.getAiGcGeneratorTimeOutLock(false);
        if (Objects.isNull(aiGcGeneratorTimeOutLock)) {
            return;
        }
        List<AiGenerateTask> list = aiGenerateTaskManager.list(new QueryWrapper<AiGenerateTask>()
                .eq(AiGenerateTask.STATUS, AiGenerateTaskStatusEnum.PROCESSING.getCode())
                .le(AiGenerateTask.CREATE_TIME, LocalDateTime.now().minusMinutes(10))
                .le(AiGenerateTask.UPDATE_TIME, LocalDateTime.now().minusMinutes(3)));
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        log.info("ai_generate_task超时定时任务查询出的Id:{}",
                list.stream().map(AiGenerateTask::getId).collect(Collectors.toList()));

        List<AiGenerateTask> updates = new ArrayList<>();

        list.forEach(item -> {
            if (!AiProductIdEnum.XIAO_HOME_SHU.getType().equals(item.getAiProductId())) {
                return;
            }
            //openai模型
            if (Integer.valueOf(1).equals(item.getUseExternalModel())) {
                return;
            }

            if (item.getRetryCount().compareTo(3) >= 0) {
                AiGenerateMqPayload aiGenerateMqPayload = new AiGenerateMqPayload();
                aiGenerateMqPayload.setId(item.getId());
                aiGenerateMqPayload.setEvent(AiGenerateEventEnum.FAIL.getEvent());
                mqServiceClientForAi.sendAiGenerateMq(aiGenerateMqPayload);
                return;
            }

            try {
                AiGenerateGetRequest aiGenerateGetRequest = new AiGenerateGetRequest();
                aiGenerateGetRequest.setId(item.getId());
                xiaoHongShuGenerateService.get(null, aiGenerateGetRequest);
            } finally {
                AiGenerateTask aiGenerateTask = new AiGenerateTask();
                aiGenerateTask.setId(item.getId());
                aiGenerateTask.setRetryCount(item.getRetryCount() + 1);
                updates.add(aiGenerateTask);
            }
        });
        if (!CollectionUtils.isEmpty(updates)) {
            aiGenerateTaskManager.updateBatchById(updates);
        }
    }
}
