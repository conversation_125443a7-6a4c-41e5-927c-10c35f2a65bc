package com.inngke.ai.crm.service.qunfeng.init;

import com.inngke.ai.crm.db.crm.entity.*;
import com.inngke.ai.crm.dto.enums.CoinProductPeriodTypeEnum;
import com.inngke.common.core.utils.SnowflakeHelper;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Optional;

@Component
public class VipInit extends Init {

    @Override
    public Class<? extends Init> next() {
        return CoinInit.class;
    }

    @Override
    public void init(InitContext ctx) {
        User user = ctx.getUser();
        Staff staff = ctx.getStaff();
        CoinProduct initcoinProduct = ctx.getInitCoinProduct();

        UserVip userVip = initUserVip(staff, initcoinProduct);
        user.setCurrentVipId(userVip.getId());
        user.setCurrentVipExpiredTime(getExtTime(userVip.getPeriodType()));

        ctx.setUserVip(userVip);
    }

    private CoinOrder initCoinOrder(User user, CoinProduct initcoinProduct) {
        CoinOrder coinOrder = new CoinOrder();
        coinOrder.setId(SnowflakeHelper.getId());
        coinOrder.setOrderId(SnowflakeHelper.getId());
        coinOrder.setCoinProductId(initcoinProduct.getId());
        coinOrder.setUserId(user.getId());
        coinOrder.setStatus(1);
        coinOrder.setTitle(initcoinProduct.getTitle());
        coinOrder.setAmount(initcoinProduct.getAmount());
        coinOrder.setOrgAmount(initcoinProduct.getOrgAmount());
        coinOrder.setCoin(initcoinProduct.getCoin());
        coinOrder.setExpireTime(LocalDateTime.now().plusMinutes(2));
        coinOrder.setCreateTime(LocalDateTime.now());

        return coinOrder;
    }

    private UserVip initUserVip(Staff staff, CoinProduct initcoinProduct) {
        UserVip userVip = new UserVip();
        userVip.setId(SnowflakeHelper.getId());
        userVip.setCoinOrderId(0L);
        userVip.setStaffId(staff.getId());
        userVip.setRealName(staff.getName());
        userVip.setCoinProductId(initcoinProduct.getId());
        userVip.setVipType(initcoinProduct.getVipType());
        userVip.setPeriodType(initcoinProduct.getPeriodType());
        userVip.setCoin(initcoinProduct.getCoin());
        userVip.setAmount(initcoinProduct.getAmount());

        CoinProductPeriodTypeEnum code = CoinProductPeriodTypeEnum.getByCode(initcoinProduct.getPeriodType());

        Optional.ofNullable(code).ifPresent(coinProductPeriodTypeEnum -> {
            userVip.setRemainCount(code.getUserVipCount() - 1);
            userVip.setTotalCount(code.getUserVipCount());
        });
        userVip.setEnable(true);
        userVip.setActivationTime(LocalDateTime.now());
        userVip.setCreateTime(LocalDateTime.now());

        return userVip;
    }
}
