package com.inngke.ai.crm.service.impl;

import com.inngke.ai.crm.client.VueProMenuServiceForAi;
import com.inngke.ai.crm.core.config.AiGcConfig;
import com.inngke.ai.crm.db.crm.entity.Department;
import com.inngke.ai.crm.db.crm.entity.User;
import com.inngke.ai.crm.dto.response.org.department.DepartmentTreeDto;
import com.inngke.ai.crm.service.CrmMenuService;
import com.inngke.ai.crm.service.StaffUserRelationService;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.ip.auth.rbac.dto.VueProMenuDto;
import com.inngke.ip.auth.rbac.dto.request.MenuGetRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-10-18 15:22
 **/
@Service
public class CrmMenuServiceImpl implements CrmMenuService {

    private static final Set<String> codes = Set.of(
            "data_management",
            "xhs_data",
            /*"xhs_company_data",
            "xhs_enterprise_data",*/
            "xhs_department_data",
            "xhs_staff_data",
            "dy_data",
            /*"dy_enterprise_data",*/
            "dy_department_data",
            "dy_staff_data",
            "xhs_list",
            "dy_list");

    @Autowired
    private VueProMenuServiceForAi vueProMenuServiceForAi;

    @Autowired
    private AiGcConfig aiGcConfig;

    @Autowired
    DepartmentCacheFactory departmentCacheFactory;

    @Autowired
    StaffUserRelationService staffUserRelationService;


    @Override
    public BaseResponse<List<VueProMenuDto>> getVueProMenu(JwtPayload jwtPayload) {
        MenuGetRequest menuGetRequest = new MenuGetRequest();
        menuGetRequest.setBid(aiGcConfig.getBid());
        menuGetRequest.setAppId(5);
        menuGetRequest.setUserId(jwtPayload.getCid());
        menuGetRequest.setGroupCode("pc-main");
        List<VueProMenuDto> vueProMenu = vueProMenuServiceForAi.getVueProMenu(menuGetRequest);

        /*
        1、总部员工：显示pc端-全部目录及页面
        2、非总部员工：仅显示pc端-【数据管理、小红书笔记、AI短视频】的目录及页面，不显示“员工列表”、“分配记录”的目录及页面。
        */
        StaffUserRelationService.StaffUserRelation staffUserRelation = staffUserRelationService.initFromUserIds(List.of(jwtPayload.getCid()));
        Department userDepartment = staffUserRelation.getUserDepartment(jwtPayload.getCid());
        User user = staffUserRelation.getUser(jwtPayload.getCid());
        DepartmentCacheFactory.DepartmentCache cache = departmentCacheFactory.getCache(user.getOrganizeId().intValue());
        List<DepartmentTreeDto> roots = cache.getRoots();
        if (CollectionUtils.isEmpty(roots)) {
            throw new InngkeServiceException("非企业用户无法登录");
        }

        DepartmentTreeDto departmentTreeDto = roots.get(0);


        if (!userDepartment.getId().equals(departmentTreeDto.getId())) {
            vueProMenu = vueProMenu.stream().filter(vueProMenuDto -> codes.contains(vueProMenuDto.getCode())).collect(Collectors.toList());

        }
        return BaseResponse.success(vueProMenu);
    }
}
