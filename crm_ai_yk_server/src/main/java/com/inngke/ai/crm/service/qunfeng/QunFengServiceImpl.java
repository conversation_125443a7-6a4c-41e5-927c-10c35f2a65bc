package com.inngke.ai.crm.service.qunfeng;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.inngke.ai.crm.core.CrmServiceConsts;
import com.inngke.ai.crm.core.config.AiGcConfig;
import com.inngke.ai.crm.db.crm.entity.*;
import com.inngke.ai.crm.db.crm.manager.*;
import com.inngke.ai.crm.dto.enums.AppConfigCodeEnum;
import com.inngke.ai.crm.dto.enums.CoinDispatchTypeEnum;
import com.inngke.ai.crm.dto.enums.CoinLogEventTypeEnum;
import com.inngke.ai.crm.dto.request.CrmUserLoginRequest;
import com.inngke.ai.crm.dto.request.UserInfoRequest;
import com.inngke.ai.crm.dto.response.CrmUserLoginDto;
import com.inngke.ai.crm.dto.response.UserInfoDto;
import com.inngke.ai.crm.service.CrmUserService;
import com.inngke.ai.crm.service.UserVipService;
import com.inngke.ai.crm.service.devops.UserService;
import com.inngke.ai.crm.service.qunfeng.init.Init;
import com.inngke.ai.crm.service.qunfeng.init.PcUserInit;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.SnowflakeHelper;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.Lock;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.JwtService;
import com.inngke.common.service.LockService;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.JsonUtil;
import com.inngke.ip.ai.qunfeng.api.QunFengMarketApi;
import com.inngke.ip.ai.qunfeng.api.QunFengOauthApi;
import com.inngke.ip.ai.qunfeng.dto.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class QunFengServiceImpl implements QunFengService {

    private static final Logger logger = LoggerFactory.getLogger(QunFengServiceImpl.class);
    private static final String QUN_FENG_CHECK_ORDER = CrmServiceConsts.CACHE_LOCK_KEY_PRE + "qunfeng_check_order:user:";

    @Autowired
    private PcUserInit pcUserInit;
    @Autowired
    private JwtService jwtService;
    @Autowired
    private AiGcConfig aiGcConfig;
    @Autowired
    private AppConfigManager appConfigManager;
    @Autowired
    private QunFengOauthApi qunFengOauthApi;
    @Autowired
    private QunFengMarketApi qunFengMarketApi;
    @Autowired
    private QunFengOrderManager qunFengOrderManager;
    @Autowired
    private QunFengProductManager qunFengProductManager;
    @Autowired
    private UserManager userManager;
    @Autowired
    private LockService lockService;
    @Autowired
    private UserVipService userVipService;
    @Autowired
    private UserService userService;
    @Autowired
    private StaffManager staffManager;
    @Autowired
    private UserVipManager userVipManager;
    @Autowired
    private CoinManager coinManager;
    @Autowired
    private CrmUserService crmUserService;

    @Override
    public BaseResponse<CrmUserLoginDto> initUser(CrmUserLoginRequest request) {
        QunFengUserDto qunFengUser = Optional.ofNullable(qunFengOauthApi.getUserInfo(request.getCode()))
                .map(QunFengBaseResponse::getData).orElseThrow(() -> new InngkeServiceException("获取用户信息失败"));
        logger.info("获取用户信息: " + JsonUtil.toJsonString(qunFengUser));

        asyncUserOrders(qunFengUser.getId(), request.getServerName());

        QunFengOrder qunFengOrder = qunFengOrderManager.getQunFengUserFirstOrder(qunFengUser.getId());
        logger.info("获取用户订单: " + JsonUtil.toJsonString(qunFengUser));

        if (Objects.isNull(qunFengOrder)) {
            logger.info("未获取到群峰用户订单");
            throw new InngkeServiceException("未获取到群峰用户订单");
        }

        QunFengProduct product = qunFengProductManager.getById(qunFengOrder.getProductId());
        logger.info("获取用户订单商品: " + JsonUtil.toJsonString(product));

        User user = pcUserInit.init(qunFengUser, qunFengOrder, product);

        JwtPayload jwtPayload = new JwtPayload();
        jwtPayload.setBid(aiGcConfig.getBid());
        jwtPayload.setCid(user.getId());
        jwtPayload.setExp(DateTimeUtils.getMilli(LocalDateTime.now().plusDays(1)) / 1000);
        jwtPayload.setAppId(4);
        String token = jwtService.gen(jwtPayload);

        CrmUserLoginDto crmUserLoginDto = new CrmUserLoginDto();
        crmUserLoginDto.setToken(token);
        return BaseResponse.success(crmUserLoginDto);
    }

    @Override
    public String getAppAccessToken(GetAppAccessTokenRequest appAccessTokenRequest) {
        if (Objects.isNull(appAccessTokenRequest)) {
            throw new InngkeServiceException("获取应用配置失败");
        }

        QunFengBaseResponse<AccessTokenDto> response = qunFengOauthApi.getAppAccessToken(appAccessTokenRequest);

        AccessTokenDto accessTokenDto = Optional.ofNullable(response).map(QunFengBaseResponse::getData).orElse(null);
        if (Objects.isNull(accessTokenDto)) {
            throw new InngkeServiceException("获取应用AccessToken失败:response " + JsonUtil.toJsonString(response));
        }

        return accessTokenDto.getAccessToken();
    }

    @Override
    public void asyncUserOrders(Long qunFengUserId, String serverName) {
        Long userId = Optional.ofNullable(userManager.getByQunFengUserId(qunFengUserId)).map(User::getId).orElse(null);

        GetAppAccessTokenRequest appAccessTokenRequest = getAppAccessTokenRequest(serverName);
        String orderFilter = "{\"use_uid\":" + qunFengUserId + "}";
        QunFengBaseResponse<OrderResponse> response = qunFengMarketApi.getActiveFunc(
                getAppAccessToken(appAccessTokenRequest),
                appAccessTokenRequest.getAppId(),
                orderFilter, 1, 100
        );

        List<OrderResponse.QunFengOrder> qunFengOrders = Optional.ofNullable(response)
                .map(QunFengBaseResponse::getData).map(OrderResponse::getOrderList).orElse(Lists.newArrayList());

        qunFengOrders = qunFengOrders.stream().filter(order -> Objects.nonNull(order.getPayTime())).collect(Collectors.toList());

        qunFengOrderManager.async(qunFengUserId, userId, qunFengOrders);
    }

    @Override
    public void checkUserOrders(User user, String serverName) {
        Long qunFengUserId = user.getQunFengUserId();
        if (Objects.isNull(qunFengUserId) || qunFengUserId <= 0L){
            return;
        }
        Lock lock = lockService.getLock(QUN_FENG_CHECK_ORDER + qunFengUserId, 60);
        if (Objects.isNull(lock)) {
            return;
        }
        try {
            Staff staff = staffManager.getByUserId(user.getId());
            if (Objects.isNull(staff)) {
                logger.info("「{}」用户,员工不存在", user.getId());
                return;
            }

            asyncUserOrders(qunFengUserId, serverName);

            List<QunFengOrder> qunFengOrders = qunFengOrderManager.getByQunFengUserId(qunFengUserId).stream()
                    .filter(order -> !order.getFinished()).collect(Collectors.toList());

            //过滤已完成的订单
            List<Long> qunFengProductIds = qunFengOrders.stream()
                    .map(QunFengOrder::getProductId).collect(Collectors.toList());

            //获取对应的群峰商品
            Map<Long, QunFengProduct> qunFengProductMap = qunFengProductManager.getByIds(qunFengProductIds).stream()
                    .collect(Collectors.toMap(QunFengProduct::getId, Function.identity()));

            qunFengOrders.forEach(qunFengOrder -> {
                QunFengProduct qunFengProduct = qunFengProductMap.get(qunFengOrder.getProductId());
                if (Objects.isNull(qunFengProduct) || Objects.isNull(qunFengProduct.getCoinProductId())) {
                    return;
                }

                logger.info("开始处理订单: {}", qunFengOrder.getId());
                handleQunFengOrder(user, staff, qunFengOrder, qunFengProduct);
            });
        } catch (Exception e) {
            logger.error("处理订单异常: {}", e.getMessage());
        } finally {
            lock.unlock();
        }
    }

    @Override
    public BaseResponse<UserInfoDto> checkUserOrders(JwtPayload jwtPayload,String serverName) {
        User user = userManager.getById(jwtPayload.getCid());

        checkUserOrders(user, serverName);

        UserInfoRequest request = new UserInfoRequest();
        request.setUserId(jwtPayload.getCid());

        return crmUserService.userInfo(request);
    }

    private void handleQunFengOrder(User user, Staff staff, QunFengOrder qunFengOrder, QunFengProduct qunFengProduct) {
        Integer periodType = Optional.ofNullable(userVipManager.getById(qunFengProduct.getCoinProductId())).map(UserVip::getPeriodType).orElse(3);
        LocalDateTime extTime = Init.getExtTime(periodType);

        Coin coin = new Coin();
        coin.setId(SnowflakeHelper.getId());
        coin.setUserId(user.getId());
        coin.setCoin(qunFengProduct.getCoin());
        coin.setDispatchId(SnowflakeHelper.getId());
        coin.setDispatchType(CoinDispatchTypeEnum.QUN_FENG.getCode());
        coin.setTotalCoin(coin.getCoin());
        coin.setExpireTime(extTime);
        coin.setCreateTime(LocalDateTime.now());

        CoinLog coinLog = new CoinLog();
        coinLog.setId(SnowflakeHelper.getId());
        coinLog.setUserId(user.getId());
        coinLog.setCoinId(coin.getId());
        coinLog.setCoin(coin.getTotalCoin());
        coinLog.setEventType(CoinLogEventTypeEnum.QUN_FENG.getCode());
        coinLog.setCreateTime(LocalDateTime.now());

        coinManager.manualAddCoin(Lists.newArrayList(coin), Lists.newArrayList(coinLog));
        qunFengOrderManager.update(Wrappers.<QunFengOrder>update().eq(QunFengOrder.ID,qunFengOrder.getId())
                .set(QunFengOrder.USER_ID,user.getId()).set(QunFengOrder.FINISHED, true));
        logger.info("订单{}分配积分成功", qunFengOrder.getId());
    }


    private GetAppAccessTokenRequest getAppAccessTokenRequest(String serverName) {
        String defaultConfig = AppConfigCodeEnum.QUN_FENG_APP_CONFIG.getCode();
        String serverNameConfig = defaultConfig + InngkeAppConst.DOT_STR + serverName;

        Map<String, String> configStrMap = appConfigManager.getValueByCodeList(defaultConfig, serverNameConfig);

        String configStr = Optional.ofNullable(configStrMap.get(serverNameConfig)).orElse(configStrMap.get(defaultConfig));
        GetAppAccessTokenRequest request = JsonUtil.jsonToObject(configStr, GetAppAccessTokenRequest.class);
        if (Objects.isNull(request)) {
            throw new InngkeServiceException("获取应用配置失败");
        }
        return request;
    }

}
