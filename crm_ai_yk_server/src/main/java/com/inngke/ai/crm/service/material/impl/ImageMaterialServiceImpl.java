package com.inngke.ai.crm.service.material.impl;

import com.inngke.ai.crm.db.crm.entity.ImageMaterial;
import com.inngke.ai.crm.db.crm.entity.VideoMaterial;
import com.inngke.ai.crm.db.crm.manager.MaterialManager;
import com.inngke.ai.crm.dto.request.material.ListMaterialRequest;
import com.inngke.ai.crm.dto.request.material.RotateMaterialRequest;
import com.inngke.ai.crm.service.material.MaterialService;
import com.inngke.common.dto.JwtPayload;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;


@Service
public class ImageMaterialServiceImpl extends MaterialServiceAbs implements MaterialService {
    @Autowired
    @Qualifier("imageMaterialManagerImpl")
    private MaterialManager<ImageMaterial> imageMaterialMaterialManager;

    @Override
    protected MaterialManager<ImageMaterial> getManager() {
        return imageMaterialMaterialManager;
    }
}
