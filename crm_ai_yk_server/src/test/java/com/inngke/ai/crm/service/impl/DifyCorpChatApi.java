package com.inngke.ai.crm.service.impl;

import com.inngke.ai.crm.dto.response.SaleTipsTextResponse;
import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.Query;

public interface DifyCorpChatApi {
    @Headers({"app-id: chat_bp_yk", "content-type: application/json"})
    @GET("/api/dify-corpchat/sale-tips-text?bid=1")
    Call<SaleTipsTextResponse> getSaleTipsText(@Query("roomId") String chatId, @Query("clientId") Long clientId);
}
