package com.inngke.ai.crm.dto.response;

import com.google.common.io.Resources;
import com.inngke.ai.crm.core.AiGenerateState;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.utils.JsonUtil;
import com.inngke.ip.ai.dify.dto.response.DifyResponse;
import org.junit.jupiter.api.Assertions;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.atomic.AtomicInteger;

public class AiSaleVerbalStateTest {
    private String[][][] TEST_CONTENT = new String[][][]{
            // chat1.txt
            new String[][]{
                    new String[]{" [[跟进策略]] \n"},
                    new String[]{" 对于处于了解和比较产品阶段的客户，您可以详细介绍我们的tata木门室内门系列以及案例详情，让他们更加了解我们的产品，并帮助客户做出决定。\n"},
                    new String[]{" 如果客户处于谈判阶段，您可以强调我们的产品特点，例如磁吸静音门、木套等等，并结合客户的需求提供一些推荐方案。 \n\n以上两种备选话术都要亲和力十足，并引导客户完成下一步行动。请注意不要编造信息，如果客户对于我们的产品特点有疑问或需要更多的信息，您可以详细介绍我们的特点和优势。"}
            },

            // chat2.txt
            new String[][]{
                    new String[]{
                            " 根据客户的动态和历史聊天记录，您的客户对我们的产品有一定的了解和兴趣，我们需要转化这个兴趣为潜在的销售机会。\n",
                    },
                    new String[]{
                            " 您可以主动向客户介绍我们的tata木门室内门系列产品，并且提供详细的产品信息和特性。您可以强调我们产品的优点，比如防盗、静音、环保等，让客户充分了解我们的产品，以便于客户做出决策。\n",
                            " 您可以向客户介绍我们的一些成功案例，并且详细介绍我们的产品在案例中的使用效果和优点。这样可以让客户更加信任我们的产品，并且增加转化的机会。\n"
                    },
                    new String[]{
                            " 您还可以针对客户的需求推荐我们的zx-002-米白案例，这一个案例非常适合您客户的风格和需求，可以为客户提供更好的购买体验以及更加个性化的咨询服务。\n" +
                                    "\n",
                            " 您还可以主动提供我们专业的定制服务，听取客户的需求和想法，为客户打造一个独一无二的家居环境。同时，提供一些我们公司在客户所在城市的活动和促销策略，让客户有更多的选择和参考。"
                    }
            },

            // chat3.txt
            new String[][]{
                    new String[]{" [[跟进策略]] \n该客户已经添加了量尺和签单服务记录，说明对我们的产品比较感兴趣，我们可以通过以下方式进一步促进销售：\n1. 详细介绍tata木门室内门系列的产品特点和优势，以及与其他竞品的区别，增强客户对我们产品的信任感；\n2. 提供室内装修设计的服务，推荐适合该客户的门型、颜色和材质，让客户感到这是量身定做的；\n3. 为客户提供优惠活动，例如可在客户所在的广州市参加我们的促销活动，这将极大地增加客户的满意度字。\n\n"},
                    new String[]{" [[备选话术1]]\nlupin先生，您好，感谢您对我们的产品感兴趣！我们的tata木门室内门系列能够为您的家庭创造一个温馨舒适的环境。在该系列中，我们提供了多种不同的门型、颜色和材质供您选择，每一款门都经过严格的质检，以确保其品质和性能。与其他品牌相比，我们的产品优势在于靠谱、美观、性价比高。\n"},
                    new String[]{" [[备选话术2]]\nlupin先生，您的房子是100~150平方米的三居室，我建议您使用我们的tata木门室内门系列中的九宫格门型，这样可以给您的家带来更为精致的感觉。同时，我们的木门在材质上也非常优秀，使用选择的优质木材，即使经历了多年的使用，也不会变形或变色。如果您现在下单，还能享"}
            },

            // chat4.txt
            new String[][]{
                    new String[]{" 根据您的档案情况，您的销售阶段正处于即将成交。\n"},
                    new String[]{" 您可以向家人和朋友询问对我们公司的看法，了解其他客户的反馈。\n"},
                    new String[]{" 下单前请确认清楚所需的颜色、型号等细节信息，以确保订单准确无误。"}
            },
            new String[][]{
                    new String[]{""},
                    new String[]{""},
                    new String[]{""}
            }
    };

    public void testNewWords() throws Exception {
        for (int i = 1; i <= 4; i++) {
            test(i, "verbal/chat" + i + ".txt");
        }
//        int i = 2;
//        test(i, "verbal/chat" + i + ".txt");
    }

    private void test(int stage, String sourcePath) {
        AiGenerateState state = AiGenerateState.getBuilder(null)
                .addState(2, "customerState", InngkeAppConst.EMPTY_STR, 0, "IN:")
                .addState(1, "saleVerbal", InngkeAppConst.EMPTY_STR, 1, "R1:")
                .addState(1, "saleVerbal", InngkeAppConst.EMPTY_STR, 1, "R2:")
                .build();

        AtomicInteger count = new AtomicInteger(0);
        try {
            Resources.readLines(Resources.getResource(sourcePath), StandardCharsets.UTF_8).forEach(line -> {
                int c = count.addAndGet(1);
                DifyResponse resp = JsonUtil.jsonToObject(line, DifyResponse.class);
                String answer = resp.getAnswer();
                state.newWords(answer);
            });
        } catch (IOException e) {
            e.printStackTrace();
        }
        state.finish();
        System.out.println("------- 测试文本: " + sourcePath);

        String[][] testExpects = TEST_CONTENT[stage - 1];

        for (int i = 0; i < testExpects.length; i++) {
            String[] expects = testExpects[i];
            for (int j = 0; j < expects.length; j++) {
                String exp = expects[j];
                String act = state.getContents(i).get(j).toString();
                Assertions.assertEquals("阶段:" + i + "，输出:" + j + "]，不匹配\n期望：" + exp + "\n实际：" + act, exp, act);
            }
        }
    }
}