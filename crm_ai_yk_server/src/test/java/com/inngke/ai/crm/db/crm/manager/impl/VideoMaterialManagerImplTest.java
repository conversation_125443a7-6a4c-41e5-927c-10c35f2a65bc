package com.inngke.ai.crm.db.crm.manager.impl;

import com.inngke.ai.crm.BaseJunitTest;
import com.inngke.ai.crm.db.crm.manager.VideoMaterialManager;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class VideoMaterialManagerImplTest extends BaseJunitTest {

    @Autowired
    private VideoMaterialManager videoMaterialManager;

    @Test
    public void materialUse() {
        videoMaterialManager.getReDetectionList(0L, 110L, 20);
    }

}