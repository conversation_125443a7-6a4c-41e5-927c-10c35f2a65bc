package com.inngke.ai.crm;

import com.google.common.io.Files;
import com.inngke.common.utils.JsonUtil;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.tts.v20190823.TtsClient;
import com.tencentcloudapi.tts.v20190823.models.TextToVoiceRequest;
import com.tencentcloudapi.tts.v20190823.models.TextToVoiceResponse;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

public class TextToVoiceTest {
    @Test
    public void test() {
        //https://github.com/TencentCloud/tencentcloud-sdk-java/blob/master/src/main/java/com/tencentcloudapi/tts/v20190823/models/TextToVoiceRequest.java
        //https://cloud.tencent.com/document/api/1073/37995
        Credential credential = new Credential(
                "AKIDxwtnifcWQG7znYGu0321xqgS5awBE3gE",
                "UnwvpfMq67uSlpeEZxBBwUotiIdkcIjv"
        );
        TtsClient ttsClient = new TtsClient(credential, "ap-shanghai");
        TextToVoiceRequest request = new TextToVoiceRequest();
        request.setText("这座现代住宅位于城市中心，拥有大窗户和金属框架的阳台。它由玻璃、金属和其他材料制成，创造了一个独特的外观，使建筑成为城市的地标性建筑之一。");
        request.setSessionId("123");
        request.setModelType(1L);
        request.setVolume(0f);
        request.setSpeed(0f);
        request.setProjectId(0L);
        request.setModelType(1L);
        request.setVoiceType(101032L);
        request.setPrimaryLanguage(1L);
        request.setSampleRate(8000L);
        request.setCodec("mp3");
        request.setEnableSubtitle(true);
        //情感
        request.setEmotionCategory("jieshuo");

        try {
            TextToVoiceResponse resp = ttsClient.TextToVoice(request);
            File dir = new File("./tmp");
            if (!dir.exists()) {
                dir.mkdirs();
            }
            File file = new File(dir, "test.mp3");
            Files.write(Base64.getDecoder().decode(resp.getAudio()), file);
            Files.write(JsonUtil.toJsonString(resp.getSubtitles()).getBytes(StandardCharsets.UTF_8), new File(dir, "test.json"));
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("Hello World!");
    }
}
