package com.inngke.ai.crm.dto.form;

import com.inngke.ai.crm.BaseJunitTest;
import com.inngke.common.utils.JsonUtil;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class CommonFormConfigTest{

    @Test
    public void testGetType() {
        String text = "[{" +
                "  \"type\": \"input\"," +
                "  \"label\": \"输入框\"" +
                "}, {" +
                "  \"type\": \"upload\"," +
                "  \"label\": \"上传框\"" +
                "},{" +
                "  \"type\": \"select-button\"," +
                "  \"label\": \"选择框\"" +
                "},{" +
                "  \"type\": \"textarea\"," +
                "  \"label\": \"多行文本框\"" +
                "}]";
        List<CommonFormConfig> commonFormConfigs = JsonUtil.jsonToList(text, CommonFormConfig.class);
        System.out.println(commonFormConfigs);

        System.out.println(JsonUtil.toJsonString(commonFormConfigs));
    }
}