package com.inngke.ai.crm.service.impl;

import com.inngke.ai.crm.BaseJunitTest;
import com.inngke.ip.ai.volc.dto.request.MusicSourceSeparatePayload;
import com.inngke.ip.ai.volc.service.VolcSamiService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.File;

public class VolcSamiServiceTest extends BaseJunitTest {
    @Autowired
    private VolcSamiService volcSamiService;

    @Test
    public void test() {
        File audioFile = new File("/Users/<USER>/Downloads/FireShot/222.MP3");
        File targetFile = new File("/Users/<USER>/Downloads/FireShot/a.mp3");
        MusicSourceSeparatePayload payload = new MusicSourceSeparatePayload();
        payload.setModel("2track_vocal");
        volcSamiService.musicSourceSeparate(payload, audioFile, targetFile);

//        payload.setModel("bs_4track_vocal");
//        targetFile = new File("/Users/<USER>/Downloads/FireShot/b.mp3");
//        volcSamiService.musicSourceSeparate(payload, audioFile, targetFile);
    }
}
