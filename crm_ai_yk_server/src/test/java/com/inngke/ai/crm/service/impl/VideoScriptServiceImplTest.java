package com.inngke.ai.crm.service.impl;

import com.google.common.collect.Maps;
import com.inngke.ai.crm.BaseJunitTest;
import com.inngke.ai.crm.dto.request.video.VideoCreateWithMaterialRequest;
import com.inngke.ai.crm.service.VideoScriptService;
import com.inngke.common.dto.JwtPayload;
import com.inngke.ip.ai.dify.app.VideoSubtitleKeywordApp;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

public class VideoScriptServiceImplTest extends BaseJunitTest {

    @Autowired
    private VideoScriptService videoScriptService;

    @Autowired
    private VideoSubtitleKeywordApp videoSubtitleKeywordApp;

    @Test
    public void testCreate() {
        JwtPayload jwtPayload = new JwtPayload();

        VideoCreateWithMaterialRequest request = new VideoCreateWithMaterialRequest();
        Map<String, Object> prompts = Maps.newHashMap();
        prompts.put("brandName", "鞋柜怎么设计更加合理");
        prompts.put("length", "7");
        prompts.put("digitalPersonSwitch", "-1");
        prompts.put("voiceSex", "1");
        prompts.put("appId", "10095");
        prompts.put("theme", "鞋柜避坑");
        prompts.put("effectLevel", "2");
        request.setPromptMap(prompts);
        videoScriptService.create(jwtPayload, request);
    }
}