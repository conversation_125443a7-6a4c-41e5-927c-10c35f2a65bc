package com.inngke.ai.crm.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.inngke.ai.crm.BaseJunitTest;
import com.inngke.ai.crm.db.crm.entity.JianyingResourceScene;
import com.inngke.ai.crm.db.crm.manager.JianyingResourceSceneManager;
import com.inngke.ai.dto.config.JyResourceSceneConfig;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.utils.JsonUtil;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public class JyResourceListReset extends BaseJunitTest {
    @Autowired
    private JianyingResourceSceneManager jianyingResourceSceneManager;

    @Test
    public void test() {
        Map<String, JianyingResourceScene> jySceneMap = jianyingResourceSceneManager.list().stream()
                .collect(Collectors.toMap(JianyingResourceScene::getKeywordType, Function.identity()));

        JianyingResourceScene listConfig = jySceneMap.get("list");
        if (listConfig == null) {
            throw new InngkeServiceException("未配置列表！");
        }
        List<JyResourceSceneConfig> listConfigs = getConfigs(listConfig);
        if (listConfigs == null) {
            throw new InngkeServiceException("[list]未配置！");
        }
        JyResourceSceneConfig listTextTemplateConfig = listConfigs.stream().filter(config -> config.getType().equals("text_template")).findFirst().orElse(null);
        if (listTextTemplateConfig == null) {
            throw new InngkeServiceException("[list]配置无 text_template！");
        }
        listTextTemplateConfig.setType("list");

        jySceneMap.forEach((tagName, tagConfig) -> {
            if (tagName.equals("list")) {
                return;
            }

            List<JyResourceSceneConfig> configs = getConfigs(tagConfig);
            if (configs == null) {
                System.out.println(tagName + "配置为空！");
                return;
            }
            if (configs.stream().anyMatch(config -> config.getType().equals("list"))) {
                //已经设置了列表
                return;
            }
            configs.add(listTextTemplateConfig);

            String json;
            try {
                json = JsonUtil.getObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(configs);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
            tagConfig.setResourceConfig(json);
            tagConfig.setUpdateTime(LocalDateTime.now());
            //写入数据库
            jianyingResourceSceneManager.updateById(tagConfig);
        });
    }

    private List<JyResourceSceneConfig> getConfigs(JianyingResourceScene tagConfig) {
        String resourceConfigStr = tagConfig.getResourceConfig();
        List<JyResourceSceneConfig> configs = JsonUtil.jsonToList(resourceConfigStr, JyResourceSceneConfig.class);
        if (configs == null || configs.isEmpty()) {
            System.out.println("[" + tagConfig.getKeywordType() + "]配置为空！");
            return null;
        }
        return configs;
    }
}
