package com.inngke.ai.crm.dto.response;

import com.google.common.io.Resources;
import com.inngke.ai.crm.core.AiGenerateState;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.utils.JsonUtil;
import com.inngke.ip.ai.dify.dto.response.DifyResponse;
import org.junit.jupiter.api.Assertions;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.atomic.AtomicInteger;

public class AiXiaohongshuStateTest {
    private static final String STR_TITLE = "title";
    private static final String STR_CONTENT = "content";
    private static final String STR_TAG = "tag";
    private String[][][] TEST_CONTENT = new String[][][]{
            // chat1.txt
            new String[][]{
                    new String[]{"【实木地热地板，打造你的舒适卧室\uD83D\uDECF️】\n" +
                            "\n"},
                    new String[]{"1. 亲们，你们知道吗？实木地热地板是卧室的地面材料首选哦！\uD83D\uDC4D它有着独特的纹理和质感，让你的房间瞬间升级！\uD83C\uDF1F\n" +
                            "2. 想象一下，每天回家躺在舒适的地板上，感受着自然、舒适的氛围，是不是超级放松呢？\uD83D\uDE09\n" +
                            "3. 实木地热地板还具有耐用性，能够承受日常使用中的磨损和压力，让你的房间始终保持美观大方！\uD83D\uDC4C\n" +
                            "4. 此外，它还具有良好的吸音效果，有助于减少噪音干扰，提高睡眠质量。\uD83C\uDF19\n" +
                            "5. 亲们，还在等什么呢？快来试试实木地热地板吧！让你的房间焕发新的生机！\uD83C\uDF89\n" +
                            "\n"},
                    new String[]{" #实木地热地板 #卧室装修 #舒适生活 #家居设计"}
            },

            // chat2.txt
            new String[][]{
                    new String[]{
                            "\uD83D\uDCAB实木地板打造温馨卧室，舒适与美观兼具！\uD83D\uDECF️\n" +
                                    "\n",
                    },
                    new String[]{
                            "\uD83C\uDF1F实木地板，让卧室焕发自然气息\uD83C\uDF33。它的纹理清晰细腻，与家具色彩相得益彰，营造出和谐的氛围\uD83C\uDFA8。在柔和的光线下，卧室显得神秘又迷人✨，让人心醉神迷\uD83E\uDD70。\n" +
                                    "\n" +
                                    "\uD83D\uDC4D实木地板不仅美观大方，还有助于缓解身心疲劳\uD83D\uDE34，提高睡眠质量\uD83D\uDE34。让你的卧室成为真正的温馨港湾\uD83C\uDFE0！\n" +
                                    "\n" +
                                    "快来评论区告诉我你的想法吧\uD83D\uDC47！喜欢的话记得点赞\uD83D\uDC4D和评论\uD83D\uDCAC哦！让我们一起分享美好生活吧！\uD83D\uDC95\n" +
                                    "\n"
                    },
                    new String[]{
                            " #实木地板 #卧室装修 #温馨家居 #舒适生活"
                    }
            },

            // chat3.txt
            new String[][]{
                    new String[]{"\n" +
                            "【实木地板\uD83C\uDF33】卧室的舒适与美观并存，你值得拥有！ \n"},
                    new String[]{"\n" +
                            "实木地板\uD83C\uDF33，是卧室的主要装饰，以自然、舒适和温暖的氛围为设计理念。在卧室中，地板的使用不仅带来了舒适感，还增加了空间的视觉吸引力。\uD83D\uDC4D\uD83D\uDC4D \n" +
                            "通过使用不同的颜色和纹理来创造独特的视觉效果，使房间更加美丽而富有活力。\uD83D\uDE0D\uD83D\uDE0D \n" +
                            "此外，实木地板的耐用性和耐久性也是它的优势之一，可以持续地保持其美观的外观并持久地维护。\uD83D\uDCAA\uD83D\uDCAA \n" +
                            "快来一起感受实木地板的魅力吧！\uD83D\uDC4F\uD83D\uDC4F \n"},
                    new String[]{" #实木地板 #卧室装饰 #舒适美观 #耐用耐久"}
            },

            // chat4.txt
            new String[][]{
                    new String[]{"\uD83C\uDF89实木地板：让卧室焕发自然美感\uD83C\uDF1F\n\n"},
                    new String[]{"\uD83D\uDCAB实木地板是卧室中的重要元素，它的存在让房间更加舒适和自然。\uD83C\uDF08在卧室的设计中，地板的选择非常重要，因为它不仅与整个房间的布局、氛围协调一致，而且能够提高舒适度和美感。\uD83C\uDFA8从视觉上来看，实木地板的纹理和颜色非常柔和，可以为空带来温暖的氛围感。\uD83C\uDF1E此外，它的质感也非常适合现代风格，使房间更具时尚感。\uD83D\uDD70️在功能上，实木地板可以用于地面装饰或作为铺地材料使用。\uD83D\uDCAA对于需要更耐用和保护性能的房间来说，选择强化木地板是一种不错的选择。\uD83D\uDC4C而对于那些喜欢柔软舒适的体验的人来说，选择实木地板则更为合适。\uD83D\uDC4D总体而言，实木地板是现代卧室设计中不可或缺的重要元素之一，它的柔和质感和自然美感为房间增添了一份独特的气息。\uD83C\uDF1F\n" +
                            "\n" +
                            "重点介绍产品：实木地板\n" +
                            "\n" +
                            "笔记要求：请用灵动、活泼、欢快的语气介绍\n" +
                            "\n"},
                    new String[]{" 卧室装修 实木地板 现代风格 家居美学"}
            },
            new String[][]{
                    new String[]{""},
                    new String[]{""},
                    new String[]{""}
            }
    };

    public void testNewWords() throws Exception {
//        for (int i = 1; i <= 4; i++) {
//            test(i, "xiaohongshu/" + i + ".txt");
//        }
        int i = 4;
        test(i, "xiaohongshu/" + i + ".txt");
    }

    private void test(int stage, String sourcePath) {
        AiGenerateState state = AiGenerateState.getBuilder(null)
                .addState(2, STR_TITLE, InngkeAppConst.EMPTY_STR, 0, "一，标题\n", "一，标题 ")
                .addState(2, STR_CONTENT, InngkeAppConst.EMPTY_STR, 0, "二，正文\n", "二，正文 ")
                .addState(2, STR_TAG, null, 0, "【标签】")
                .build();

        AtomicInteger count = new AtomicInteger(0);
        try {
            Resources.readLines(Resources.getResource(sourcePath), StandardCharsets.UTF_8).forEach(line -> {
                int c = count.addAndGet(1);
                DifyResponse resp = JsonUtil.jsonToObject(line, DifyResponse.class);
                String answer = resp.getAnswer();
                state.newWords(answer);
            });
        } catch (IOException e) {
            e.printStackTrace();
        }
        state.finish();
        System.out.println("------- 测试文本: " + sourcePath);

        String[][] testExpects = TEST_CONTENT[stage - 1];

        for (int i = 0; i < testExpects.length; i++) {
            String[] expects = testExpects[i];
            for (int j = 0; j < expects.length; j++) {
                String exp = expects[j];
                String act = state.getContents(i).get(j).toString();
                Assertions.assertEquals("阶段:" + i + "，输出:" + j + "]，不匹配\n期望：" + exp + "\n实际：" + act, exp, act);
            }
        }
    }
}