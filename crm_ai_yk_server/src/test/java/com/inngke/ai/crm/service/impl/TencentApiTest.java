package com.inngke.ai.crm.service.impl;

import com.alibaba.fastjson2.JSON;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.cvm.v20170312.CvmClient;
import com.tencentcloudapi.cvm.v20170312.models.DescribeInstancesRequest;
import com.tencentcloudapi.cvm.v20170312.models.DescribeInstancesResponse;
import com.tencentcloudapi.ivld.v20210903.IvldClient;
import com.tencentcloudapi.ivld.v20210903.models.*;
import org.junit.jupiter.api.Test;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Date 2024/4/17 14:06
 */
public class TencentApiTest {

    private static final Credential cred = new Credential(
            "AKIDUvmXBw1FKG0b5Si2Mo9czAFCDEHE8ziY",
            "nRyUyNiw8Vr4DB4wmS2iFhJvTvlty2B4"
    );


    @Test
    public void testConnect() throws TencentCloudSDKException {
        CvmClient client = new CvmClient(cred, "ap-shanghai");
        DescribeInstancesRequest req = new DescribeInstancesRequest();
        DescribeInstancesResponse resp = client.DescribeInstances(req);

        System.out.println(DescribeInstancesResponse.toJsonString(resp));

    }

    /**
     * 上传视频
     *
     * @throws TencentCloudSDKException
     */
    @Test
    public void testImportMedia() throws TencentCloudSDKException {
        ImportMediaRequest importMediaRequest = new ImportMediaRequest();
        importMediaRequest.setURL("https://v3-web.douyinvod.com/23e1db893246f4de2a0929f8de3b5b81/661f9129/video/tos/cn/tos-cn-ve-15/ooABwsob9lDqeCABBInSnh8XhZiygA4gi2eJRA/?a=6383&ch=26&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C3&cv=1&br=1374&bt=1374&cs=0&ds=6&ft=GZnU0RqeffPdXP~ka1jNvAq-antLjrK7w89.RkaTFxg_ejVhWL6&mime_type=video_mp4&qs=0&rc=OmY4aDlnaDk0OzZoOzdpOUBpM2o7ZWg6ZnVxcTMzNGkzM0BeLV9fX2MyXzQxNTQwLS5hYSNhZy0xcjQwZDFgLS1kLS9zcw%3D%3D&btag=e00008000&cquery=100w_100B_100x_100z_100a&dy_q=1713341195&feature_id=f0150a16a324336cda5d6dd0b69ed299&l=202404171606351914DEAD56751003C848");
        importMediaRequest.setWriteBackCosPath("https://cos-publish-1301920668.cos.ap-shanghai.myqcloud.com/video_parse/");
        importMediaRequest.setMediaType(2L);
        importMediaRequest.setName("testTest");
        importMediaRequest.setCallbackURL("http://ai.inngke.net/api/ai/user/login-test");
        IvldClient ivldClient = new IvldClient(cred, "ap-shanghai");
        ImportMediaResponse importMediaResponse = ivldClient.ImportMedia(importMediaRequest);
        System.out.println(importMediaResponse.getMediaId());
    }

    @Test
    public void testDescribeMedia() throws TencentCloudSDKException {
        DescribeMediaRequest describeMediaRequest = new DescribeMediaRequest();
        describeMediaRequest.setMediaId("media-z5QQYTsZ");
        IvldClient ivldClient = new IvldClient(cred, "ap-shanghai");
        DescribeMediaResponse describeMediaResponse = ivldClient.DescribeMedia(describeMediaRequest);
        MediaInfo mediaInfo = describeMediaResponse.getMediaInfo();
    }

    @Test
    public void testCreateTask() throws TencentCloudSDKException {
        CreateTaskRequest createTaskRequest = new CreateTaskRequest();
        createTaskRequest.setMediaId("media-z5QQYTsZ");
        createTaskRequest.setTaskName("123");
        MediaPreknownInfo mediaPreknownInfo = new MediaPreknownInfo();
        mediaPreknownInfo.setMediaType(2L);
        mediaPreknownInfo.setMediaLang(1L);
        mediaPreknownInfo.setMediaLabel(2L);
        createTaskRequest.setMediaPreknownInfo(mediaPreknownInfo);
        IvldClient ivldClient = new IvldClient(cred, "ap-shanghai");
        CreateTaskResponse createTaskResponse = ivldClient.CreateTask(createTaskRequest);
        System.out.println(createTaskResponse.getTaskId());
    }

    @Test
    public void testDescribeTask() throws TencentCloudSDKException {
        DescribeTaskRequest req = new DescribeTaskRequest();
        req.setTaskId("task-66wja5Ni");
        IvldClient ivldClient = new IvldClient(cred, "ap-shanghai");
        DescribeTaskResponse describeTaskResponse = ivldClient.DescribeTask(req);
    }

    @Test
    public void testDescribeTaskResult() throws TencentCloudSDKException {
        DescribeTaskDetailRequest req = new DescribeTaskDetailRequest();
        req.setTaskId("task-gsSFDNYH");
        IvldClient ivldClient = new IvldClient(cred, "ap-shanghai");
        DescribeTaskDetailResponse describeTaskDetailResponse = ivldClient.DescribeTaskDetail(req);
        System.out.println(JSON.toJSONString(describeTaskDetailResponse));
    }

    @Test
    public void testUrl() {
        String text = "4.15 reo:/ <EMAIL> 12/17 是时候开始找感觉了# 猫 # 我和我的猫 # 拳击 # 滚烫人生 # 这我的小猫快看啊  https://v.douyin.com/iYQbmJx1/ 复制此链接，打开Dou音搜索，直接观看视频！";
        String urlPattern = "((https?|ftp|gopher|telnet|file):((//)|(\\\\))+([\\w\\d:#@%/;$()~_?\\+-=\\\\\\.&]*))";
        Pattern pattern = Pattern.compile(urlPattern, Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(text);

        while (matcher.find()) {
            System.out.println(matcher.group());
        }
    }

}

