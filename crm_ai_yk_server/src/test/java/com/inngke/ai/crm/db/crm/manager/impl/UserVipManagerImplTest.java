package com.inngke.ai.crm.db.crm.manager.impl;

import com.google.common.collect.Lists;
import com.inngke.ai.crm.BaseJunitTest;
import com.inngke.ai.crm.db.crm.manager.UserVipManager;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class UserVipManagerImplTest extends BaseJunitTest {
    @Autowired
    private UserVipManager userVipManager;

    @Test
    public void testGetUserVipExpiredTimeMap() {
        userVipManager.getUserVipExpiredTimeMap(Lists.newArrayList(249968045814976375L));
    }
}