# mybatis-plus-generator的配置.

globalConfig:
  author: xu<PERSON><PERSON>
  open: false
  idType: AUTO
  enableCache: false
  activeRecord: false
  baseResultMap: true
  baseColumnList: true
  swagger2: false
  fileOverride: true
  mapperName: '%sDao'
  serviceName: '%sManager'
  serviceImplName: '%sManagerImpl'
dataSourceConfig:
  url: ******************************************************************************************************************************************************************************************************************
  driverName: com.mysql.jdbc.Driver
  username: yk_root
  password: M22YjT@zH1Yzdplk
packageConfig:
  parent: com.inngke.ai.crm
  entity: db.crm.entity
  service: db.crm.manager
  serviceImpl: db.crm.manager.impl
  mapper: db.crm.dao
  xml: db.crm.mapper
  pathInfo:
    entity_path: src/main/java/com/inngke/ai/crm/db/crm/entity
    service_path: src/main/java/com/inngke/ai/crm/db/crm/manager
    service_impl_path: src/main/java/com/inngke/ai/crm/db/crm/manager/impl
    mapper_path: src/main/java/com/inngke/ai/crm/db/crm/dao
strategyConfig:
  naming: underline_to_camel
  columnNaming: underline_to_camel
  entityLombokModel: true
  entityColumnConstant: true
  superMapperClass: com.baomidou.mybatisplus.core.mapper.BaseMapper
  superServiceClass: com.baomidou.mybatisplus.extension.service.IService
  superServiceImplClass: com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
#  tablePrefix:
#    - ***
  include:
#    - qywx_auth_corp
#    - tenant
#    - qywx_group_chat
#    - qywx_group_chat_member
#    - customer
#    - staff
#    - customer_follow
#    - customer
#    - staff
#    - gpt_app_conf
#    - gpt_app_dataset
#    - gpt_app_pre_prompt
#    - ai_generate_task
#    - user_document
#    - user_document_qa
#    - sd_segment_log
#    - organize
#    - user_vip
#    - user_quit_organize
#    - video_material
#    - video_material_group
#    - ai_generate_video_output
#    - content_moderation
#    - video_quality_audit
#    - video_structure
#    - dou_yin_release_log
#    - creation_task
#    - creation_task_release_log
#    - creation_staff_relation
#    - department
#    - product
#    - staff
#    - product
#    - point_goods
#    - point_goods_exchange
#    - media_import
#     - media_process
#     - ai_generate_task_io
#     - video_material_vector
#     - tts_config
#     - text_style_config
#     - video_word
#     - app_demo
#     - video_material_dir
#     - video_create_task
#     - video_material_dir
#     - video_material_draft
#    - material_search
#      - jianying_resource
#     - jianying_resource_scene
#     - category
#     - video_project_draft
#     - video_script
#     - publish_task
#     - publish_video
#     - text_font
#     - video_lut
#     - qun_feng_order
#     - qun_feng_product
#     - video_job
#     - mash_up_task
#     - video_oceanengine_diagnosis
#     - video_widget
     - video_mashup_script
