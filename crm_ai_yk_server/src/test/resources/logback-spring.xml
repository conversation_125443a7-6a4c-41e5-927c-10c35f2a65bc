<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="2 seconds">
    <property name="PATTERN" value="%-12(%d{yyyy-MM-dd HH:mm:ss.SSS}) |-%-5level [%thread] %c[%L] - %msg%n"/>

    <!-- 开发环境 -->
    <appender name="APP_LOG" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <Pattern>${PATTERN}</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <logger name="com.alibaba.cloud.dubbo.registry.DubboCloudRegistry" level="WARN" additivity="false"/>
    <logger name="com.alibaba.nacos.client.naming" level="WARN" additivity="false"/>
    <logger name="com.alibaba.nacos.client.config.impl.ClientWorker" level="WARN" additivity="false"/>
    <root level="INFO">
        <appender-ref ref="APP_LOG"/>
    </root>
</configuration>
