server:
  port: 16240  #HTTP服务端口号
  dubboPort: 16040 #Dubbo服务端口
spring:
  application:
    name: crm_ai_yk
  cloud:
    nacos:
      config:
        namespace: CONF-${ENV:TEST}
        server-addr: nacos:8848 # 注册中心地址，注意必须带端口号！
        file-extension: yaml
        shared-configs[0]:
          dataId: redis_conf.yaml
        shared-configs[1]:
          dataId: dubbo_conf.yaml
        shared-configs[2]:
          dataId: privatization_conf.yaml
        shared-configs[3]:
          dataId: common_conf.yaml
        shared-configs[4]:
          dataId: api_secret.yaml
          refresh: true
        shared-configs[5]:
          dataId: tdmq_conf.yaml
      discovery:
        server-addr: nacos:8848 # 注册中心地址，注意必须带端口号！
        group: ${ENV:TEST}

inngke:
  dify:
#    url: https://ai.inngk.com
    url: http://170.106.197.97/
  llm:
    imageToText:
      name: VisualGLM
      url: http://8.134.150.215/