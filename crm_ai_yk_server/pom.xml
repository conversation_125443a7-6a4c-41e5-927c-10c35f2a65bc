<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.inngke.ai</groupId>
    <artifactId>crm_ai_yk_server</artifactId>
    <version>${crm_ai_yk_server.version}</version>

    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <video_ai_yk_api.version>2.0.3-SNAPSHOT</video_ai_yk_api.version>
                <vector-db-sdk.version>2.0.2-SNAPSHOT</vector-db-sdk.version>
                <crm_ai_yk_server.version>2.0.0-SNAPSHOT</crm_ai_yk_server.version>
                <dify-api-sdk.version>2.0.1-SNAPSHOT</dify-api-sdk.version>
                <qunfeng-api-sdk.version>2.0.0-SNAPSHOT</qunfeng-api-sdk.version>
                <dify-api-sdk.version>2.0.1-SNAPSHOT</dify-api-sdk.version>
                <douyin-api-sdk.version>2.0.0-SNAPSHOT</douyin-api-sdk.version>
                <chanjing-api-sdk.version>2.0.0-SNAPSHOT</chanjing-api-sdk.version>
                <volc-api-sdk.version>2.0.0-SNAPSHOT</volc-api-sdk.version>
                <yk-common-wx-proxy.version>2.0.0-SNAPSHOT</yk-common-wx-proxy.version>
                <yk-common-es.version>1.0.0-SNAPSHOT</yk-common-es.version>
                <yk-common-cache.version>2.0.0-SNAPSHOT</yk-common-cache.version>
                <yk-common-app.version>2.0.0-SNAPSHOT</yk-common-app.version>
                <yk-common-dynamic-datasource.version>2.0.0-SNAPSHOT</yk-common-dynamic-datasource.version>
                <common_ip_yk_api.version>2.0.0-SNAPSHOT</common_ip_yk_api.version>
                <reach_ip_yk_api.version>2.0.0-SNAPSHOT</reach_ip_yk_api.version>
                <yk-common-mq-core.version>2.0.0-SNAPSHOT</yk-common-mq-core.version>
                <yk-common-mq-api.version>2.0.0-SNAPSHOT</yk-common-mq-api.version>
                <event_ip_yk_api.version>2.0.0-SNAPSHOT</event_ip_yk_api.version>
                <quick-bi-sdk.version>2.0.0-SNAPSHOT</quick-bi-sdk.version>

                <auth_ip_yk_api.version>2.0.0-SNAPSHOT</auth_ip_yk_api.version>
                <auth_ip_yk_rbac_api.version>2.0.0-SNAPSHOT</auth_ip_yk_rbac_api.version>
                <yk-common-attachment.version>1.0.0-SNAPSHOT</yk-common-attachment.version>
                <yk-common-app-brand.version>2.0.0-SNAPSHOT</yk-common-app-brand.version>
                <yk-common-api.version>2.0.0-SNAPSHOT</yk-common-api.version>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <crm_ai_yk_server.version>1.3.73</crm_ai_yk_server.version>
                <dify-api-sdk.version>1.0.16</dify-api-sdk.version>
                <video_ai_yk_api.version>1.0.59</video_ai_yk_api.version>
                <douyin-api-sdk.version>2.0.0-SNAPSHOT</douyin-api-sdk.version>
                <chanjing-api-sdk.version>2.0.0-SNAPSHOT</chanjing-api-sdk.version>
                <volc-api-sdk.version>1.0.1</volc-api-sdk.version>
                <qunfeng-api-sdk.version>2.0.0-SNAPSHOT</qunfeng-api-sdk.version>
                <vector-db-sdk.version>2.0.2-SNAPSHOT</vector-db-sdk.version>
                <yk-common-wx-proxy.version>3.0.10</yk-common-wx-proxy.version>
                <yk-common-es.version>3.0.0</yk-common-es.version>
                <yk-common-cache.version>1.0.3</yk-common-cache.version>
                <yk-common-app.version>3.1.14</yk-common-app.version>
                <yk-common-dynamic-datasource.version>1.1.8</yk-common-dynamic-datasource.version>
                <common_ip_yk_api.version>3.0.31</common_ip_yk_api.version>
                <reach_ip_yk_api.version>3.0.12</reach_ip_yk_api.version>
                <yk-common-mq-core.version>1.0.3</yk-common-mq-core.version>
                <yk-common-mq-api.version>1.0.3</yk-common-mq-api.version>
                <event_ip_yk_api.version>3.0.10</event_ip_yk_api.version>
                <auth_ip_yk_api.version>3.0.0</auth_ip_yk_api.version>
                <auth_ip_yk_rbac_api.version>3.0.23</auth_ip_yk_rbac_api.version>
                <quick-bi-sdk.version>2.0.0-SNAPSHOT</quick-bi-sdk.version>
                <yk-common-attachment.version>1.0.1</yk-common-attachment.version>
                <yk-common-app-brand.version>3.0.22</yk-common-app-brand.version>
                <yk-common-api.version>3.0.4</yk-common-api.version>
            </properties>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
        </profile>
    </profiles>
    <properties>
        <jdk.version>11</jdk.version>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>

        <spring-cloud-alibaba.version>2.2.6.RELEASE</spring-cloud-alibaba.version>
        <spring-boot.version>2.6.3</spring-boot.version>
        <cos_api.version>5.6.69</cos_api.version>
        <wechatpay-java.version>0.2.11</wechatpay-java.version>
        <zxing.version>3.5.1</zxing.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.inngke.ai</groupId>
            <artifactId>video_ai_yk_api</artifactId>
            <version>${video_ai_yk_api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.inngke.ai</groupId>
            <artifactId>vector-db-sdk</artifactId>
            <version>${vector-db-sdk.version}</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp-sse</artifactId>
            <version>3.14.9</version>
            <exclusions>
                <exclusion>
                    <groupId>com.squareup.okio</groupId>
                    <artifactId>okio</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.inngke.common</groupId>
            <artifactId>yk-common-api</artifactId>
            <version>${yk-common-api.version}</version>
        </dependency>

        <dependency>
            <groupId>com.inngke.common</groupId>
            <artifactId>yk-common-utils</artifactId>
            <version>3.0.9</version>
        </dependency>
        <dependency>
            <groupId>com.inngke.common</groupId>
            <artifactId>yk-common-cache</artifactId>
            <version>${yk-common-cache.version}</version>
        </dependency>

        <dependency>
            <groupId>com.inngke.common</groupId>
            <artifactId>yk-common-wx-proxy</artifactId>
            <version>${yk-common-wx-proxy.version}</version>
        </dependency>

        <dependency>
            <groupId>com.inngke.common</groupId>
            <artifactId>yk-common-es</artifactId>
            <version>${yk-common-es.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
            <version>3.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.github.ulisesbocchio</groupId>
            <artifactId>jasypt-spring-boot-starter</artifactId>
            <version>3.0.5</version>
        </dependency>

        <dependency>
            <groupId>com.inngke</groupId>
            <artifactId>quick-bi-sdk</artifactId>
            <version>${quick-bi-sdk.version}</version>
        </dependency>

        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
            <version>2.0.1.Final</version>
        </dependency>

        <dependency>
            <groupId>com.qcloud</groupId>
            <artifactId>cos_api</artifactId>
            <version>${cos_api.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.google.code.gson</groupId>
                    <artifactId>gson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-codec</groupId>
                    <artifactId>commons-codec</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.squareup.okio</groupId>
                    <artifactId>okio</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.inngke.common</groupId>
            <artifactId>yk-common-app</artifactId>
            <version>${yk-common-app.version}</version>
        </dependency>

        <dependency>
            <groupId>com.inngke.ip</groupId>
            <artifactId>dify-api-sdk</artifactId>
            <version>${dify-api-sdk.version}</version>
        </dependency>
        <dependency>
            <groupId>com.inngke.ip</groupId>
            <artifactId>douyin-api-sdk</artifactId>
            <version>${douyin-api-sdk.version}</version>
        </dependency>
        <dependency>
            <groupId>com.inngke.ip</groupId>
            <artifactId>chanjing-api-sdk</artifactId>
            <version>${chanjing-api-sdk.version}</version>
        </dependency>

        <dependency>
            <groupId>com.inngke.ip</groupId>
            <artifactId>volc-api-sdk</artifactId>
            <version>${volc-api-sdk.version}</version>
        </dependency>

        <dependency>
            <groupId>com.inngke.ip</groupId>
            <artifactId>qunfeng-api-sdk</artifactId>
            <version>${qunfeng-api-sdk.version}</version>
        </dependency>

        <dependency>
            <groupId>com.inngke.common</groupId>
            <artifactId>yk-common-dynamic-datasource</artifactId>
            <version>${yk-common-dynamic-datasource.version}</version>
        </dependency>

        <dependency>
            <groupId>com.inngke.ip</groupId>
            <artifactId>common_ip_yk_api</artifactId>
            <version>${common_ip_yk_api.version}</version>
        </dependency>

        <dependency>
            <groupId>com.inngke.ip</groupId>
            <artifactId>reach_ip_yk_api</artifactId>
            <version>${reach_ip_yk_api.version}</version>
        </dependency>

        <dependency>
            <groupId>com.inngke.common</groupId>
            <artifactId>yk-common-mq-core</artifactId>
            <version>${yk-common-mq-core.version}</version>
        </dependency>

        <dependency>
            <groupId>com.inngke.ip</groupId>
            <artifactId>event_ip_yk_api</artifactId>
            <version>${event_ip_yk_api.version}</version>
        </dependency>

        <dependency>
            <groupId>com.github.wechatpay-apiv3</groupId>
            <artifactId>wechatpay-java</artifactId>
            <version>${wechatpay-java.version}</version>
        </dependency>

        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java</artifactId>
            <version>3.1.792</version>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>ecs20140526</artifactId>
            <version>3.1.12</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-codec</groupId>
                    <artifactId>commons-codec</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--  V1.0 SDK  -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
            <version>4.6.3</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-ecs</artifactId>
            <version>4.24.59</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>alimt20181012</artifactId>
            <version>1.2.0</version>
        </dependency>

        <dependency>
            <groupId>com.inngke.ip</groupId>
            <artifactId>auth_ip_yk_api</artifactId>
            <version>${auth_ip_yk_api.version}</version>
        </dependency>

        <dependency>
            <groupId>com.inngke.ip</groupId>
            <artifactId>auth_ip_yk_rbac_api</artifactId>
            <version>${auth_ip_yk_rbac_api.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
            <version>5.7.19</version>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>4.1.2</version>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-poi</artifactId>
            <version>5.7.19</version>
        </dependency>

        <dependency>
            <groupId>com.inngke.common</groupId>
            <artifactId>yk-common-attachment</artifactId>
            <version>${yk-common-attachment.version}</version>
        </dependency>

        <dependency>
            <groupId>com.inngke.common</groupId>
            <artifactId>yk-common-app-brand</artifactId>
            <version>${yk-common-app-brand.version}</version>
        </dependency>

        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>${zxing.version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
            <version>${zxing.version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>28.0-jre</version>
        </dependency>

        <!-- 测试依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <version>${spring-boot.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- Mybatis-Plus的代码生成插件 -->
            <!-- 执行命令： mvn mybatis-plus-generator:generator -->
            <plugin>
                <groupId>com.github.ciweigg</groupId>
                <artifactId>mybatis-plus-generator-maven-plugin</artifactId>
                <version>1.0.1-RELEASE</version>
                <configuration>
                    <configurationFile>${basedir}/src/test/resources/mybatis-generator-crm_ai_yk.yml</configurationFile>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${jdk.version}</source>
                    <target>${jdk.version}</target>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.8.2</version>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.0.2</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>inngke-java-maven</id>
            <name>maven</name>
            <url>https://inngke-maven.pkg.coding.net/repository/java/maven/</url>
        </repository>
    </distributionManagement>
</project>