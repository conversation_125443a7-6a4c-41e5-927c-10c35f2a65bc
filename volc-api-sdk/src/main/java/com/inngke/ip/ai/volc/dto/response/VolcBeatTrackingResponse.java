package com.inngke.ip.ai.volc.dto.response;

import lombok.Data;

/**
 * 火山云节拍检测响应
 */
@Data
public class VolcBeatTrackingResponse {
    
    /**
     * 状态码
     */
    private Integer statusCode;
    
    /**
     * 状态文本
     */
    private String statusText;
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 命名空间
     */
    private String namespace;
    
    /**
     * 响应数据，JSON格式的字符串
     */
    private String payload;
    
    /**
     * 二进制数据
     */
    private byte[] data;
    
    /**
     * 状态
     */
    private String state;
    
    /**
     * 检查响应是否成功
     * 
     * @return 是否成功
     */
    public boolean isSuccess() {
        return statusCode != null && statusCode == 20000;
    }
}
