package com.inngke.ip.ai.volc.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;

public class RefreshTokenRequest implements Serializable {
    @JsonProperty("app_id")
    private Long appId;
    
    @JsonProperty("secret")
    private String secret;
    
    @JsonProperty("grant_type")
    private String grantType;
    
    @JsonProperty("refresh_token")
    private String refreshToken;

    public Long getAppId() {
        return appId;
    }

    public RefreshTokenRequest setAppId(Long appId) {
        this.appId = appId;
        return this;
    }

    public String getSecret() {
        return secret;
    }

    public RefreshTokenRequest setSecret(String secret) {
        this.secret = secret;
        return this;
    }

    public String getGrantType() {
        return grantType;
    }

    public RefreshTokenRequest setGrantType(String grantType) {
        this.grantType = grantType;
        return this;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public RefreshTokenRequest setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
        return this;
    }
}
