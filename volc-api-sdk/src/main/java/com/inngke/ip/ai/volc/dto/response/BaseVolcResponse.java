package com.inngke.ip.ai.volc.dto.response;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

public class BaseVolcResponse<T extends Serializable> implements Serializable {
    /**
     * 请求 ID
     * 请求 ID,与传入的参数中 reqid 一致
     */
    @JsonProperty("reqid")
    @JsonAlias("request_id")
    private String reqId;

    /**
     * 请求状态码
     * 错误码详见：https://www.volcengine.com/docs/6561/79823
     */
    private Integer code;

    /**
     * 请求状态信息
     * 错误信息
     */
    private String message;

    /**
     * 数据
     */
    private T data;

    public String getReqId() {
        return reqId;
    }

    public void setReqId(String reqId) {
        this.reqId = reqId;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public BaseVolcResponse<T> setData(T data) {
        this.data = data;
        return this;
    }
}
