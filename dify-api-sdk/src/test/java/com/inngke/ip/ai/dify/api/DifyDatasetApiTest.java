package com.inngke.ip.ai.dify.api;

import com.inngke.ip.ai.dify.dto.request.DifyDatasetCreateByTextRequest;
import com.inngke.ip.ai.dify.dto.request.DifyDatasetProcessRule;
import com.inngke.ip.ai.dify.dto.request.DifyDatasetRule;
import com.inngke.ip.ai.dify.dto.request.DifyDatasetSettingDto;
import com.inngke.ip.ai.dify.dto.response.DifyDatasetAddDocumentByFileResp;
import com.inngke.ip.ai.dify.dto.response.DifyDatasetAddDocumentByTextResp;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import org.junit.jupiter.api.Test;
import retrofit2.Response;

import java.io.File;
import java.nio.charset.StandardCharsets;

class DifyDatasetApiTest extends BaseDifyApiTest {

    //    public static final String DATASET_API_KEY = "Bearer dataset-8wPpCaaFhnXbfO5gStkHDVGB";

    //    public static final String DATASET_ID = "578afd70-2720-49b2-8a28-abb73bb167fe";
    public static final String DATASET_ID = "ceadaad3-d9ad-4326-a865-efa8f1db5733";
    private File file = new File("/Users/<USER>/Downloads/C/249675081800744987.txt");

    @Test
    void createByFile() throws Exception {
        DifyDatasetRule rule = new DifyDatasetRule();
        DifyDatasetProcessRule processRule = new DifyDatasetProcessRule();
        processRule.setRules(null);
        processRule.setMode("automatic");

        DifyDatasetSettingDto setting = new DifyDatasetSettingDto();
//        setting.setName("");
        setting.setIndexingTechnique("high_quality");
        setting.setProcessRule(processRule);
        String json = objectMapper.writeValueAsString(setting);

        RequestBody body = new MultipartBody.Builder().setType(MultipartBody.FORM)
                .addFormDataPart(
                        "data",
                        null,
                        RequestBody.create(MediaType.parse("application/json"), json.getBytes(StandardCharsets.UTF_8))
                )
                .addFormDataPart(
                        "file",
                        file.getName(),
                        RequestBody.create(MediaType.parse("application/octet-stream"), file)
                )
                .build();

        Response<DifyDatasetAddDocumentByFileResp> resp = difyDatasetApi.createByFile(
                DATASET_API_KEY,
                DATASET_ID,
                body
        ).execute();
        System.out.println(resp);
        System.out.println(resp.body());
    }

    @Test
    public void createByText() throws Exception {
        DifyDatasetCreateByTextRequest body = new DifyDatasetCreateByTextRequest();
        body.setName("249675081800744988");
        body.setText("卧室里，一张白色的床、一面衣柜和一扇窗户。房间里充满了光线，透过窗户洒落进来，让房间更加明亮舒适。床上铺着柔软的床单，墙上挂着一幅画作，让人感受到一种温馨的氛围。");
        body.setIndexingTechnique("high_quality");
        DifyDatasetProcessRule rule = new DifyDatasetProcessRule();
        rule.setMode("automatic");
        body.setProcessRule(rule);

        Response<DifyDatasetAddDocumentByTextResp> resp = difyDatasetApi.createByText(
                DATASET_API_KEY,
                DATASET_ID,
                body
        ).execute();
        if (resp.isSuccessful()) {
            System.out.println(toJsonString(resp.body()));
        } else {
            System.out.println(resp);
        }
    }
}