package com.inngke.quickbi.config;

import com.aliyun.quickbi_public20220101.Client;
import com.aliyun.teaopenapi.models.Config;
import com.inngke.quickbi.exceptions.QuickBiApiException;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnBean({QuickBiProperties.class, AliCloudProperties.class})
public class QuickBiClientConfigure {
    @Bean
    public Client quickBiClient(QuickBiProperties quickBiProperties, AliCloudProperties aliCloudProperties) {
        Config config = new Config()
                .setAccessKeyId(aliCloudProperties.getAccessKey())
                .setAccessKeySecret(aliCloudProperties.getAccessKeySecret())
                .setEndpoint(quickBiProperties.getEndpoint());
        try {
            return new Client(config);
        } catch (Exception e) {
            throw new QuickBiApiException("初始化quickbi client失败", e);
        }
    }
}
